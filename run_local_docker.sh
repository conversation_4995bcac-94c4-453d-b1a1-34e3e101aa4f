#!/bin/bash
# Run services locally with Docker Compose

echo "=== Running Compliance Services Locally with Docker ==="
echo ""

# First, we need to ensure we have the mod files
if [ ! -f "steampipe-mod-gcp-compliance/mod.pp" ]; then
    echo "⚠️  Mod files missing. The repository is missing the actual Steampipe mod files."
    echo "   These need to be added to the repository for benchmarks to work."
    exit 1
fi

# Build base image first
echo "1. Building base image..."
docker build -f Base_image_steampipe/Dockerfile -t steampipe-base:latest .

# Run with docker-compose
echo ""
echo "2. Starting services with Docker Compose..."
docker-compose up --build gcp-compliance

# To test:
echo ""
echo "Service is running at http://localhost:8080"
echo ""
echo "Test commands:"
echo "  curl http://localhost:8080/health"
echo "  curl -X POST http://localhost:8080/api/gcp/run-benchmark -H 'Content-Type: application/json' -d '{\"benchmark\":\"cis_v300\",\"project_id\":\"YOUR_PROJECT_ID\"}'"