"""
Base authentication module for all cloud providers.
Provides common functionality for authentication across AWS, Azure, and GCP.
"""

from flask import Blueprint
from google.auth import default
from google.cloud import secretmanager
import os
import json
import logging
import traceback
from abc import ABC, abstractmethod
from typing import Tuple, Optional, Any, Dict

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Common paths
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
AUTHENTICATION_FOLDER = os.path.join('/tmp', 'authentication')


class CloudAuthBase(ABC):
    """Abstract base class for cloud authentication."""
    
    def __init__(self, cloud_provider: str):
        """
        Initialize the authentication base class.
        
        Args:
            cloud_provider: Name of the cloud provider (aws, azure, gcp)
        """
        self.cloud_provider = cloud_provider
        self.auth_type = os.getenv('AUTH_TYPE', 'sa').lower()
        self.customer_id = os.getenv('CUSTOMER_ID')
        self.project_id = os.getenv('PROJECT_ID')
        
    def get_service_credentials(self, secret_reference: Optional[str] = None) -> Tuple[Optional[Any], Optional[str]]:
        """
        Main entry point for getting cloud credentials.
        Returns credentials object and account/project identifier.
        
        Args:
            secret_reference: Optional secret reference name to retrieve from Secret Manager
        """
        try:
            logger.info(f"Using authentication type: {self.auth_type} for {self.cloud_provider}")
            
            if self.auth_type == 'adc':
                return self._get_credentials_from_adc()
            elif self.auth_type == 'sa' or secret_reference:
                return self._get_credentials_from_secret_manager(secret_reference)
            else:
                # For cloud providers that support local files (like AWS)
                if hasattr(self, '_get_credentials_from_file'):
                    return self._get_credentials_from_file()
                else:
                    error_msg = f"Unsupported authentication type: {self.auth_type}"
                    logger.error(error_msg)
                    return None, error_msg
                    
        except Exception as e:
            logger.error(f"Authentication error for {self.cloud_provider}: {str(e)}")
            logger.error(f"Detailed error: {traceback.format_exc()}")
            return None, f"Authentication error: {str(e)}"
    
    @abstractmethod
    def _get_credentials_from_adc(self) -> Tuple[Optional[Any], Optional[str]]:
        """Get credentials using Application Default Credentials."""
        pass
    
    def _get_credentials_from_secret_manager(self, secret_reference: Optional[str] = None) -> Tuple[Optional[Any], Optional[str]]:
        """Get credentials from Google Secret Manager."""
        try:
            # If secret_reference is provided, use it directly
            if secret_reference:
                secret_id = secret_reference
                logger.info(f"Using provided secret reference: {secret_id}")
            else:
                # Legacy behavior - construct from customer_id (will be deprecated)
                if not self.customer_id:
                    error_msg = "No secret reference provided and CUSTOMER_ID environment variable is not set"
                    logger.error(error_msg)
                    return None, error_msg
                secret_id = f"sa-{self.cloud_provider}-{self.customer_id}-secops".lower().replace('_', '-')
                
            if not self.project_id:
                error_msg = "PROJECT_ID environment variable is not set"
                logger.error(error_msg)
                return None, error_msg
            
            # Initialize Secret Manager client
            credentials, _ = default()
            secret_client = secretmanager.SecretManagerServiceClient(credentials=credentials)
            
            # Construct secret path
            secret_name = f"projects/{self.project_id}/secrets/{secret_id}/versions/latest"
            
            logger.info(f"Retrieving {self.cloud_provider.upper()} credentials from GCP Secret Manager: {secret_id}")
            
            # Access the secret
            try:
                response = secret_client.access_secret_version(request={"name": secret_name})
                secret_content = response.payload.data.decode("UTF-8")
            except Exception as e:
                logger.error(f"Failed to access secret: {str(e)}")
                logger.error(f"Secret path attempted: {secret_name}")
                return None, f"Failed to access secret: {str(e)}"
            
            # Create authentication folder if it doesn't exist
            try:
                os.makedirs(AUTHENTICATION_FOLDER, mode=0o755, exist_ok=True)
            except Exception as e:
                logger.error(f"Failed to create authentication folder: {str(e)}")
                return None, f"Failed to create authentication folder: {str(e)}"
            
            # Process the secret content based on cloud provider
            return self._process_secret_content(secret_content)
            
        except Exception as e:
            logger.error(f"Error retrieving {self.cloud_provider} credentials from GCP Secret: {str(e)}")
            logger.error(f"Detailed error: {traceback.format_exc()}")
            return None, f"Error retrieving credentials: {str(e)}"
    
    @abstractmethod
    def _process_secret_content(self, secret_content: str) -> Tuple[Optional[Any], Optional[str]]:
        """Process the secret content and return appropriate credentials."""
        pass
    
    def _parse_json_secret(self, secret_content: str) -> Optional[Dict]:
        """Common method to parse JSON secrets."""
        try:
            return json.loads(secret_content)
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in secret: {str(e)}")
            return None
    
    def _save_credentials_to_file(self, content: str, filename: str) -> Optional[str]:
        """Save credentials to a file in the authentication folder."""
        file_path = os.path.join(AUTHENTICATION_FOLDER, filename)
        try:
            with open(file_path, 'w') as f:
                f.write(content)
            logger.info(f"Saved {self.cloud_provider} credentials to: {file_path}")
            return file_path
        except Exception as e:
            logger.error(f"Failed to write credentials file: {str(e)}")
            return None
    
    def _clear_environment_variables(self, env_vars: list):
        """Clear specified environment variables."""
        for env_var in env_vars:
            if env_var in os.environ:
                del os.environ[env_var]
                logger.debug(f"Cleared environment variable: {env_var}")


class SecretManagerHelper:
    """Helper class for common Secret Manager operations."""
    
    @staticmethod
    def get_secret_content(project_id: str, secret_id: str) -> Optional[str]:
        """
        Retrieve secret content from Google Secret Manager.
        
        Args:
            project_id: GCP project ID
            secret_id: Secret ID in Secret Manager
            
        Returns:
            Secret content as string or None if error
        """
        try:
            credentials, _ = default()
            secret_client = secretmanager.SecretManagerServiceClient(credentials=credentials)
            
            name = f"projects/{project_id}/secrets/{secret_id}/versions/latest"
            response = secret_client.access_secret_version(request={"name": name})
            return response.payload.data.decode("UTF-8")
        except Exception as e:
            logger.error(f"Failed to get secret {secret_id}: {str(e)}")
            return None
    
    @staticmethod
    def construct_secret_id(cloud_provider: str, customer_id: str) -> str:
        """
        Construct standardized secret ID.
        
        Args:
            cloud_provider: Cloud provider name (aws, azure, gcp)
            customer_id: Customer identifier
            
        Returns:
            Formatted secret ID
        """
        return f"sa-{cloud_provider}-{customer_id}-secops".lower().replace('_', '-')


def create_auth_blueprint(name: str) -> Blueprint:
    """Create a Flask blueprint for authentication routes."""
    return Blueprint(name, __name__)