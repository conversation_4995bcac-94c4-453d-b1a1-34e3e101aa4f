"""
Authentication factory module that provides a unified interface for all cloud providers.
"""

from typing import <PERSON><PERSON>, Optional, Any
from .auth_aws import AWSAuth
from .auth_azure import AzureAuth
from .auth_gcp import GCPAuth
from .auth_base import create_auth_blueprint, logger


class CloudAuthFactory:
    """Factory class for creating cloud-specific authentication instances."""
    
    _auth_classes = {
        'aws': AWSAuth,
        'azure': AzureAuth,
        'gcp': GCPAuth
    }
    
    @classmethod
    def create_auth(cls, cloud_provider: str):
        """
        Create an authentication instance for the specified cloud provider.
        
        Args:
            cloud_provider: Cloud provider name (aws, azure, gcp)
            
        Returns:
            Authentication instance for the specified cloud provider
            
        Raises:
            ValueError: If cloud provider is not supported
        """
        cloud_provider = cloud_provider.lower()
        
        if cloud_provider not in cls._auth_classes:
            raise ValueError(f"Unsupported cloud provider: {cloud_provider}. "
                           f"Supported providers: {', '.join(cls._auth_classes.keys())}")
        
        return cls._auth_classes[cloud_provider]()
    
    @classmethod
    def get_service_credentials(cls, cloud_provider: str, secret_reference: Optional[str] = None) -> Tuple[Optional[Any], Optional[str]]:
        """
        Get service credentials for the specified cloud provider.
        
        Args:
            cloud_provider: Cloud provider name (aws, azure, gcp)
            secret_reference: Optional secret reference name to retrieve from Secret Manager
            
        Returns:
            Tuple of (credentials, account/project identifier)
        """
        try:
            auth_instance = cls.create_auth(cloud_provider)
            return auth_instance.get_service_credentials(secret_reference)
        except Exception as e:
            logger.error(f"Failed to get credentials for {cloud_provider}: {str(e)}")
            return None, str(e)


# Convenience function for backward compatibility
def get_service_credentials(cloud_provider: str, secret_reference: Optional[str] = None) -> Tuple[Optional[Any], Optional[str]]:
    """
    Get service credentials for the specified cloud provider.
    
    This is a convenience function that maintains backward compatibility
    with existing code that imports get_service_credentials directly.
    
    Args:
        cloud_provider: Cloud provider name (aws, azure, gcp)
        secret_reference: Optional secret reference name to retrieve from Secret Manager
        
    Returns:
        Tuple of (credentials, account/project identifier)
    """
    return CloudAuthFactory.get_service_credentials(cloud_provider, secret_reference)


# Create auth blueprint for Flask applications
auth = create_auth_blueprint('auth')