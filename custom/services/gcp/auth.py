from flask import Blueprint, request, jsonify, abort
from googleapiclient.discovery import build
from google.auth import default
from google.oauth2 import service_account
from google.cloud import secretmanager
from functools import wraps
import os
import sys
import json
from google.auth.credentials import Credentials
import logging
import subprocess
import traceback
sys.path.append('/app/shared')
try:
    from secret_manager import SecretManagerClient, validate_credentials
except ImportError:
    # Fallback for local development
    SecretManagerClient = None
    validate_credentials = None


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create a blueprint
auth = Blueprint('auth', __name__)

# Use a writable directory for authentication files
# Override any imported value to ensure we use /tmp
AUTHENTICATION_FOLDER = '/tmp/authentication'
SERVICE_ACCOUNT_FILE = os.path.join(AUTHENTICATION_FOLDER, 'service-account.json')

def get_service_credentials(secret_reference=None, sm_credentials=None):
    try:
        # Get authentication type from environment variables
        auth_type = os.getenv('AUTH_TYPE', 'sa').lower()  # Default to service account if not specified
        logger.info(f"Using authentication type: {auth_type}")

        # If secret_reference is provided, use it directly
        if secret_reference:
            logger.info(f"Using provided secret reference: {secret_reference}")
            return get_credentials_from_secret_reference(secret_reference, sm_credentials)
        elif auth_type == 'adc':
            try:
                logger.info("Attempting to use Application Default Credentials")
                credentials, project_id = default()
                logger.info(f"ADC Credentials path: {os.getenv('GOOGLE_APPLICATION_CREDENTIALS')}")
                logger.info(f"Credential type: {type(credentials).__name__}")
                logger.info(f"Valid credentials: {credentials.valid}")
                logger.info(f"Expiry: {credentials.expiry}")
                return credentials, project_id
            except Exception as e:
                logger.error(f"ADC authentication failed: {str(e)}")
                logger.error(f"Detailed error: {traceback.format_exc()}")
                return None, f"ADC authentication failed: {str(e)}"
        
        try:
            # Initialize Secret Manager client
            credentials, project_id = default()
            secret_client = secretmanager.SecretManagerServiceClient(credentials=credentials)
            
            # Get customer_id from environment
            customer_id = os.getenv('CUSTOMER_ID')
            
            project_id = os.getenv('PROJECT_ID')
            logger.error(f"project id of env: {project_id}")

            if not customer_id:
                error_msg = "No customer_id provided and CUSTOMER_ID environment variable is not set"
                logger.error(error_msg)
                return None, error_msg
            
            secret_id = f"sa-gcp-{customer_id}-secops".lower().replace('_', '-')
            name = f"projects/{project_id}/secrets/{secret_id}/versions/latest"

            try:
                response = secret_client.access_secret_version(request={"name": name})
                secret_content = response.payload.data.decode("UTF-8")
            except Exception as e:
                logger.error(f"Failed to access secret: {str(e)}")
                logger.error(f"Secret path attempted: {name}")
                logger.error(f"Detailed error: {traceback.format_exc()}")
                return None, f"Failed to access secret: {str(e)}"

            try:
                service_account_json = json.loads(secret_content)
            except json.JSONDecodeError as e:
                logger.error(f"Invalid JSON in secret content: {str(e)}")
                return None, f"Invalid JSON in secret content: {str(e)}"

            try:
                os.makedirs(AUTHENTICATION_FOLDER, mode=0o755, exist_ok=True)
            except Exception as e:
                logger.error(f"Failed to create authentication folder: {str(e)}")
                logger.error(f"Folder path: {AUTHENTICATION_FOLDER}")
                return None, f"Failed to create authentication folder: {str(e)}"
            
            file_path = os.path.join(AUTHENTICATION_FOLDER, 'service-account.json')
            print(f"File path: {file_path}")
            
            try:
                with open(file_path, 'w') as f:
                    json.dump(service_account_json, f, indent=2)
            except Exception as e:
                logger.error(f"Failed to write service account file: {str(e)}")
                logger.error(f"File path: {file_path}")
                return None, f"Failed to write service account file: {str(e)}"

            project_id = service_account_json.get('project_id')
            if not project_id:
                error_msg = "project_id not found in service account JSON"
                logger.error(error_msg)
                return None, error_msg

            try:
                credentials = service_account.Credentials.from_service_account_file(file_path)
                # os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = file_path
                
                # logger.info(f"Service account Credentials path: {os.getenv('GOOGLE_APPLICATION_CREDENTIALS')}")
                logger.info(f"Credential type: {type(credentials).__name__}")
                logger.info(f"Valid credentials: {credentials.valid}")
                logger.info(f"Expiry: {credentials.expiry}")
                
                # try:
                #     subprocess.run([
                #         "gcloud", "auth", "activate-service-account",
                #         "--key-file", file_path
                #     ], check=True, capture_output=True, text=True)
                # except subprocess.CalledProcessError as e:
                #     logger.error(f"Failed to activate service account: {str(e)}")
                #     logger.error(f"Command output: {e.output}")
                #     return None, f"Failed to activate service account: {str(e)}"

                return credentials, project_id

            except Exception as e:
                logger.error(f"Failed to create credentials from service account file: {str(e)}")
                logger.error(f"File path: {file_path}")
                logger.error(f"Detailed error: {traceback.format_exc()}")
                return None, f"Failed to create credentials from service account file: {str(e)}"

        except Exception as e:
            logger.error(f"Service account authentication failed: {str(e)}")
            logger.error(f"Detailed error: {traceback.format_exc()}")
            return None, f"Service account authentication failed: {str(e)}"
            
    except Exception as e:
        logger.error(f"Authentication error: {str(e)}")
        logger.error(f"Detailed error: {traceback.format_exc()}")
        return None, f"Authentication error: {str(e)}"

def get_credentials_from_secret_reference(secret_reference, sm_credentials=None):
    """Get GCP credentials from Secret Manager using secret reference"""
    try:
        # If SM credentials are provided, use them to access Secret Manager
        if sm_credentials:
            # Create credentials from provided JSON
            if isinstance(sm_credentials, dict):
                sm_creds = service_account.Credentials.from_service_account_info(sm_credentials)
                project_id = sm_credentials.get('project_id')
            else:
                logger.error("Invalid sm_credentials format - expected dict")
                return None, "Invalid sm_credentials format"
                
            secret_client = secretmanager.SecretManagerServiceClient(credentials=sm_creds)
        else:
            # Fall back to default credentials
            # Get project ID
            project_id = os.getenv('PROJECT_ID')
            if not project_id:
                _, project_id = default()
                
            # Initialize Secret Manager client
            credentials, _ = default()
            secret_client = secretmanager.SecretManagerServiceClient(credentials=credentials)
        
        # Construct secret path - use the project_id where the secret is stored
        # When using sm_credentials, the secret is in the same project as the SM credentials
        secret_name = f"projects/{project_id}/secrets/{secret_reference}/versions/latest"
        
        logger.info(f"Retrieving GCP credentials from Secret Manager: {secret_reference}")
        
        # Access the secret
        try:
            response = secret_client.access_secret_version(request={"name": secret_name})
            secret_content = response.payload.data.decode("UTF-8")
        except Exception as e:
            logger.error(f"Failed to access secret: {str(e)}")
            logger.error(f"Secret path attempted: {secret_name}")
            return None, f"Failed to access secret: {str(e)}"
            
        # Parse JSON credentials
        try:
            service_account_json = json.loads(secret_content)
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in secret content: {str(e)}")
            return None, f"Invalid JSON in secret content: {str(e)}"
            
        # Create authentication folder if it doesn't exist
        try:
            os.makedirs(AUTHENTICATION_FOLDER, mode=0o755, exist_ok=True)
        except Exception as e:
            logger.error(f"Failed to create authentication folder: {str(e)}")
            return None, f"Failed to create authentication folder: {str(e)}"
            
        # Write credentials to file - use standard name for Steampipe config
        file_path = os.path.join(AUTHENTICATION_FOLDER, 'service-account.json')
        
        try:
            with open(file_path, 'w') as f:
                json.dump(service_account_json, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to write service account file: {str(e)}")
            return None, f"Failed to write service account file: {str(e)}"
            
        # Extract project ID
        project_id = service_account_json.get('project_id')
        if not project_id:
            error_msg = "project_id not found in service account JSON"
            logger.error(error_msg)
            return None, error_msg
            
        # Create credentials object
        try:
            credentials = service_account.Credentials.from_service_account_file(file_path)
            logger.info(f"Successfully loaded GCP credentials from secret: {secret_reference}, project: {project_id}")
            return credentials, project_id
        except Exception as e:
            logger.error(f"Failed to create credentials from service account file: {str(e)}")
            return None, f"Failed to create credentials from service account file: {str(e)}"
            
    except Exception as e:
        logger.error(f"Error retrieving credentials from secret reference: {str(e)}")
        logger.error(f"Detailed error: {traceback.format_exc()}")
        return None, f"Error retrieving credentials: {str(e)}"

def get_customer_credentials_from_secret(customer_id):
    """Get customer-specific GCP credentials from Secret Manager"""
    try:
        # Ensure we use the correct authentication folder
        global AUTHENTICATION_FOLDER
        AUTHENTICATION_FOLDER = '/tmp/authentication'
        
        # Get project ID
        project_id = os.getenv('GCP_PROJECT_ID')
        if not project_id:
            credentials, project_id = default()
        
        # Initialize Secret Manager client
        sm_client = SecretManagerClient(project_id=project_id)
        
        # Get customer credentials
        creds, error = sm_client.get_customer_credentials(customer_id, 'gcp')
        if error:
            logger.error(f"Error getting customer credentials: {error}")
            return None, error
        
        # Validate credentials
        if validate_credentials:
            valid, validation_error = validate_credentials('gcp', creds)
            if not valid:
                logger.error(f"Invalid credentials: {validation_error}")
                return None, validation_error
        
        # Write credentials to a temporary file for GCP SDK
        try:
            os.makedirs(AUTHENTICATION_FOLDER, mode=0o755, exist_ok=True)
        except Exception as e:
            logger.error(f"Failed to create authentication folder: {str(e)}")
            return None, f"Failed to create authentication folder: {str(e)}"
        
        # Create a unique file name for this customer
        file_path = os.path.join(AUTHENTICATION_FOLDER, f'service-account-{customer_id}.json')
        
        try:
            with open(file_path, 'w') as f:
                json.dump(creds, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to write service account file: {str(e)}")
            return None, f"Failed to write service account file: {str(e)}"
        
        # Get project ID from credentials
        project_id = creds.get('project_id')
        if not project_id:
            logger.error("project_id not found in service account JSON")
            return None, "project_id not found in service account JSON"
        
        # Create credentials object
        try:
            credentials = service_account.Credentials.from_service_account_file(file_path)
            
            # Set environment variable for GCP SDK
            os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = file_path
            
            logger.info(f"Successfully loaded GCP credentials for customer {customer_id}, project: {project_id}")
            return credentials, project_id
            
        except Exception as e:
            logger.error(f"Failed to create credentials from service account file: {str(e)}")
            return None, f"Failed to create credentials from service account file: {str(e)}"
        
    except Exception as e:
        logger.error(f"Error with Secret Manager authentication: {str(e)}")
        logger.error(f"Detailed error: {traceback.format_exc()}")
        return None, f"Secret Manager authentication failed: {str(e)}"

