connection "gcp_demologinproject_443517" {
  plugin  = "gcp"
  project = "demologinproject-443517"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_gen_lang_client_**********" {
  plugin  = "gcp"
  project = "gen-lang-client-**********"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_pbmm_lz_setup_********" {
  plugin  = "gcp"
  project = "pbmm-lz-setup-********"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_aashish_test_pbmm" {
  plugin  = "gcp"
  project = "aashish-test-pbmm"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_ansh_test_prj_443118" {
  plugin  = "gcp"
  project = "ansh-test-prj-443118"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_egg_timer_83d78" {
  plugin  = "gcp"
  project = "egg-timer-83d78"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_newagent_fvne" {
  plugin  = "gcp"
  project = "newagent-fvne"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_kubeshot_admin" {
  plugin  = "gcp"
  project = "kubeshot-admin"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_kubeshot_autogpt" {
  plugin  = "gcp"
  project = "kubeshot-autogpt"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_kubeshot_devops" {
  plugin  = "gcp"
  project = "kubeshot-devops"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_kubeshot_rag" {
  plugin  = "gcp"
  project = "kubeshot-rag"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_opensas_production" {
  plugin  = "gcp"
  project = "opensas-production"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_test_ai_project_435023" {
  plugin  = "gcp"
  project = "test-ai-project-435023"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_c_scc_mdzj" {
  plugin  = "gcp"
  project = "prj-c-scc-mdzj"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_b_seed_94c1_2c37" {
  plugin  = "gcp"
  project = "prj-b-seed-94c1-2c37"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_b_cicd_local_ae40" {
  plugin  = "gcp"
  project = "prj-b-cicd-local-ae40"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_b_seed_7378_4989" {
  plugin  = "gcp"
  project = "prj-b-seed-7378-4989"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_c_billing_logs_gjmf" {
  plugin  = "gcp"
  project = "prj-c-billing-logs-gjmf"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_c_kms_7l3b" {
  plugin  = "gcp"
  project = "prj-c-kms-7l3b"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_c_logging_ufek" {
  plugin  = "gcp"
  project = "prj-c-logging-ufek"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_c_scc_xt2u" {
  plugin  = "gcp"
  project = "prj-c-scc-xt2u"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_c_secrets_rwht" {
  plugin  = "gcp"
  project = "prj-c-secrets-rwht"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_d_kms_j5lx" {
  plugin  = "gcp"
  project = "prj-d-kms-j5lx"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_d_monitoring_yxes" {
  plugin  = "gcp"
  project = "prj-d-monitoring-yxes"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_d_secrets_2h6s" {
  plugin  = "gcp"
  project = "prj-d-secrets-2h6s"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_i_kms_1445" {
  plugin  = "gcp"
  project = "prj-i-kms-1445"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_i_monitoring_7xd1" {
  plugin  = "gcp"
  project = "prj-i-monitoring-7xd1"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_i_secrets_fw0s" {
  plugin  = "gcp"
  project = "prj-i-secrets-fw0s"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_m_kms_0ae8" {
  plugin  = "gcp"
  project = "prj-m-kms-0ae8"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_m_monitoring_u6b3" {
  plugin  = "gcp"
  project = "prj-m-monitoring-u6b3"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_m_secrets_j7nj" {
  plugin  = "gcp"
  project = "prj-m-secrets-j7nj"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_d_shared_base_9wfd" {
  plugin  = "gcp"
  project = "prj-d-shared-base-9wfd"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_i_shared_base_bf06" {
  plugin  = "gcp"
  project = "prj-i-shared-base-bf06"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_m_shared_base_efu9" {
  plugin  = "gcp"
  project = "prj-m-shared-base-efu9"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_n_shared_base_9d2d" {
  plugin  = "gcp"
  project = "prj-n-shared-base-9d2d"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_net_dns_vkj6" {
  plugin  = "gcp"
  project = "prj-net-dns-vkj6"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_net_hub_base_lely" {
  plugin  = "gcp"
  project = "prj-net-hub-base-lely"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_net_interconnect_n4no" {
  plugin  = "gcp"
  project = "prj-net-interconnect-n4no"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_p_shared_base_cq0a" {
  plugin  = "gcp"
  project = "prj-p-shared-base-cq0a"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_n_kms_bm0l" {
  plugin  = "gcp"
  project = "prj-n-kms-bm0l"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_n_monitoring_yfut" {
  plugin  = "gcp"
  project = "prj-n-monitoring-yfut"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_n_secrets_odqg" {
  plugin  = "gcp"
  project = "prj-n-secrets-odqg"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_p_kms_2tax" {
  plugin  = "gcp"
  project = "prj-p-kms-2tax"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_p_monitoring_x9fj" {
  plugin  = "gcp"
  project = "prj-p-monitoring-x9fj"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_p_secrets_aql2" {
  plugin  = "gcp"
  project = "prj-p-secrets-aql2"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_aashish_test_prj" {
  plugin  = "gcp"
  project = "aashish-test-prj"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_ansh_test_prj" {
  plugin  = "gcp"
  project = "ansh-test-prj"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_vratant_test_prj" {
  plugin  = "gcp"
  project = "vratant-test-prj"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_bansari_test" {
  plugin  = "gcp"
  project = "bansari-test"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_bucket_rsync" {
  plugin  = "gcp"
  project = "bucket-rsync"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_neosec_ai_solutions" {
  plugin  = "gcp"
  project = "neosec-ai-solutions"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_test_harjot_prj" {
  plugin  = "gcp"
  project = "test-harjot-prj"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_shubhi_test_prj" {
  plugin  = "gcp"
  project = "shubhi-test-prj"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_backstage_412018" {
  plugin  = "gcp"
  project = "backstage-412018"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_scrapper_419215" {
  plugin  = "gcp"
  project = "scrapper-419215"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_ottpol_bootstrap_test" {
  plugin  = "gcp"
  project = "prj-ottpol-bootstrap-test"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_c_billing_psi" {
  plugin  = "gcp"
  project = "prj-c-billing-psi"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_c_logging_psi" {
  plugin  = "gcp"
  project = "prj-c-logging-psi"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_c_core_networking_np_psi" {
  plugin  = "gcp"
  project = "prj-c-core-networking-np-psi"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_c_host_networking_np_psi" {
  plugin  = "gcp"
  project = "prj-c-host-networking-np-psi"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_c_core_networking_p_psi" {
  plugin  = "gcp"
  project = "prj-c-core-networking-p-psi"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_c_host_networking_p_psi" {
  plugin  = "gcp"
  project = "prj-c-host-networking-p-psi"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_c_sec_sentinal_np_psi" {
  plugin  = "gcp"
  project = "prj-c-sec-sentinal-np-psi"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_c_sec_sentinal_p_psi" {
  plugin  = "gcp"
  project = "prj-c-sec-sentinal-p-psi"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_c_api_gateway_np_psi" {
  plugin  = "gcp"
  project = "prj-c-api-gateway-np-psi"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_c_observability_np_psi" {
  plugin  = "gcp"
  project = "prj-c-observability-np-psi"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_c_api_gateway_p_psi" {
  plugin  = "gcp"
  project = "prj-c-api-gateway-p-psi"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_c_observability_p_psi" {
  plugin  = "gcp"
  project = "prj-c-observability-p-psi"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_c_tools_np_psi" {
  plugin  = "gcp"
  project = "prj-c-tools-np-psi"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_c_tools_p_psi" {
  plugin  = "gcp"
  project = "prj-c-tools-p-psi"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_opol_core_networks" {
  plugin  = "gcp"
  project = "prj-opol-core-networks"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_foundation_psi" {
  plugin  = "gcp"
  project = "prj-foundation-psi"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_test_prj_for_psi_wif" {
  plugin  = "gcp"
  project = "test-prj-for-psi-wif"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_sys_10584336580509349754235258" {
  plugin  = "gcp"
  project = "sys-10584336580509349754235258"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_sys_16473596295331132258055880" {
  plugin  = "gcp"
  project = "sys-16473596295331132258055880"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_sys_16577995987289835251666163" {
  plugin  = "gcp"
  project = "sys-16577995987289835251666163"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_sys_18228428281390614574756768" {
  plugin  = "gcp"
  project = "sys-18228428281390614574756768"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_sys_25365120900523877734101376" {
  plugin  = "gcp"
  project = "sys-25365120900523877734101376"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_sys_47687987468820982859752416" {
  plugin  = "gcp"
  project = "sys-47687987468820982859752416"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_sys_49349059182320262405653747" {
  plugin  = "gcp"
  project = "sys-49349059182320262405653747"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_sys_51815433874226388266640321" {
  plugin  = "gcp"
  project = "sys-51815433874226388266640321"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_sys_57733743513041139174379021" {
  plugin  = "gcp"
  project = "sys-57733743513041139174379021"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_sys_77432550242619391028652600" {
  plugin  = "gcp"
  project = "sys-77432550242619391028652600"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_sys_83396747445816645808979860" {
  plugin  = "gcp"
  project = "sys-83396747445816645808979860"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_i_shared_base_vtix" {
  plugin  = "gcp"
  project = "prj-i-shared-base-vtix"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_m_shared_base_ue4h" {
  plugin  = "gcp"
  project = "prj-m-shared-base-ue4h"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_c_billing_logs_igh9" {
  plugin  = "gcp"
  project = "prj-c-billing-logs-igh9"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_c_kms_a0bj" {
  plugin  = "gcp"
  project = "prj-c-kms-a0bj"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_c_logging_yozr" {
  plugin  = "gcp"
  project = "prj-c-logging-yozr"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_c_scc_d6ki" {
  plugin  = "gcp"
  project = "prj-c-scc-d6ki"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_c_secrets_308g" {
  plugin  = "gcp"
  project = "prj-c-secrets-308g"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_d_kms_3k9a" {
  plugin  = "gcp"
  project = "prj-d-kms-3k9a"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_d_monitoring_n9jv" {
  plugin  = "gcp"
  project = "prj-d-monitoring-n9jv"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_d_secrets_67yy" {
  plugin  = "gcp"
  project = "prj-d-secrets-67yy"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_d_bu1_b_app_s1_3qz3" {
  plugin  = "gcp"
  project = "prj-d-bu1-b-app-s1-3qz3"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_d_bu1_b_dat_s1_opyk" {
  plugin  = "gcp"
  project = "prj-d-bu1-b-dat-s1-opyk"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_d_bu1_b_pub_s1_ng3l" {
  plugin  = "gcp"
  project = "prj-d-bu1-b-pub-s1-ng3l"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_d_bu2_b_app_s2_scuy" {
  plugin  = "gcp"
  project = "prj-d-bu2-b-app-s2-scuy"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_d_bu2_b_dat_s2_acpp" {
  plugin  = "gcp"
  project = "prj-d-bu2-b-dat-s2-acpp"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_d_bu2_b_pub_s2_ecm4" {
  plugin  = "gcp"
  project = "prj-d-bu2-b-pub-s2-ecm4"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_i_kms_w6ei" {
  plugin  = "gcp"
  project = "prj-i-kms-w6ei"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_i_monitoring_4oby" {
  plugin  = "gcp"
  project = "prj-i-monitoring-4oby"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_i_secrets_9a17" {
  plugin  = "gcp"
  project = "prj-i-secrets-9a17"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_i_bu1_b_idn_s1_f4zt" {
  plugin  = "gcp"
  project = "prj-i-bu1-b-idn-s1-f4zt"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_i_bu2_b_idn_s2_1p4g" {
  plugin  = "gcp"
  project = "prj-i-bu2-b-idn-s2-1p4g"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_m_kms_9101" {
  plugin  = "gcp"
  project = "prj-m-kms-9101"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_m_monitoring_5azq" {
  plugin  = "gcp"
  project = "prj-m-monitoring-5azq"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_m_secrets_rygd" {
  plugin  = "gcp"
  project = "prj-m-secrets-rygd"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_m_bu1_b_mgt_s1_u5z4" {
  plugin  = "gcp"
  project = "prj-m-bu1-b-mgt-s1-u5z4"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_d_shared_base_ui45" {
  plugin  = "gcp"
  project = "prj-d-shared-base-ui45"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_i_shared_base_5p6b" {
  plugin  = "gcp"
  project = "prj-i-shared-base-5p6b"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_m_shared_base_l23e" {
  plugin  = "gcp"
  project = "prj-m-shared-base-l23e"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_n_shared_base_q37n" {
  plugin  = "gcp"
  project = "prj-n-shared-base-q37n"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_net_dns_xl09" {
  plugin  = "gcp"
  project = "prj-net-dns-xl09"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_net_hub_base_yhyv" {
  plugin  = "gcp"
  project = "prj-net-hub-base-yhyv"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_net_interconnect_cz6f" {
  plugin  = "gcp"
  project = "prj-net-interconnect-cz6f"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_p_shared_base_tp4m" {
  plugin  = "gcp"
  project = "prj-p-shared-base-tp4m"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_n_kms_8rwi" {
  plugin  = "gcp"
  project = "prj-n-kms-8rwi"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_n_monitoring_lmkv" {
  plugin  = "gcp"
  project = "prj-n-monitoring-lmkv"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_n_secrets_re9a" {
  plugin  = "gcp"
  project = "prj-n-secrets-re9a"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_n_bu1_b_app_s1_ed86" {
  plugin  = "gcp"
  project = "prj-n-bu1-b-app-s1-ed86"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_n_bu1_b_dat_s1_gsoo" {
  plugin  = "gcp"
  project = "prj-n-bu1-b-dat-s1-gsoo"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_n_bu1_b_pub_s1_eokf" {
  plugin  = "gcp"
  project = "prj-n-bu1-b-pub-s1-eokf"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_n_bu2_b_app_s2_y42x" {
  plugin  = "gcp"
  project = "prj-n-bu2-b-app-s2-y42x"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_n_bu2_b_dat_s2_5qs6" {
  plugin  = "gcp"
  project = "prj-n-bu2-b-dat-s2-5qs6"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_n_bu2_b_pub_s2_rg1i" {
  plugin  = "gcp"
  project = "prj-n-bu2-b-pub-s2-rg1i"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_p_kms_mv3y" {
  plugin  = "gcp"
  project = "prj-p-kms-mv3y"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_p_monitoring_ftqd" {
  plugin  = "gcp"
  project = "prj-p-monitoring-ftqd"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_p_secrets_5an9" {
  plugin  = "gcp"
  project = "prj-p-secrets-5an9"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_p_bu1_b_app_s1_1ezr" {
  plugin  = "gcp"
  project = "prj-p-bu1-b-app-s1-1ezr"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_p_bu1_b_dat_s1_q8si" {
  plugin  = "gcp"
  project = "prj-p-bu1-b-dat-s1-q8si"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_p_bu1_b_pub_s1_f8tj" {
  plugin  = "gcp"
  project = "prj-p-bu1-b-pub-s1-f8tj"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_p_bu2_b_app_s2_jrxg" {
  plugin  = "gcp"
  project = "prj-p-bu2-b-app-s2-jrxg"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_p_bu2_b_dat_s2_1kcx" {
  plugin  = "gcp"
  project = "prj-p-bu2-b-dat-s2-1kcx"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_p_bu2_b_pub_s2_fyt7" {
  plugin  = "gcp"
  project = "prj-p-bu2-b-pub-s2-fyt7"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_b_seed_46ef_0c2d" {
  plugin  = "gcp"
  project = "prj-b-seed-46ef-0c2d"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp_prj_b_seed_7c39_66c1" {
  plugin  = "gcp"
  project = "prj-b-seed-7c39-66c1"
  credentials = "/tmp/authentication/service-account.json"
}

connection "gcp" {
  plugin = "gcp"
  type        = "aggregator"
  credentials = "/tmp/authentication/service-account.json"
  connections = ["gcp_*"]
}