from flask import Flask, request, jsonify, Response, stream_with_context, make_response
import subprocess
import shlex
import logging
import sys
import json
import os
from datetime import datetime
import boto3
from botocore.exceptions import ClientError, NoCredentialsError, PartialCredentialsError
from generate_control import run_prompt
import re
import time
import random
from auth import auth, get_service_credentials
import run_steampipe
import signal
import atexit
from typing import List, Dict
import requests
from functools import wraps
import traceback
import threading
sys.path.append('/app/shared')
from job_manager import job_manager
from steampipe_health import initialize_steampipe_on_startup


app = Flask(__name__)

@app.after_request
def after_request(response):
    response.headers['Access-Control-Allow-Origin'] = '*'
    response.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
    response.headers['Access-Control-Allow-Headers'] = '*'
    if request.method == 'OPTIONS':
        response.status_code = 200
    return response

# Configure structured logging for Cloud Run
class StructuredMessage:
    def __init__(self, message, **kwargs):
        self.message = message
        self.kwargs = kwargs

    def __str__(self):
        return json.dumps({
            'message': self.message,
            'severity': 'INFO',
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            **self.kwargs
        })

# Configure logging for Cloud Run
logging.basicConfig(
    level=logging.INFO,
    format='%(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

def log(message, **kwargs):
    """Enhanced logging format for Cloud Run"""
    logger.info(StructuredMessage(message, **kwargs))

# Constants
CONTROLS_FILE_PATH = './my_controls/my_controls.pp'  # Path to your .sp file
domain = os.getenv('DOMAIN')
NEOSEC_VERIFICATION_URL = f"https://{domain}/charge_credits"
NEOSEC_SUBSCRIPTION_URL = f"https://{domain}/check_subscription"
AUTH_TYPE = os.getenv('AUTH_TYPE', 'adc')  # Default to 'adc' if not set

# AWS clients
aws_organizations = boto3.client('organizations')
aws_sts = boto3.client('sts')

 # Path to your .sp file

# Initialize global variables for process management
steampipe_process = None
max_retries = 3
database_timeout = 120  # seconds

def init_steampipe_service():
    """Initialize Steampipe service using improved health module"""
    # Skip steampipe service in Cloud Run - it doesn't support background services
    if os.getenv('K_SERVICE'):  # Cloud Run sets this env var
        log("Running in Cloud Run - skipping steampipe service initialization", severity="INFO")
        return True
    try:
        initialize_steampipe_on_startup()
        return True
    except Exception as e:
        log(f"Failed to initialize Steampipe service: {e}", severity="ERROR")
        return False

app.register_blueprint(auth)


# Helper Functions

def save_output_to_json(account_id, benchmark, result_data):
    """Save the command output and results to a JSON file."""
    try:
        # Create output directory if it doesn't exist
        output_dir = "/app/steampipe-mod-aws-compliance/outputs"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            
        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{output_dir}/{account_id}_{benchmark}_{timestamp}.json"
        
        # Prepare the JSON data
        json_data = {
            "account_id": account_id,
            "benchmark": benchmark,
            "timestamp": timestamp,
            "results": result_data
        }
        
        # Write JSON to file
        with open(filename, 'w') as f:
            json.dump(json_data, f, indent=2)
            
        return filename
    except Exception as e:
        log(f"Error saving output to JSON file: {str(e)}")
        return None

def run_powerpipe_command_async(job_id, command_name, account_id, secret_reference=None, sm_credentials=None):
    """Execute powerpipe benchmark command asynchronously"""
    try:
        # If secret_reference is provided, reinitialize credentials
        if secret_reference:
            log(f"Regenerating config for secret reference: {secret_reference}")
            creds, actual_account_id = get_service_credentials(secret_reference, sm_credentials)
            
            # Log what we got back
            log(f"get_service_credentials returned: creds={creds is not None}, actual_account_id={repr(actual_account_id)}")
            
            # Check if authentication failed
            if creds is None and actual_account_id and actual_account_id.startswith("Error"):
                # Authentication failed
                error_msg = actual_account_id
                log(f"Authentication failed: {error_msg}")
                job_manager.update_job_status(job_id, 'failed', error=error_msg)
                return
            
            if actual_account_id and not actual_account_id.startswith("Error"):
                # Use the actual account ID from the credentials
                account_id = actual_account_id
                log(f"Using account ID from secret: {account_id}")
                # Regenerate Steampipe configuration
                is_cloud_run = os.getenv('K_SERVICE') is not None
                run_steampipe.generate_and_save_config(is_cloud_run=is_cloud_run)
                log("AWS Steampipe config regenerated with secret credentials")
        
        # Update job status to running
        job_manager.update_job_status(job_id, 'running', progress=10)
        
        # Always use powerpipe
        base_command = [
            "powerpipe",
            "benchmark",
            "run",
            command_name,
            "--var",
            f"account_id={account_id}",
            "--export=json"
        ]
        
        # Determine if we should limit to specific account or scan all
        scan_all = account_id.lower() in ["all", "all-accounts", "aws", "*"]
        
        # Add search-path-prefix to limit scan to specific account unless scanning all
        if not scan_all:
            # Always add search-path-prefix for specific account scans
            connection_name = f"aws_{account_id}"
            base_command.extend(["--search-path-prefix", connection_name])
            log(f"Limiting scan to account: {account_id} using connection: {connection_name}")
        else:
            log(f"Scanning all accounts as requested (account_id: {account_id})")
        
        log(f"Executing command: {' '.join(base_command)}")
        job_manager.update_job_status(job_id, 'running', progress=30)
        
        # Create a process with pipe to capture output
        process = subprocess.Popen(
            base_command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
            text=True,
            cwd="/app/steampipe-mod-aws-compliance"
        )
        
        # Read output and error streams
        stdout, stderr = process.communicate()
        job_manager.update_job_status(job_id, 'running', progress=80)

        # Try to find and read the exported JSON file
        json_file_path = None
        json_data = None
        
        for line in stdout.splitlines():
            if "File exported to" in line:
                json_file_path = line.split()[-1].strip()
                break
                
        if json_file_path and os.path.exists(json_file_path):
            try:
                with open(json_file_path, 'r') as f:
                    json_data = json.load(f)
                    
                # Save the result
                job_manager.save_result(job_id, 'aws', command_name, account_id, json_data)
                log(f"Job {job_id} completed successfully")
                
            except Exception as e:
                error_msg = f"Error reading JSON output: {str(e)}"
                log(error_msg)
                job_manager.update_job_status(job_id, 'failed', error=error_msg)
        else:
            # If no JSON file, save the raw output
            result = {
                'status': 'completed' if process.returncode == 0 else 'error',
                'stdout': stdout,
                'stderr': stderr,
                'return_code': process.returncode
            }
            
            if process.returncode == 0:
                job_manager.save_result(job_id, 'aws', command_name, account_id, result)
            else:
                job_manager.update_job_status(job_id, 'failed', error=stderr or stdout)
                
    except Exception as e:
        error_msg = f"Error executing benchmark: {str(e)}"
        log(error_msg)
        job_manager.update_job_status(job_id, 'failed', error=error_msg)

def run_powerpipe_command(command_name, account_id, secret_reference=None, sm_credentials=None):
    """Execute Powerpipe benchmark command with account_id and return real-time output."""
    try:
        # If secret_reference is provided, reinitialize credentials
        if secret_reference:
            log(f"Regenerating config for secret reference: {secret_reference}")
            creds, actual_account_id = get_service_credentials(secret_reference, sm_credentials)
            
            # Check if authentication failed
            if creds is None and actual_account_id and actual_account_id.startswith("Error"):
                # Authentication failed, return error
                return {
                    "status": "error",
                    "error": actual_account_id,
                    "benchmark_results": None
                }
            
            if actual_account_id and not actual_account_id.startswith("Error"):
                # Use the actual account ID from the credentials
                account_id = actual_account_id
                log(f"Using account ID from secret: {account_id}")
                # Regenerate Steampipe configuration
                is_cloud_run = os.getenv('K_SERVICE') is not None
                run_steampipe.generate_and_save_config(is_cloud_run=is_cloud_run)
        
        # Always use powerpipe
        base_command = [
            "powerpipe",
            "benchmark",
            "run",
            command_name,
            "--var",
            f"account_id={account_id}",
            "--export=json"
        ]
        
        # Determine if we should limit to specific account or scan all
        scan_all = account_id.lower() in ["all", "all-accounts", "aws", "*"]
        
        # Add search-path-prefix to limit scan to specific account unless scanning all
        if not scan_all:
            # Always add search-path-prefix for specific account scans
            connection_name = f"aws_{account_id}"
            base_command.extend(["--search-path-prefix", connection_name])
            log(f"Limiting scan to account: {account_id} using connection: {connection_name}")
        else:
            log(f"Scanning all accounts as requested (account_id: {account_id})")
        
        log(f"Executing command: {' '.join(base_command)}")
        
        # Create a process with pipe to capture output
        process = subprocess.Popen(
            base_command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
            text=True,
            cwd="/app/steampipe-mod-aws-compliance"
        )
        
        # Read output and error streams
        stdout, stderr = process.communicate()

        # Try to find and read the exported JSON file
        json_file_path = None
        json_data = None
        
        for line in stdout.splitlines():
            if "File exported to" in line:
                json_file_path = line.split()[-1].strip()
                break
                
        if json_file_path and os.path.exists(json_file_path):
            try:
                with open(json_file_path, 'r') as f:
                    json_data = json.load(f)
            except Exception as e:
                log(f"Error reading JSON file: {str(e)}")

        # Save consolidated results to our JSON file
        # Only include the actual benchmark results, not the raw stdout which may contain invalid characters
        result_data = {
            "benchmark_results": json_data,
            "command": command_name,
            "error": stderr if stderr else None
        }
        
        output_file = save_output_to_json(account_id, command_name, result_data)

        return {
            "status": "success" if process.returncode == 0 else "error",
            "output": stdout,
            "error": stderr if stderr else None,
            "command": command_name,
            "return_code": process.returncode,
            "output_file": output_file,
            "benchmark_results": json_data
        }
    except Exception as e:
        log(f"Error executing command: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "command": command_name
        }

def extract_steampipe_data(response_text: str, control_name_suggestion: str = "testcontrol") -> dict:
    """Extracts and formats the Steampipe control and query."""
    try:
        control_block = ""
        sql_query_block = ""
        control_name = control_name_suggestion

        # Extract the entire control block
        control_match = re.search(r'control\s+"([^"]+)"\s*\{[^}]*\}', response_text, re.DOTALL | re.IGNORECASE)
        if control_match:
            control_name = control_match.group(1).strip()
            control_block = control_match.group(0).strip()

        # Extract the entire query block
        query_match = re.search(r'query\s+"[^"]+"\s*\{[^}]*\}', response_text, re.DOTALL | re.IGNORECASE)
        if not query_match:  # If not in query block, look for a plain SQL block
           query_match = re.search(r'`sql(.*?)`', response_text, re.DOTALL | re.IGNORECASE)
           if query_match: # found sql block only
                sql_query_content = query_match.group(1).strip()
                # create query block
                sql_query_block = f'''query "{control_name}" {{
  sql = <<-EOQ
{sql_query_content}
  EOQ
}}'''
        else:
            sql_query_block = query_match.group(0).strip()

        # Construct default control block, if control block missing, but, sql block available
        if not control_block and sql_query_block:
            control_block = f"""control "{control_name}" {{
  title       = "Generated Control for {control_name}"
  description = "Generated control based on user prompt."
  query       = query.{control_name}
  tags = merge(local.aws_compliance_common_tags, {{
    service = "AWS/Generated"  // Modify as needed
  }})
}}"""

        if not control_block or not sql_query_block: #check if we have both blocks
            return {"error": "Could not find both control and query blocks."}

        return {
            "control_block": control_block,
            "query_block": sql_query_block,
            "control_name": control_name,
        }

    except Exception as e:
        return {"error": f"Error during extraction/formatting: {e}"}

def write_steampipe_control_file(filepath: str, extracted_data: dict) -> None:
    """Overwrites the Steampipe control file with the new content."""
    control_name = extracted_data.get('control_name', 'testcontrol')  # Fallback
    control_block = extracted_data.get('control_block')
    query_block = extracted_data.get('query_block')

    if not control_block or not query_block:
        raise ValueError("Control block and query block are required.")

    try:
        # Construct the complete file content
        new_file_content = f"""locals {{
  my_controls_common_tags = merge(local.aws_compliance_common_tags, {{
    type = "Benchmark"
  }})
}}

benchmark "my_controls" {{
  title       = "All Controls"
  description = "This benchmark contains all controls grouped by service to help you detect resource configurations that do not meet best practices."
  children = [
    control.{control_name},
  ]
  tags = local.my_controls_common_tags
}}

{control_block}

{query_block}
"""
        # Write the new content to the file (overwrites existing content)
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(new_file_content)

    except Exception as e:
        raise Exception(f"Error writing to Steampipe control file: {e}")

# AWS Organization and Account Functions
def get_aws_organization_id():
    """Retrieve the AWS organization ID using Boto3."""
    try:
        # Create a new session to ensure it picks up the latest credentials
        session = boto3.session.Session()
        organizations = session.client('organizations', region_name='us-east-1')
        
        response = organizations.describe_organization()
        return response['Organization']['Id'], None
    except ClientError as e:
        log(f"AWS API Error: {e}")
        # Check for specific credential errors
        error_code = getattr(e, 'response', {}).get('Error', {}).get('Code', '')
        error_message = getattr(e, 'response', {}).get('Error', {}).get('Message', str(e))
        
        if error_code in ['InvalidClientTokenId', 'UnrecognizedClientException', 'AccessDenied']:
            return None, f"AWS Credentials Error: {error_message}"
        return None, f"AWS API Error: {error_message}"
    except NoCredentialsError:
        log("No AWS credentials found")
        return None, "No AWS credentials found"
    except PartialCredentialsError as e:
        log(f"Partial AWS credentials error: {e}")
        return None, f"Partial AWS credentials error: {e}"
    except Exception as e:
        log(f"Unexpected error in get_aws_organization_id: {str(e)}")
        return None, f"Unexpected error: {str(e)}"

def get_aws_accounts():
    """Retrieve all AWS accounts in the organization."""
    try:
        # Create a new session to ensure it picks up the latest credentials
        session = boto3.session.Session()
        organizations = session.client('organizations', region_name='us-east-1')
        
        accounts = []
        paginator = organizations.get_paginator('list_accounts')
        for page in paginator.paginate():
            accounts.extend(page['Accounts'])
        return accounts, None
    except ClientError as e:
        log(f"AWS API Error: {e}")
        # Check for specific credential errors
        error_code = getattr(e, 'response', {}).get('Error', {}).get('Code', '')
        error_message = getattr(e, 'response', {}).get('Error', {}).get('Message', str(e))
        
        if error_code in ['InvalidClientTokenId', 'UnrecognizedClientException', 'AccessDenied']:
            return None, f"AWS Credentials Error: {error_message}"
        return None, f"AWS API Error: {error_message}"
    except NoCredentialsError:
        log("No AWS credentials found")
        return None, "No AWS credentials found"
    except PartialCredentialsError as e:
        log(f"Partial AWS credentials error: {e}")
        return None, f"Partial AWS credentials error: {e}"
    except Exception as e:
        log(f"Unexpected error in get_aws_accounts: {str(e)}")
        return None, f"Unexpected error: {str(e)}"

def check_subscription():
    """
    Decorator to verify active subscription status without charging credits.
    Similar to charge_credits but uses the check_subscription endpoint instead.
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Skip subscription check for development and when IAP is not configured
            if AUTH_TYPE == 'adc' or os.getenv('SKIP_IAP_CHECK', 'true').lower() == 'true':
                log("Skipping subscription verification (development mode or IAP not configured)", 
                    auth_type=AUTH_TYPE,
                    severity="INFO")
                return f(*args, **kwargs)
            try:
                # Get IAP token from headers
                iap_token = request.headers.get('X-Original-Iap-Jwt')
                log("Processing request headers", 
                    headers=dict(request.headers),
                    severity="DEBUG")
                
                host = request.headers.get('Referer')
                if not iap_token:
                    log("Missing IAP token", severity="ERROR")
                    return jsonify({
                        "status": "error",
                        "error": "Missing IAP token"
                    }), 401

                # Make request to check_subscription endpoint on neosec marketplace
                headers = {
                    "token": iap_token,
                    "Content-Type": "application/json",
                    "subdomain": host
                }
                
                log("Making request to check subscription", 
                    url=NEOSEC_SUBSCRIPTION_URL,  # You'll need to define this constant
                    severity="DEBUG")
                    
                # No need to include cost_credits for subscription check
                data = {}
                
                response = requests.post(
                    NEOSEC_SUBSCRIPTION_URL,
                    headers=headers,
                    json=data
                )

                log("Response from subscription check", 
                    response_status=response.status_code,
                    response_body=response.text,
                    severity="DEBUG")
                
                if response.status_code != 200:
                    log("Subscription verification failed", 
                        status_code=response.status_code,
                        severity="ERROR")
                    return jsonify({
                        "status": "error",
                        "error": "No active subscription found"
                    }), 402  # Payment Required

                return f(*args, **kwargs)
            except Exception as e:
                log("Subscription verification error", 
                    error=str(e),
                    stack_trace=traceback.format_exc(),
                    severity="ERROR")
                return jsonify({
                    "status": "error",
                    "error": f"Subscription verification failed: {str(e)}"
                }), 500
        return decorated_function
    return decorator

def charge_credits(cost_credits=1):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Skip IAP check for development and when IAP is not configured
            if AUTH_TYPE == 'adc' or os.getenv('SKIP_IAP_CHECK', 'true').lower() == 'true':
                log("Skipping IAP verification (development mode or IAP not configured)", 
                    auth_type=AUTH_TYPE,
                    severity="INFO")
                return f(*args, **kwargs)
            try:
                # Get IAP token from headers
                iap_token = request.headers.get('X-Original-Iap-Jwt')
                log("Processing request headers", 
                    headers=dict(request.headers),
                    severity="DEBUG")
                
                host = request.headers.get('Referer')
                if not iap_token:
                    log("Missing IAP token", severity="ERROR")
                    return jsonify({
                        "status": "error",
                        "error": "Missing IAP token"
                    }), 401

                # Make request to charge_credit endpoint on neosec marketplace
                headers = {
                    "token": iap_token,
                    "Content-Type": "application/json",
                    "subdomain": host
                }
                
                log("Making request to Neosec marketplace", 
                    url=NEOSEC_VERIFICATION_URL,
                    credits=cost_credits,
                    severity="DEBUG")
                    
                data = {
                    "cost_credits": cost_credits
                }
                
                response = requests.post(
                    NEOSEC_VERIFICATION_URL,
                    headers=headers,
                    json=data
                )

                log("Response from charge credit", 
                    response_status=response.status_code,
                    response_body=response.text,
                    severity="DEBUG")
                
                if response.status_code != 200:
                    log("Failed to charge credit", 
                        status_code=response.status_code,
                        severity="ERROR")
                    return jsonify({
                        "status": "error",
                        "error": "Failed to charge credit"
                    }), 400

                return f(*args, **kwargs)
            except Exception as e:
                log("IAP verification error", 
                    error=str(e),
                    stack_trace=traceback.format_exc(),
                    severity="ERROR")
                return jsonify({
                    "status": "error",
                    "error": f"IAP verification failed: {str(e)}"
                }), 500
        return decorated_function
    return decorator

        
@app.route('/api/aws/test-auth', methods=['POST'])
def test_auth():
    """Test authentication with secret reference"""
    try:
        data = request.get_json()
        log(f"Raw request data keys: {list(data.keys()) if data else 'No data'}")
        secret_reference = data.get('secret_reference')
        sm_credentials = data.get('sm_credentials')
        
        log(f"Test auth endpoint - Secret reference: {secret_reference}")
        log(f"SM credentials provided: {bool(sm_credentials)}")
        if sm_credentials and isinstance(sm_credentials, dict):
            log(f"SM credentials project_id: {sm_credentials.get('project_id')}")
        log(f"AUTH_TYPE: {os.getenv('AUTH_TYPE', 'not set')}")
        
        # Test authentication
        credential, account_id = get_service_credentials(secret_reference, sm_credentials)
        
        if account_id and not account_id.startswith("Error"):
            return jsonify({
                "status": "success",
                "message": "Authentication successful using secret reference",
                "account_id": account_id,
                "secret_reference": secret_reference,
                "auth_type": "secret_manager",
                "credential_type": type(credential).__name__ if credential else "None"
            })
        else:
            return jsonify({
                "status": "error",
                "message": account_id if account_id else "Authentication failed",
                "secret_reference": secret_reference
            }), 401
            
    except Exception as e:
        log(f"Test auth error: {str(e)}")
        return jsonify({
            "status": "error",
            "message": str(e),
            "secret_reference": secret_reference if 'secret_reference' in locals() else None
        }), 500

@app.route('/api/aws/generate', methods=['POST'])
def generate():
    """API endpoint to generate control."""
    try:
        data = request.get_json()
        user_query = data['prompt']  # Get the prompt from the request

        response_text = run_prompt(user_query)
        extracted_data = extract_steampipe_data(response_text)

        if "error" in extracted_data:
            return jsonify({'error': extracted_data["error"], 'response': response_text}), 500

        write_steampipe_control_file(CONTROLS_FILE_PATH, extracted_data)

        # Return the combined, formatted output to the user.
        formatted_output = f"{extracted_data['control_block']}\n\n{extracted_data['query_block']}"
        return jsonify({'response': formatted_output})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/aws/testcontrol', methods=['POST'])
def testcontrol():
    """
    Runs a Powerpipe command.  Expects 'command_name' and 'account_id' in the request body.
    """
    data = request.get_json()

    if not data or 'account_id' not in data:
        account_id = 'aws'
    else:
        account_id = data['account_id']
    try:
        output = run_powerpipe_command("my_controls", account_id)
        log(f"{output}")
        # Return the output as a JSON response, and set the content type to plain text.
        return jsonify({'response': output})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/aws/run-aws-benchmark', methods=['POST'])
@check_subscription()
def run_benchmark():
    """API endpoint to run Powerpipe benchmark."""
    try:
        log(f"Request headers: {request.headers}")
        data = request.get_json()
        if not data or 'benchmark' not in data:
            return jsonify({
                "status": "error",
                "error": "Missing 'benchmark' parameter"
            }), 400
            
        if 'account_id' not in data:
            return jsonify({
                "status": "error",
                "error": "Missing 'account_id' parameter"
            }), 400
            
        benchmark = data['benchmark']
        account_id = data['account_id']
        secret_reference = data.get('secret_reference')
        sm_credentials = data.get('sm_credentials')
        
        log(f"Running benchmark: {benchmark} for account: {account_id}")
        log(f"Secret reference: {secret_reference}, SM credentials provided: {bool(sm_credentials)}")
        
        # Check if we should use streaming (for known large responses)
        should_stream = (benchmark == "nist_800_53_rev_5" and account_id == "aws")
        
        if should_stream:
            # Stream large responses
            def generate():
                # Run the benchmark and get results
                result = run_powerpipe_command(benchmark, account_id, secret_reference, sm_credentials)
                
                # Stream the result in chunks
                # First send metadata/header
                yield json.dumps({"status": "streaming", "total_size": len(json.dumps(result))}) + "\n"
                
                # Then stream the benchmark results in chunks
                chunk_size = 1024 * 1024  # 1MB chunks
                result_json = json.dumps(result)
                
                for i in range(0, len(result_json), chunk_size):
                    chunk = result_json[i:i+chunk_size]
                    yield chunk
            
            return Response(stream_with_context(generate()), 
                            content_type='application/json; charset=utf-8')
        else:
            # For smaller responses, keep existing behavior
            result = run_powerpipe_command(benchmark, account_id, secret_reference, sm_credentials)
            return jsonify(result)
        
    except Exception as e:
        log(f"API error: {str(e)}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/api/aws/run-aws-benchmark-async', methods=['POST'])
@check_subscription()
def run_benchmark_async():
    """API endpoint to run powerpipe benchmark asynchronously"""
    try:
        data = request.get_json()
        if not data or 'benchmark' not in data:
            return jsonify({
                "status": "error",
                "error": "Missing 'benchmark' parameter"
            }), 400
            
        if 'account_id' not in data:
            return jsonify({
                "status": "error",
                "error": "Missing 'account_id' parameter"
            }), 400
            
        benchmark = data['benchmark']
        account_id = data['account_id']
        secret_reference = data.get('secret_reference')
        sm_credentials = data.get('sm_credentials')
        
        # If secret_reference is provided, use it to get credentials
        if secret_reference:
            log(f"Using secret reference: {secret_reference}")
            creds, actual_account_id = get_service_credentials(secret_reference, sm_credentials)
            
            # Check if authentication failed
            if creds is None and actual_account_id and actual_account_id.startswith("Error"):
                return jsonify({
                    "status": "error",
                    "error": actual_account_id
                }), 400
            
            if actual_account_id and not actual_account_id.startswith("Error"):
                # Use the actual account ID from the credentials
                account_id = actual_account_id
                log(f"Using account ID from secret: {account_id}")
        
        # Create a job
        job_id = job_manager.create_job('aws', benchmark, account_id)
        
        # Run the benchmark in a background thread
        thread = threading.Thread(
            target=run_powerpipe_command_async,
            args=(job_id, benchmark, account_id, secret_reference, sm_credentials),
            daemon=True
        )
        thread.start()
        
        log(f"Started async benchmark job: {job_id}")
        
        return jsonify({
            "status": "success",
            "job_id": job_id,
            "message": "Benchmark job started",
            "check_status_url": f"/api/aws/job/{job_id}/status",
            "get_result_url": f"/api/aws/job/{job_id}/result"
        })
        
    except Exception as e:
        log(f"API error: {str(e)}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/api/aws/job/<job_id>/status', methods=['GET'])
def get_job_status(job_id):
    """Get the status of a running job"""
    job_status = job_manager.get_job_status(job_id)
    
    if not job_status:
        return jsonify({
            "status": "error",
            "error": "Job not found"
        }), 404
    
    return jsonify(job_status)

@app.route('/api/aws/job/<job_id>/result', methods=['GET'])
def get_job_result(job_id):
    """Get the result of a completed job"""
    job_status = job_manager.get_job_status(job_id)
    
    if not job_status:
        return jsonify({
            "status": "error",
            "error": "Job not found"
        }), 404
    
    if job_status['status'] != 'completed':
        return jsonify({
            "status": "error",
            "error": f"Job is {job_status['status']}, not completed",
            "job_status": job_status
        }), 400
    
    result = job_manager.get_result_by_job_id(job_id)
    if result:
        return jsonify(result)
    else:
        return jsonify({
            "status": "error",
            "error": "Result not found"
        }), 404

@app.route('/api/aws/latest-result/<benchmark>/<account_id>', methods=['GET'])
def get_latest_result(benchmark, account_id):
    """Get the latest result for a specific benchmark and account"""
    result = job_manager.get_latest_result('aws', benchmark, account_id)
    
    if result:
        return jsonify(result)
    else:
        return jsonify({
            "status": "error",
            "error": "No results found for this benchmark and account"
        }), 404

@app.route('/api/aws/list-results', methods=['GET'])
def list_results():
    """List all available results"""
    results = job_manager.list_available_results('aws')
    return jsonify({
        "status": "success",
        "results": results
    })

@app.route('/api/aws/jobs', methods=['GET'])
def list_jobs():
    """List all jobs"""
    jobs = job_manager.get_all_jobs('aws')
    return jsonify({
        "status": "success",
        "jobs": jobs
    })

@app.route('/api/aws/benchmark', methods=['POST'])
@check_subscription()
def run_benchmark_alias():
    """Alias endpoint for run-aws-benchmark-async"""
    return run_benchmark_async()

@app.route('/api/aws/reports', methods=['GET'])
def list_reports():
    """List all reports for a customer"""
    try:
        # List all saved report files in the outputs directory
        output_dir = "/app/steampipe-mod-aws-compliance/outputs"
        reports = []
        
        if os.path.exists(output_dir):
            for filename in os.listdir(output_dir):
                if filename.endswith('.json'):
                    # Parse filename to extract details
                    parts = filename.replace('.json', '').split('_')
                    if len(parts) >= 3:
                        account_id = parts[0]
                        benchmark = '_'.join(parts[1:-2])  # Handle benchmarks with underscores
                        timestamp = parts[-2] + '_' + parts[-1]
                        
                        # Get file size and modification time
                        file_path = os.path.join(output_dir, filename)
                        file_stat = os.stat(file_path)
                        
                        reports.append({
                            "filename": filename,
                            "account_id": account_id,
                            "benchmark": benchmark,
                            "timestamp": timestamp,
                            "size": file_stat.st_size,
                            "modified": datetime.fromtimestamp(file_stat.st_mtime).isoformat()
                        })
        
        # Sort by modification time, most recent first
        reports.sort(key=lambda x: x['modified'], reverse=True)
        
        return jsonify({
            "status": "success",
            "reports": reports,
            "total": len(reports)
        })
    except Exception as e:
        log(f"Error listing reports: {str(e)}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/api/aws/listReports/<account_id>', methods=['GET'])
def list_reports_by_account(account_id):
    """List saved report files for a specific AWS account"""
    try:
        # List all saved report files for the specified account
        output_dir = "/app/steampipe-mod-aws-compliance/outputs"
        reports = []
        
        if os.path.exists(output_dir):
            for filename in os.listdir(output_dir):
                if filename.startswith(f"{account_id}_") and filename.endswith('.json'):
                    # Parse filename to extract details
                    parts = filename.replace('.json', '').split('_')
                    if len(parts) >= 3:
                        benchmark = '_'.join(parts[1:-2])  # Handle benchmarks with underscores
                        timestamp = parts[-2] + '_' + parts[-1]
                        
                        # Get file size and modification time
                        file_path = os.path.join(output_dir, filename)
                        file_stat = os.stat(file_path)
                        
                        reports.append({
                            "filename": filename,
                            "account_id": account_id,
                            "benchmark": benchmark,
                            "timestamp": timestamp,
                            "size": file_stat.st_size,
                            "modified": datetime.fromtimestamp(file_stat.st_mtime).isoformat(),
                            "path": file_path
                        })
        
        # Sort by modification time, most recent first
        reports.sort(key=lambda x: x['modified'], reverse=True)
        
        return jsonify({
            "status": "success",
            "account_id": account_id,
            "reports": reports,
            "total": len(reports)
        })
    except Exception as e:
        log(f"Error listing reports for account {account_id}: {str(e)}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/api/aws/get-aws-org-id', methods=['GET', 'POST'])
def get_organization_id():
    """API endpoint to fetch AWS organization ID."""
    try:
        # Handle POST request with secret reference
        if request.method == 'POST':
            data = request.get_json()
            secret_reference = data.get('secret_reference') if data else None
            sm_credentials = data.get('sm_credentials') if data else None
            
            if secret_reference:
                log(f"Using secret reference for org ID: {secret_reference}")
                # Get credentials using the existing function
                credential, account_id = get_service_credentials(secret_reference, sm_credentials)
                
                if not credential or (account_id and account_id.startswith("Error")):
                    return jsonify({
                        "status": "error",
                        "error": f"Failed to authenticate with secret reference: {account_id}"
                    }), 401
                
                # Re-initialize AWS clients with the new credentials
                global aws_organizations, aws_sts
                try:
                    # Create new session with the loaded credentials
                    session = boto3.session.Session()
                    aws_organizations = session.client('organizations')
                    aws_sts = session.client('sts')
                    log("AWS clients re-initialized with secret reference credentials")
                except Exception as e:
                    log(f"Error re-initializing AWS clients: {str(e)}")
        
        # Fetch organization ID with current credentials
        org_id, error = get_aws_organization_id()
        if error:
            log(f"Organization ID fetch error: {error}")
            return jsonify({"status": "error", "error": error}), 500
            
        return jsonify({
            "status": "success",
            "organization_id": org_id
        })
    except Exception as e:
        log(f"Organization ID API error: {str(e)}")
        return jsonify({
            "status": "error", 
            "error": str(e)
        }), 500 

@app.route('/api/aws/get-accounts', methods=['POST'])
def get_accounts():
    """API endpoint to fetch all AWS accounts in the organization."""
    try:
        data = request.get_json()
        if not data or 'organization_id' not in data:
            return jsonify({"status": "error", "error": "Missing organization_id"}), 400
        
        # Check for secret reference in the request
        secret_reference = data.get('secret_reference')
        sm_credentials = data.get('sm_credentials')
        
        if secret_reference:
            log(f"Using secret reference for accounts: {secret_reference}")
            # Get credentials using the existing function
            credential, account_id = get_service_credentials(secret_reference, sm_credentials)
            
            if not credential or (account_id and account_id.startswith("Error")):
                return jsonify({
                    "status": "error",
                    "error": f"Failed to authenticate with secret reference: {account_id}"
                }), 401
            
            # Re-initialize AWS clients with the new credentials
            global aws_organizations, aws_sts
            try:
                # Create new session with the loaded credentials
                session = boto3.session.Session()
                aws_organizations = session.client('organizations')
                aws_sts = session.client('sts')
                log("AWS clients re-initialized with secret reference credentials for accounts")
            except Exception as e:
                log(f"Error re-initializing AWS clients: {str(e)}")

        accounts, error = get_aws_accounts()
        if error:
            log(f"Accounts fetch error: {error}")
            return jsonify({"status": "error", "error": error}), 500

        # Transform AWS accounts to match GCP projects format
        formatted_projects = [
            {
                "project_name": account["Name"],
                "name": account["Id"],
                "project_number": account["Id"]  # Using account ID as number for consistency
            }
            for account in accounts
        ]

        return jsonify({
            "status": "success",
            "projects": formatted_projects  # Changed from 'accounts' to 'projects'
        })
    except Exception as e:
        log(f"Error in get-accounts API: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Basic endpoint to verify API is running."""
    return jsonify({
        "status": "running"
    })


def init_steampipe_background():
    """Initialize Steampipe in background"""
    global credential, account_id, aws_organizations, aws_sts
    
    try:
        log("Background: Initializing credentials...")
        credential, account_id = get_service_credentials() 
        log(f"Background: Credentials initialized. Account ID: {account_id}")

        # Re-initialize AWS clients with fresh session to ensure they use the correct credentials
        try:
            session = boto3.session.Session(region_name='us-east-1')
            aws_organizations = session.client('organizations')
            aws_sts = session.client('sts')
            log("Background: AWS clients initialized with fresh session")
        except Exception as e:
            log(f"Background: Warning - Error initializing AWS clients: {str(e)}")

        # Generate Steampipe configuration with credentials
        log("Background: Generating Steampipe configuration...")
        is_cloud_run = os.getenv('K_SERVICE') is not None
        config_path = run_steampipe.generate_and_save_config(
            is_cloud_run=is_cloud_run
        )
        
        # Start Steampipe service with three attempts (skip in Cloud Run)
        if not os.getenv('K_SERVICE'):
            if not init_steampipe_service():
                log("Background: CRITICAL - Failed to start Steampipe service after 3 attempts")
            else:
                log("Background: Steampipe service initialized successfully")
        else:
            log("Background: Running in Cloud Run - steampipe service initialization skipped")
           
    except Exception as e:
        log(f"Background: Error during initialization: {str(e)}")
        log(f"Background: {traceback.format_exc()}")

def main():
    """Main application entry point"""
    global credential, account_id, aws_organizations, aws_sts, AUTH_TYPE
    
    log("Starting application initialization...")
    
    # Start Steampipe initialization in background
    init_thread = threading.Thread(target=init_steampipe_background, daemon=True)
    init_thread.start()
    
    # Start Flask application immediately
    port = int(os.environ.get('PORT', 8082))
    log(f"Starting Flask application on port {port}...")
    app.run(host='0.0.0.0', port=port, debug=False)

if __name__ == '__main__':
    main()