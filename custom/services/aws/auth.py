from flask import Blueprint
import boto3
import botocore
from botocore.exceptions import ClientError
from google.auth import default
from google.cloud import secretmanager
import os
import sys
import json
import logging
import traceback
import csv
import io
sys.path.append('/app/shared')
try:
    from secret_manager import SecretManagerClient, validate_credentials
except ImportError:
    # Fallback for local development
    SecretManagerClient = None
    validate_credentials = None

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create a blueprint
auth = Blueprint('auth', __name__)

# Use a writable directory for authentication files
AUTHENTICATION_FOLDER = os.path.join('/tmp', 'authentication')
AWS_CREDENTIALS_FILE = os.path.join(AUTHENTICATION_FOLDER, 'aws-credentials.csv')

def get_service_credentials(secret_reference=None, sm_credentials=None):
    """
    Get AWS credentials either from local files or from GCP Secret Manager.
    Returns (None, account_id) for AWS as credentials are handled via env vars.
    """
    try:
        # Get authentication type from environment variables
        auth_type = os.getenv('AUTH_TYPE', 'adc').lower()
        logger.info(f"Using authentication type: {auth_type}")
        
        # If secret_reference is provided, use it directly
        if secret_reference:
            logger.info(f"Using provided secret reference: {secret_reference}")
            if sm_credentials:
                logger.info(f"SM credentials provided: {bool(sm_credentials)}")
            else:
                logger.warning("No SM credentials provided to get_service_credentials")
            return get_credentials_from_secret_reference(secret_reference, sm_credentials)
        elif auth_type == 'adc':
            logger.info("Local deployment detected, using local AWS credential files")
            return get_aws_credentials_from_file()
        else:
            logger.info("Cloud deployment detected, using GCP Secret Manager for AWS credentials")
            return get_aws_credentials_from_gcp_secret()
            
    except Exception as e:
        logger.error(f"Authentication error: {str(e)}")
        logger.error(f"Detailed error: {traceback.format_exc()}")
        return None, f"Authentication error: {str(e)}"

def get_credentials_from_secret_reference(secret_reference, sm_credentials=None):
    """Get AWS credentials from Secret Manager using secret reference"""
    try:
        # SM credentials are required for accessing Secret Manager
        if not sm_credentials:
            logger.error("No SM credentials provided - this is required for accessing Secret Manager")
            return None, "Service Account credentials are required to access Secret Manager"
            
        # Create credentials from provided JSON
        if isinstance(sm_credentials, dict):
            from google.oauth2 import service_account
            sm_creds = service_account.Credentials.from_service_account_info(sm_credentials)
            project_id = sm_credentials.get('project_id')
            logger.info(f"Using SM credentials for project: {project_id}")
            
            if not project_id:
                logger.error("No project_id found in SM credentials")
                return None, "No project_id found in Service Account credentials"
        else:
            logger.error("Invalid sm_credentials format - expected dict")
            return None, "Invalid sm_credentials format"
            
        secret_client = secretmanager.SecretManagerServiceClient(credentials=sm_creds)
        
        # Construct secret path using the project from SM credentials
        secret_name = f"projects/{project_id}/secrets/{secret_reference}/versions/latest"
        
        logger.info(f"Retrieving AWS credentials from Secret Manager: {secret_reference}")
        
        # Access the secret
        try:
            response = secret_client.access_secret_version(request={"name": secret_name})
            secret_content = response.payload.data.decode("UTF-8")
        except Exception as e:
            logger.error(f"Failed to access secret: {str(e)}")
            logger.error(f"Secret path attempted: {secret_name}")
            return None, f"Failed to access secret: {str(e)}"
            
        # Parse JSON credentials
        try:
            creds_json = json.loads(secret_content)
            logger.info("Successfully parsed JSON credentials")
            
            # Look for credential keys
            creds_found = False
            
            # Check various key formats
            if "aws_access_key_id" in creds_json and "aws_secret_access_key" in creds_json:
                access_key = creds_json["aws_access_key_id"]
                secret_key = creds_json["aws_secret_access_key"]
                creds_found = True
            elif "AccessKeyId" in creds_json and "SecretAccessKey" in creds_json:
                access_key = creds_json["AccessKeyId"]
                secret_key = creds_json["SecretAccessKey"]
                creds_found = True
            elif "access_key_id" in creds_json and "secret_access_key" in creds_json:
                access_key = creds_json["access_key_id"]
                secret_key = creds_json["secret_access_key"]
                creds_found = True
            elif "Access key ID" in creds_json and "Secret access key" in creds_json:
                # Handle AWS Console CSV format with spaces
                access_key = creds_json["Access key ID"]
                secret_key = creds_json["Secret access key"]
                creds_found = True
            else:
                logger.error(f"Could not find credential keys in JSON. Available keys: {list(creds_json.keys())}")
                return None, "Could not find credential keys in JSON"
                
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in secret: {str(e)}")
            return None, f"Invalid JSON format in secret: {str(e)}"
            
        # Clear any AWS-specific environment variables that could interfere
        aws_env_vars = [
            'AWS_SHARED_CREDENTIALS_FILE', 'AWS_CONFIG_FILE', 
            'AWS_PROFILE', 'AWS_DEFAULT_PROFILE',
            'AWS_WEB_IDENTITY_TOKEN_FILE', 'AWS_ROLE_ARN',
            'AWS_SDK_LOAD_CONFIG', 'AWS_CREDENTIAL_EXPIRATION'
        ]
        
        for env_var in aws_env_vars:
            if env_var in os.environ:
                del os.environ[env_var]
        
        # Set environment variables for AWS SDK
        os.environ['AWS_ACCESS_KEY_ID'] = access_key
        os.environ['AWS_SECRET_ACCESS_KEY'] = secret_key
        
        # Check for optional session token
        if "session_token" in creds_json:
            os.environ['AWS_SESSION_TOKEN'] = creds_json["session_token"]
        elif "SessionToken" in creds_json:
            os.environ['AWS_SESSION_TOKEN'] = creds_json["SessionToken"]
            
        # Set region
        if "region" in creds_json:
            os.environ['AWS_DEFAULT_REGION'] = creds_json["region"]
            os.environ['AWS_REGION'] = creds_json["region"]
        else:
            os.environ['AWS_DEFAULT_REGION'] = 'us-east-1'
            os.environ['AWS_REGION'] = 'us-east-1'
        
        logger.info("Successfully set AWS credentials in environment variables")
        
        # Force boto3 to use a new session with the environment credentials
        boto3.setup_default_session(region_name=os.environ['AWS_DEFAULT_REGION'])
        
        # Test AWS credentials
        try:
            # Add a timeout for STS call
            config = botocore.config.Config(
                region_name=os.environ['AWS_DEFAULT_REGION'],
                connect_timeout=5,
                read_timeout=5,
                retries={'max_attempts': 1}
            )
            session = boto3.session.Session()
            sts = session.client('sts', config=config)
            caller_identity = sts.get_caller_identity()
            account_id = caller_identity['Account']
            
            logger.info(f"Successfully authenticated with AWS using secret: {secret_reference}, account: {account_id}")
            return None, account_id
            
        except ClientError as e:
            error_code = e.response.get('Error', {}).get('Code', 'Unknown')
            error_message = e.response.get('Error', {}).get('Message', str(e))
            logger.error(f"AWS authentication failed with error code {error_code}: {error_message}")
            return None, f"AWS authentication failed: {error_message}"
        except Exception as e:
            logger.error(f"AWS authentication failed: {str(e)}")
            return None, f"AWS authentication failed: {str(e)}"
            
    except Exception as e:
        logger.error(f"Error retrieving credentials from secret reference: {str(e)}")
        logger.error(f"Detailed error: {traceback.format_exc()}")
        return None, f"Error retrieving credentials: {str(e)}"

def get_customer_credentials_from_secret(customer_id):
    """Get customer-specific AWS credentials from Secret Manager"""
    try:
        # Get project ID
        project_id = os.getenv('GCP_PROJECT_ID')
        if not project_id:
            credentials, project_id = default()
        
        # Initialize Secret Manager client
        sm_client = SecretManagerClient(project_id=project_id)
        
        # Get customer credentials
        creds, error = sm_client.get_customer_credentials(customer_id, 'aws')
        if error:
            logger.error(f"Error getting customer credentials: {error}")
            return None, error
        
        # Validate credentials
        if validate_credentials:
            valid, validation_error = validate_credentials('aws', creds)
            if not valid:
                logger.error(f"Invalid credentials: {validation_error}")
                return None, validation_error
        
        # Clear any AWS-specific environment variables that could interfere
        aws_env_vars = [
            'AWS_SHARED_CREDENTIALS_FILE', 'AWS_CONFIG_FILE', 
            'AWS_PROFILE', 'AWS_DEFAULT_PROFILE',
            'AWS_WEB_IDENTITY_TOKEN_FILE', 'AWS_ROLE_ARN',
            'AWS_SDK_LOAD_CONFIG', 'AWS_CREDENTIAL_EXPIRATION'
        ]
        
        for env_var in aws_env_vars:
            if env_var in os.environ:
                del os.environ[env_var]
        
        # Set AWS environment variables
        os.environ['AWS_ACCESS_KEY_ID'] = creds['access_key_id']
        os.environ['AWS_SECRET_ACCESS_KEY'] = creds['secret_access_key']
        
        if 'session_token' in creds:
            os.environ['AWS_SESSION_TOKEN'] = creds['session_token']
        
        if 'region' in creds:
            os.environ['AWS_DEFAULT_REGION'] = creds['region']
            os.environ['AWS_REGION'] = creds['region']
        else:
            os.environ['AWS_DEFAULT_REGION'] = 'us-east-1'
            os.environ['AWS_REGION'] = 'us-east-1'
        
        # Force boto3 to use a new session with the environment credentials
        boto3.setup_default_session(region_name=os.environ['AWS_DEFAULT_REGION'])
        
        # Get account ID
        account_id = creds.get('account_id')
        if not account_id:
            try:
                # Add a timeout for STS call
                config = botocore.config.Config(
                    region_name='us-east-1',
                    connect_timeout=5,
                    read_timeout=5,
                    retries={'max_attempts': 1}
                )
                session = boto3.session.Session()
                sts = session.client('sts', config=config)
                caller_identity = sts.get_caller_identity()
                account_id = caller_identity['Account']
                creds['account_id'] = account_id
                logger.info(f"Retrieved AWS account ID: {account_id}")
            except Exception as e:
                logger.error(f"Error getting account ID: {e}")
                account_id = 'unknown'
        
        logger.info(f"Successfully loaded AWS credentials for customer {customer_id}, account: {account_id}")
        return None, account_id
        
    except Exception as e:
        logger.error(f"Error with Secret Manager authentication: {str(e)}")
        logger.error(f"Detailed error: {traceback.format_exc()}")
        return None, f"Secret Manager authentication failed: {str(e)}"

def get_aws_credentials_from_file():
    """Check AWS credentials files and return account ID"""
    try:
        # First, check if we have credentials in environment variables
        if os.environ.get('AWS_ACCESS_KEY_ID') and os.environ.get('AWS_SECRET_ACCESS_KEY'):
            logger.info("Found AWS credentials in environment variables")
            try:
                # Create a boto3 session with the environment variables
                session = boto3.session.Session(region_name='us-east-1')
                
                # Add a timeout for STS call
                config = botocore.config.Config(
                    region_name='us-east-1',
                    connect_timeout=5,
                    read_timeout=5,
                    retries={'max_attempts': 1}
                )
                sts = session.client('sts', config=config)
                
                caller_identity = sts.get_caller_identity()
                account_id = caller_identity.get('Account')
                
                logger.info(f"Successfully authenticated with AWS using environment credentials")
                logger.info(f"AWS Account ID: {account_id}")
                
                return None, account_id
            except Exception as e:
                logger.error(f"AWS authentication failed with environment variables: {str(e)}")
                # Continue to try with credential files
        
        # Get the AWS profile to use (default to 'default')
        aws_profile = os.getenv('AWS_PROFILE', 'default')
        logger.info(f"Using AWS profile: {aws_profile}")
        
        # Test AWS credentials by calling STS
        try:
            # Create a session with specific profile
            session = boto3.session.Session(profile_name=aws_profile, region_name='us-east-1')
            
            # Add a timeout for STS call
            config = botocore.config.Config(
                region_name='us-east-1',
                connect_timeout=5,
                read_timeout=5,
                retries={'max_attempts': 1}
            )
            sts = session.client('sts', config=config)
            
            caller_identity = sts.get_caller_identity()
            account_id = caller_identity.get('Account')
            
            # Log the caller identity details without credentials
            safe_identity = {k: v for k, v in caller_identity.items() if k != 'Credentials'}
            logger.info(f"Caller identity: {safe_identity}")
            
            # Update global boto3 to use this session
            boto3.setup_default_session(profile_name=aws_profile, region_name='us-east-1')
            
            logger.info(f"Successfully authenticated with AWS using file credentials")
            logger.info(f"AWS Account ID: {account_id}")
            
            return None, account_id
        except Exception as e:
            logger.error(f"AWS authentication failed: {str(e)}")
            logger.error(f"Detailed error: {traceback.format_exc()}")
            return None, f"AWS authentication failed: {str(e)}"
            
    except Exception as e:
        logger.error(f"Error checking AWS credentials: {str(e)}")
        logger.error(f"Detailed error: {traceback.format_exc()}")
        return None, f"Error checking AWS credentials: {str(e)}"

# Modify the get_aws_credentials_from_gcp_secret function to ensure proper credential setup

def get_aws_credentials_from_gcp_secret():
    """Get AWS credentials from GCP Secret Manager in JSON format"""
    try:
        # Initialize Secret Manager client using GCP's default credentials
        credentials, project_id = default()
        secret_client = secretmanager.SecretManagerServiceClient(credentials=credentials)
        
        # Get the customer ID from environment variable
        customer_id = os.getenv('CUSTOMER_ID')
        if not customer_id:
            error_msg = "CUSTOMER_ID environment variable is not set"
            logger.error(error_msg)
            return None, error_msg
        
        # Use same naming pattern as your GCP code
        secret_id = f"sa-aws-{customer_id}-secops".lower().replace('_', '-')
        project_id = os.getenv('PROJECT_ID')
        logger.error(f"project id of env: {project_id}")
        name = f"projects/{project_id}/secrets/{secret_id}/versions/latest"

        logger.info(f"Retrieving AWS credentials from GCP Secret Manager: {secret_id}")
        
        try:
            response = secret_client.access_secret_version(request={"name": name})
            secret_content = response.payload.data.decode("UTF-8")
        except Exception as e:
            logger.error(f"Failed to access secret: {str(e)}")
            logger.error(f"Secret path attempted: {name}")
            return None, f"Failed to access secret: {str(e)}"
        
        # Create authentication folder if it doesn't exist
        try:
            os.makedirs(AUTHENTICATION_FOLDER, mode=0o755, exist_ok=True)
        except Exception as e:
            logger.error(f"Failed to create authentication folder: {str(e)}")
            return None, f"Failed to create authentication folder: {str(e)}"
        
        # Save the content to a file (for reference only)
        file_path = AWS_CREDENTIALS_FILE
        try:
            with open(file_path, 'w') as f:
                f.write(secret_content)
            logger.info(f"Saved AWS credentials to: {file_path}")
        except Exception as e:
            logger.error(f"Failed to write AWS credentials file: {str(e)}")
            return None, f"Failed to write AWS credentials file: {str(e)}"
        
        # Parse JSON credentials
        try:
            creds_json = json.loads(secret_content)
            logger.info("Successfully parsed JSON credentials")
            
            # Look for common key formats in JSON
            creds_found = False
            
            # Search with exact keys
            if "aws_access_key_id" in creds_json and "aws_secret_access_key" in creds_json:
                access_key = creds_json["aws_access_key_id"]
                secret_key = creds_json["aws_secret_access_key"]
                creds_found = True
            elif "AccessKeyId" in creds_json and "SecretAccessKey" in creds_json:
                access_key = creds_json["AccessKeyId"]
                secret_key = creds_json["SecretAccessKey"]
                creds_found = True
            elif "access_key_id" in creds_json and "secret_access_key" in creds_json:
                access_key = creds_json["access_key_id"]
                secret_key = creds_json["secret_access_key"]
                creds_found = True
            else:
                # Case-insensitive matching as fallback
                keys_lower = {k.lower(): k for k in creds_json.keys()}
                if "aws_access_key_id" in keys_lower and "aws_secret_access_key" in keys_lower:
                    access_key = creds_json[keys_lower["aws_access_key_id"]]
                    secret_key = creds_json[keys_lower["aws_secret_access_key"]]
                    creds_found = True
                elif "accesskeyid" in keys_lower and "secretaccesskey" in keys_lower:
                    access_key = creds_json[keys_lower["accesskeyid"]]
                    secret_key = creds_json[keys_lower["secretaccesskey"]]
                    creds_found = True
            
            if not creds_found:
                # Log the keys that were found (without values) to help debug
                logger.error(f"Could not find credential keys in JSON. Available keys: {list(creds_json.keys())}")
                return None, "Could not find credential keys in JSON"
                
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in secret: {str(e)}")
            return None, f"Invalid JSON format in secret: {str(e)}"
        except Exception as e:
            logger.error(f"Error parsing JSON credentials: {str(e)}")
            logger.error(f"Detailed error: {traceback.format_exc()}")
            return None, f"Error parsing credentials: {str(e)}"
                
        # CRITICAL FIX: Clear any AWS-specific environment variables that could interfere
        aws_env_vars = [
            'AWS_SHARED_CREDENTIALS_FILE', 'AWS_CONFIG_FILE', 
            'AWS_PROFILE', 'AWS_DEFAULT_PROFILE',
            'AWS_WEB_IDENTITY_TOKEN_FILE', 'AWS_ROLE_ARN',
            'AWS_SDK_LOAD_CONFIG', 'AWS_CREDENTIAL_EXPIRATION'
        ]
        
        for env_var in aws_env_vars:
            if env_var in os.environ:
                del os.environ[env_var]
        
        # Set environment variables for AWS SDK
        os.environ['AWS_ACCESS_KEY_ID'] = access_key
        os.environ['AWS_SECRET_ACCESS_KEY'] = secret_key
        # Explicitly set the region
        os.environ['AWS_REGION'] = 'us-east-1'
        os.environ['AWS_DEFAULT_REGION'] = 'us-east-1'
        
        # Log success but without the actual credentials
        logger.info("Successfully set AWS credentials in environment variables")
        
        # Force boto3 to use a new session with the environment credentials
        boto3.setup_default_session(region_name='us-east-1')
        
        # Test AWS credentials with explicit region and session settings
        try:
            # Create a fresh client that will pick up the environment variables
            session = boto3.session.Session()
            sts = session.client('sts', region_name='us-east-1')
            caller_identity = sts.get_caller_identity()
            account_id = caller_identity.get('Account')
            
            logger.info(f"Successfully authenticated with AWS using credentials from GCP Secret")
            logger.info(f"AWS Account ID: {account_id}")
            
            return None, account_id
        except ClientError as e:
            error_code = e.response.get('Error', {}).get('Code', 'Unknown')
            error_message = e.response.get('Error', {}).get('Message', str(e))
            logger.error(f"AWS authentication failed with error code {error_code}: {error_message}")
            logger.error(f"Detailed error: {traceback.format_exc()}")
            return None, f"AWS authentication failed: {error_message}"
        except Exception as e:
            logger.error(f"AWS authentication failed: {str(e)}")
            logger.error(f"Detailed error: {traceback.format_exc()}")
            return None, f"AWS authentication failed: {str(e)}"
            
    except Exception as e:
        logger.error(f"Error retrieving AWS credentials from GCP Secret: {str(e)}")
        logger.error(f"Detailed error: {traceback.format_exc()}")
        return None, f"Error retrieving AWS credentials: {str(e)}"