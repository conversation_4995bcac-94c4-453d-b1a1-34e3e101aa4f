from flask import Blueprint, request, jsonify, abort
from azure.identity import DefaultAzureCredential, ClientSecretCredential
from google.auth import default
from google.oauth2 import service_account
from google.cloud import secretmanager
from functools import wraps
import os
import sys
import json
from google.auth.credentials import Credentials
import logging
import subprocess
import traceback
sys.path.append('/app/shared')
try:
    from secret_manager import SecretManagerClient, validate_credentials
except ImportError:
    # Fallback for local development
    SecretManagerClient = None
    validate_credentials = None

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create a blueprint
auth = Blueprint('auth', __name__)

# Use a writable directory for authentication files
AUTHENTICATION_FOLDER = os.path.join('/tmp', 'authentication')
SERVICE_ACCOUNT_FILE = os.path.join(AUTHENTICATION_FOLDER, 'azure-service-account.json')

def get_service_credentials(secret_reference=None, sm_credentials=None):
    try:
        # Get authentication type from environment variables
        auth_type = os.getenv('AUTH_TYPE', 'adc').lower()
        logger.info(f"Using authentication type: {auth_type}")
        
        # If secret_reference is provided, use it directly
        if secret_reference:
            logger.info(f"Using provided secret reference: {secret_reference}")
            return get_credentials_from_secret_reference(secret_reference, sm_credentials)
        elif auth_type == 'adc':
            logger.info("Local deployment detected, using Azure CLI credentials")
            return get_azure_credentials_from_cli()
        else:
            # For non-adc auth in cloud, require secret_reference
            error_msg = "Secret reference is required for cloud deployments. Please provide secret_reference parameter."
            logger.error(error_msg)
            return None, error_msg
            
    except Exception as e:
        logger.error(f"Authentication error: {str(e)}")
        logger.error(f"Detailed error: {traceback.format_exc()}")
        return None, f"Authentication error: {str(e)}"

def get_credentials_from_secret_reference(secret_reference, sm_credentials=None):
    """Get Azure credentials from Secret Manager using secret reference"""
    try:
        # SM credentials are required for accessing Secret Manager
        if not sm_credentials:
            logger.error("No SM credentials provided - this is required for accessing Secret Manager")
            return None, "Service Account credentials are required to access Secret Manager"
            
        # Create credentials from provided JSON
        if isinstance(sm_credentials, dict):
            from google.oauth2 import service_account
            sm_creds = service_account.Credentials.from_service_account_info(sm_credentials)
            project_id = sm_credentials.get('project_id')
            logger.info(f"Using SM credentials for project: {project_id}")
            
            if not project_id:
                logger.error("No project_id found in SM credentials")
                return None, "No project_id found in Service Account credentials"
        else:
            logger.error("Invalid sm_credentials format - expected dict")
            return None, "Invalid sm_credentials format"
            
        secret_client = secretmanager.SecretManagerServiceClient(credentials=sm_creds)
        
        # Construct secret path using the project from SM credentials
        secret_name = f"projects/{project_id}/secrets/{secret_reference}/versions/latest"
        
        logger.info(f"Retrieving Azure credentials from Secret Manager: {secret_reference}")
        
        # Access the secret
        try:
            response = secret_client.access_secret_version(request={"name": secret_name})
            secret_content = response.payload.data.decode("UTF-8")
        except Exception as e:
            logger.error(f"Failed to access secret: {str(e)}")
            logger.error(f"Secret path attempted: {secret_name}")
            return None, f"Failed to access secret: {str(e)}"
            
        # Parse JSON credentials
        try:
            creds_json = json.loads(secret_content)
            logger.info("Successfully parsed JSON credentials")
            
            # Extract Azure credentials
            client_id = creds_json.get('client_id') or creds_json.get('clientId')
            client_secret = creds_json.get('client_secret') or creds_json.get('clientSecret')
            tenant_id = creds_json.get('tenant_id') or creds_json.get('tenantId')
            subscription_id = creds_json.get('subscription_id') or creds_json.get('subscriptionId')
            
            if not all([client_id, client_secret, tenant_id]):
                logger.error(f"Missing required Azure credentials. Found keys: {list(creds_json.keys())}")
                return None, "Missing required Azure credentials"
                
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in secret: {str(e)}")
            return None, f"Invalid JSON format in secret: {str(e)}"
            
        # Set environment variables for Azure SDK
        os.environ['AZURE_CLIENT_ID'] = client_id
        os.environ['AZURE_CLIENT_SECRET'] = client_secret
        os.environ['AZURE_TENANT_ID'] = tenant_id
        if subscription_id:
            os.environ['AZURE_SUBSCRIPTION_ID'] = subscription_id
        
        logger.info("Successfully set Azure credentials in environment variables")
        
        # Create Azure credential object
        try:
            credential = ClientSecretCredential(
                tenant_id=tenant_id,
                client_id=client_id,
                client_secret=client_secret
            )
            logger.info(f"Successfully created Azure credentials from secret: {secret_reference}")
            return credential, subscription_id or "credentials loaded"
            
        except Exception as e:
            logger.error(f"Failed to create Azure credentials: {str(e)}")
            return None, f"Failed to create Azure credentials: {str(e)}"
            
    except Exception as e:
        logger.error(f"Error retrieving credentials from secret reference: {str(e)}")
        logger.error(f"Detailed error: {traceback.format_exc()}")
        return None, f"Error retrieving credentials: {str(e)}"

def get_azure_credentials_from_cli():
    """Get Azure credentials from Azure CLI"""
    try:
        # Try Azure CLI authentication
        credential = DefaultAzureCredential()
        
        # Get subscription ID from az account show
        try:
            result = subprocess.run(
                ["az", "account", "show"],
                capture_output=True,
                text=True,
                check=True
            )
            account_info = json.loads(result.stdout)
            subscription_id = account_info.get('id')
            logger.info(f"Using Azure subscription: {subscription_id}")
            return credential, subscription_id
        except Exception as e:
            logger.warning(f"Could not get subscription from az cli: {e}")
            return credential, None
            
    except Exception as e:
        logger.error(f"Azure CLI authentication failed: {str(e)}")
        return None, f"Azure CLI authentication failed: {str(e)}"

def get_customer_credentials_from_secret(customer_id):
    """Get customer-specific Azure credentials from Secret Manager"""
    try:
        # Get project ID
        project_id = os.getenv('GCP_PROJECT_ID')
        if not project_id:
            credentials, project_id = default()
        
        # Initialize Secret Manager client
        sm_client = SecretManagerClient(project_id=project_id)
        
        # Get customer credentials
        creds, error = sm_client.get_customer_credentials(customer_id, 'azure')
        if error:
            logger.error(f"Error getting customer credentials: {error}")
            return None, error
        
        # Validate credentials
        if validate_credentials:
            valid, validation_error = validate_credentials('azure', creds)
            if not valid:
                logger.error(f"Invalid credentials: {validation_error}")
                return None, validation_error
        
        # Clear any Azure-specific environment variables that could interfere
        azure_env_vars = [
            'AZURE_SUBSCRIPTION_ID', 'AZURE_TENANT_ID', 
            'AZURE_CLIENT_ID', 'AZURE_CLIENT_SECRET',
            'AZURE_CREDENTIAL_SCOPE'
        ]
        
        for env_var in azure_env_vars:
            if env_var in os.environ:
                del os.environ[env_var]
        
        # Extract Azure service principal credentials
        client_id = creds['client_id']
        client_secret = creds['client_secret']
        tenant_id = creds['tenant_id']
        
        # Create Azure client credentials
        azure_credentials = ClientSecretCredential(
            tenant_id=tenant_id,
            client_id=client_id,
            client_secret=client_secret
        )
        
        # Store these values in environment variables for other components
        os.environ['AZURE_TENANT_ID'] = tenant_id
        os.environ['AZURE_CLIENT_ID'] = client_id
        os.environ['AZURE_CLIENT_SECRET'] = client_secret
        
        # Also store subscription_id if provided
        if 'subscription_id' in creds:
            os.environ['AZURE_SUBSCRIPTION_ID'] = creds['subscription_id']
        
        logger.info(f"Successfully loaded Azure credentials for customer {customer_id}")
        return azure_credentials
        
    except Exception as e:
        logger.error(f"Error with Secret Manager authentication: {str(e)}")
        logger.error(f"Detailed error: {traceback.format_exc()}")
        return None, f"Secret Manager authentication failed: {str(e)}"
