events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Include authentication config if it exists
    include /etc/nginx/conf.d/*.conf;

    # Logging settings
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;

    # Set global timeout values
    client_body_timeout 1000s;
    client_header_timeout 1000s;
    send_timeout 1000s;
    proxy_connect_timeout 1000s;
    proxy_read_timeout 1000s;
    proxy_send_timeout 1000s;

    # Enable keepalive connections
    keepalive_timeout 65;
    keepalive_requests 100;

    # Define upstream servers for your backends
    server {
        listen 8081;
        server_name _;
        
        # Root directory for static files
        root /usr/share/nginx/html;
        index index.html;

        # Health check endpoint for Cloud Run
        location /health {
            access_log off;
            return 200 'healthy\n';
        }

        # Handle frontend routes
        location / {
            try_files $uri $uri/ /index.html;
            add_header Cache-Control "no-cache";
        }

        # Proxy requests to GCP backend
        location /api/gcp/ {
            # DNS resolver for Cloud Run
            resolver 8.8.8.8 8.8.4.4 valid=300s;
            
            # Use identity token for service-to-service auth if available, otherwise forward original
            # MUST be before rewrite as $uri changes after rewrite
            set $auth_header $auth_token;
            if ($auth_header = "") {
                set $auth_header $http_authorization;
            }
            
            # Don't rewrite - backend expects full path including /api/gcp/
            
            proxy_pass $BACKEND_GCP_URL;
            proxy_http_version 1.1;
            
            # Debug logging
            add_header X-Debug-Auth-Token $auth_token always;
            add_header X-Debug-Auth-Header $auth_header always;
            
            proxy_set_header Authorization $auth_header;
            proxy_set_header Host $proxy_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Prevent redirect loops
            proxy_redirect off;
            proxy_buffering off;
            
            # CORS headers
            add_header 'Access-Control-Allow-Origin' '*' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE' always;
            add_header 'Access-Control-Allow-Headers' '*' always;
            add_header 'Access-Control-Allow-Credentials' 'true' always;

            # Handle preflight requests
            if ($request_method = 'OPTIONS') {
                add_header 'Access-Control-Allow-Origin' '*';
                add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE';
                add_header 'Access-Control-Allow-Headers' '*';
                add_header 'Access-Control-Allow-Credentials' 'true';
                add_header 'Access-Control-Max-Age' 1728000;
                add_header 'Content-Type' 'text/plain charset=UTF-8';
                add_header 'Content-Length' 0;
                return 204;
            }
        }

        # Proxy requests to AWS backend
        location /api/aws/ {
            # DNS resolver for Cloud Run
            resolver 8.8.8.8 8.8.4.4 valid=300s;
            
            # Use identity token for service-to-service auth if available, otherwise forward original
            # MUST be before rewrite as $uri changes after rewrite
            set $auth_header $auth_token;
            if ($auth_header = "") {
                set $auth_header $http_authorization;
            }
            
            # Don't rewrite - backend expects full path including /api/aws/
            
            proxy_pass $BACKEND_AWS_URL;
            proxy_http_version 1.1;
            
            # Debug logging
            add_header X-Debug-Auth-Token $auth_token always;
            add_header X-Debug-Auth-Header $auth_header always;
            
            proxy_set_header Authorization $auth_header;
            proxy_set_header Host $proxy_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Prevent redirect loops
            proxy_redirect off;
            proxy_buffering off;
            
            # CORS headers
            add_header 'Access-Control-Allow-Origin' '*' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE' always;
            add_header 'Access-Control-Allow-Headers' '*' always;
            add_header 'Access-Control-Allow-Credentials' 'true' always;

            # Handle preflight requests
            if ($request_method = 'OPTIONS') {
                add_header 'Access-Control-Allow-Origin' '*';
                add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE';
                add_header 'Access-Control-Allow-Headers' '*';
                add_header 'Access-Control-Allow-Credentials' 'true';
                add_header 'Access-Control-Max-Age' 1728000;
                add_header 'Content-Type' 'text/plain charset=UTF-8';
                add_header 'Content-Length' 0;
                return 204;
            }
        }

        # Proxy requests to Azure backend
        location /api/azure/ {
            # DNS resolver for Cloud Run
            resolver 8.8.8.8 8.8.4.4 valid=300s;
            
            # Use identity token for service-to-service auth if available, otherwise forward original
            # MUST be before rewrite as $uri changes after rewrite
            set $auth_header $auth_token;
            if ($auth_header = "") {
                set $auth_header $http_authorization;
            }
            
            # Don't rewrite - backend expects full path including /api/azure/
            
            proxy_pass $BACKEND_AZURE_URL;
            proxy_http_version 1.1;
            
            # Debug logging
            add_header X-Debug-Auth-Token $auth_token always;
            add_header X-Debug-Auth-Header $auth_header always;
            
            proxy_set_header Authorization $auth_header;
            proxy_set_header Host $proxy_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Prevent redirect loops
            proxy_redirect off;
            proxy_buffering off;
            
            # CORS headers
            add_header 'Access-Control-Allow-Origin' '*' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE' always;
            add_header 'Access-Control-Allow-Headers' '*' always;
            add_header 'Access-Control-Allow-Credentials' 'true' always;

            # Handle preflight requests
            if ($request_method = 'OPTIONS') {
                add_header 'Access-Control-Allow-Origin' '*';
                add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE';
                add_header 'Access-Control-Allow-Headers' '*';
                add_header 'Access-Control-Allow-Credentials' 'true';
                add_header 'Access-Control-Max-Age' 1728000;
                add_header 'Content-Type' 'text/plain charset=UTF-8';
                add_header 'Content-Length' 0;
                return 204;
            }
        }

        # Custom error pages
        error_page 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html;
            internal;
        }
    }
}