# Use an official Node.js runtime as a parent image
FROM node:18 as build

WORKDIR /

COPY package*.json ./
# Add web-vitals installation to the build stage
RUN npm install && npm install web-vitals --save
RUN npm install lucide-react
RUN npm install --save-dev @babel/plugin-proposal-private-property-in-object
RUN npm install xlsx

COPY . .

RUN npm run build

# Use an official Nginx image as a parent image
FROM nginx:stable-alpine

ENV BACKEND_GCP_URL='http://localhost:8080'
ENV BACKEND_AWS_URL='http://localhost:8082'
ENV BACKEND_AZURE_URL='http://localhost:8083'

# Copy the nginx template instead of the direct config
COPY ./nginx.conf /etc/nginx/nginx.conf
COPY ./nginx.conf /etc/nginx/nginx.conf.template

# Copy the build output to the Nginx HTML directory
COPY --from=build /build /usr/share/nginx/html

# Copy nginx configs
COPY nginx-cloudrun.conf /etc/nginx/nginx-cloudrun.conf

# Copy and set up entrypoint scripts
COPY entrypoint.sh /entrypoint.sh
COPY entrypoint-cloudrun.sh /entrypoint-cloudrun.sh
COPY entrypoint-cloudrun-auth.sh /entrypoint-cloudrun-auth.sh
RUN chmod +x /entrypoint.sh /entrypoint-cloudrun.sh /entrypoint-cloudrun-auth.sh

EXPOSE 8081

# Use authentication-enabled entrypoint for Cloud Run
ENTRYPOINT ["/entrypoint-cloudrun-auth.sh"]
