<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Steampipe Compliance Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #0a0e1a;
            color: #e4e6eb;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background-color: #1c2333;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .header h1 {
            color: #3b82f6;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #9ca3af;
            font-size: 1.1rem;
        }
        
        .cloud-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .cloud-card {
            background-color: #1c2333;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
            border: 1px solid #374151;
        }
        
        .cloud-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        
        .cloud-title {
            font-size: 1.5rem;
            color: #3b82f6;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .cloud-icon {
            width: 32px;
            height: 32px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #10b981;
        }
        
        .status-indicator.offline {
            background-color: #ef4444;
        }
        
        .benchmarks-section {
            margin-top: 15px;
        }
        
        .benchmark-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background-color: #0f172a;
            margin-bottom: 10px;
            border-radius: 4px;
            border: 1px solid #334155;
        }
        
        .benchmark-name {
            color: #e4e6eb;
            font-weight: 500;
        }
        
        .run-button {
            background-color: #3b82f6;
            color: white;
            border: none;
            padding: 6px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }
        
        .run-button:hover {
            background-color: #2563eb;
        }
        
        .run-button:disabled {
            background-color: #4b5563;
            cursor: not-allowed;
        }
        
        .auth-section {
            background-color: #0f172a;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border: 1px solid #334155;
        }
        
        .auth-input {
            width: 100%;
            padding: 8px 12px;
            background-color: #1c2333;
            border: 1px solid #374151;
            color: #e4e6eb;
            border-radius: 4px;
            margin-top: 8px;
        }
        
        .jobs-section {
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .job-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px;
            background-color: #0f172a;
            margin-bottom: 8px;
            border-radius: 4px;
            font-size: 14px;
            border: 1px solid #334155;
        }
        
        .job-status {
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .job-status.completed {
            background-color: #065f46;
            color: #10b981;
        }
        
        .job-status.failed {
            background-color: #7f1d1d;
            color: #ef4444;
        }
        
        .job-status.running {
            background-color: #7c2d12;
            color: #f59e0b;
        }
        
        .loading {
            display: inline-block;
            width: 14px;
            height: 14px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Steampipe Compliance Dashboard</h1>
            <p>Unified compliance scanning across AWS, Azure, and GCP</p>
        </div>
        
        <div class="cloud-grid">
            <!-- GCP Card -->
            <div class="cloud-card">
                <div class="cloud-header">
                    <div class="cloud-title">
                        <img src="data:image/svg+xml;base64,PHN2ZyB2aWV3Qm94PSIwIDAgMjQgMjQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEyIDJDNi40OCAyIDIgNi40OCAyIDEyczQuNDggMTAgMTAgMTAgMTAtNC40OCAxMC0xMFMxNy41MiAyIDEyIDJ6bTAgMThjLTQuNDIgMC04LTMuNTgtOC04czMuNTgtOCA4LTggOCAzLjU4IDggOC0zLjU4IDgtOCA4eiIgZmlsbD0iIzQyODVGNCIvPjwvc3ZnPg==" class="cloud-icon" alt="GCP">
                        Google Cloud Platform
                    </div>
                    <div class="status-indicator" id="gcp-status"></div>
                </div>
                
                <div class="auth-section">
                    <label>Secret Reference:</label>
                    <input type="text" class="auth-input" id="gcp-secret" placeholder="e.g., gcp-local-secops" value="gcp-local-secops">
                </div>
                
                <div class="benchmarks-section">
                    <h3 style="margin-bottom: 10px; color: #9ca3af;">Available Benchmarks</h3>
                    <div class="benchmark-item">
                        <span class="benchmark-name">CIS v3.0.0</span>
                        <button class="run-button" onclick="runBenchmark('gcp', 'cis_v300')">Run</button>
                    </div>
                    <div class="benchmark-item">
                        <span class="benchmark-name">CIS v2.0.0</span>
                        <button class="run-button" onclick="runBenchmark('gcp', 'cis_v200')">Run</button>
                    </div>
                    <div class="benchmark-item">
                        <span class="benchmark-name">CIS v1.2.0</span>
                        <button class="run-button" onclick="runBenchmark('gcp', 'cis_v120')">Run</button>
                    </div>
                    <div class="benchmark-item">
                        <span class="benchmark-name">NIST 800-53 Rev 5</span>
                        <button class="run-button" onclick="runBenchmark('gcp', 'nist_800_53_rev_5')">Run</button>
                    </div>
                    <div class="benchmark-item">
                        <span class="benchmark-name">NIST CSF v1.0</span>
                        <button class="run-button" onclick="runBenchmark('gcp', 'nist_csf_v10')">Run</button>
                    </div>
                </div>
                
                <div class="jobs-section" id="gcp-jobs">
                    <h3 style="margin-bottom: 10px; color: #9ca3af;">Recent Jobs</h3>
                </div>
            </div>
            
            <!-- AWS Card -->
            <div class="cloud-card">
                <div class="cloud-header">
                    <div class="cloud-title">
                        <img src="data:image/svg+xml;base64,PHN2ZyB2aWV3Qm94PSIwIDAgMjQgMjQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEyIDJDNi40OCAyIDIgNi40OCAyIDEyczQuNDggMTAgMTAgMTAgMTAtNC40OCAxMC0xMFMxNy41MiAyIDEyIDJ6bTAgMThjLTQuNDIgMC04LTMuNTgtOC04czMuNTgtOCA4LTggOCAzLjU4IDggOC0zLjU4IDgtOCA4eiIgZmlsbD0iI0ZGOTkwMCIvPjwvc3ZnPg==" class="cloud-icon" alt="AWS">
                        Amazon Web Services
                    </div>
                    <div class="status-indicator" id="aws-status"></div>
                </div>
                
                <div class="auth-section">
                    <label>Secret Reference:</label>
                    <input type="text" class="auth-input" id="aws-secret" placeholder="e.g., aws-local-secops" value="aws-local-secops">
                </div>
                
                <div class="benchmarks-section">
                    <h3 style="margin-bottom: 10px; color: #9ca3af;">Available Benchmarks</h3>
                    <div class="benchmark-item">
                        <span class="benchmark-name">CIS v3.0.0</span>
                        <button class="run-button" onclick="runBenchmark('aws', 'cis_v300')">Run</button>
                    </div>
                    <div class="benchmark-item">
                        <span class="benchmark-name">CIS v2.0.0</span>
                        <button class="run-button" onclick="runBenchmark('aws', 'cis_v200')">Run</button>
                    </div>
                    <div class="benchmark-item">
                        <span class="benchmark-name">CIS v1.5.0</span>
                        <button class="run-button" onclick="runBenchmark('aws', 'cis_v150')">Run</button>
                    </div>
                    <div class="benchmark-item">
                        <span class="benchmark-name">Foundational Security</span>
                        <button class="run-button" onclick="runBenchmark('aws', 'foundational_security')">Run</button>
                    </div>
                    <div class="benchmark-item">
                        <span class="benchmark-name">NIST 800-53 Rev 5</span>
                        <button class="run-button" onclick="runBenchmark('aws', 'nist_800_53_rev_5')">Run</button>
                    </div>
                </div>
                
                <div class="jobs-section" id="aws-jobs">
                    <h3 style="margin-bottom: 10px; color: #9ca3af;">Recent Jobs</h3>
                </div>
            </div>
            
            <!-- Azure Card -->
            <div class="cloud-card">
                <div class="cloud-header">
                    <div class="cloud-title">
                        <img src="data:image/svg+xml;base64,PHN2ZyB2aWV3Qm94PSIwIDAgMjQgMjQiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEyIDJDNi40OCAyIDIgNi40OCAyIDEyczQuNDggMTAgMTAgMTAgMTAtNC40OCAxMC0xMFMxNy41MiAyIDEyIDJ6bTAgMThjLTQuNDIgMC04LTMuNTgtOC04czMuNTgtOCA4LTggOCAzLjU4IDggOC0zLjU4IDgtOCA4eiIgZmlsbD0iIzAwNzhENCIvPjwvc3ZnPg==" class="cloud-icon" alt="Azure">
                        Microsoft Azure
                    </div>
                    <div class="status-indicator" id="azure-status"></div>
                </div>
                
                <div class="auth-section">
                    <label>Secret Reference:</label>
                    <input type="text" class="auth-input" id="azure-secret" placeholder="e.g., azure-local-secops" value="azure-local-secops">
                </div>
                
                <div class="benchmarks-section">
                    <h3 style="margin-bottom: 10px; color: #9ca3af;">Available Benchmarks</h3>
                    <div class="benchmark-item">
                        <span class="benchmark-name">CIS v2.1.0</span>
                        <button class="run-button" onclick="runBenchmark('azure', 'cis_v210')">Run</button>
                    </div>
                    <div class="benchmark-item">
                        <span class="benchmark-name">CIS v2.0.0</span>
                        <button class="run-button" onclick="runBenchmark('azure', 'cis_v200')">Run</button>
                    </div>
                    <div class="benchmark-item">
                        <span class="benchmark-name">CIS v1.3.0</span>
                        <button class="run-button" onclick="runBenchmark('azure', 'cis_v130')">Run</button>
                    </div>
                    <div class="benchmark-item">
                        <span class="benchmark-name">HIPAA HITRUST v9.2</span>
                        <button class="run-button" onclick="runBenchmark('azure', 'hipaa_hitrust_v92')">Run</button>
                    </div>
                    <div class="benchmark-item">
                        <span class="benchmark-name">NIST SP 800-53 Rev 5</span>
                        <button class="run-button" onclick="runBenchmark('azure', 'nist_sp_800_53_rev_5')">Run</button>
                    </div>
                </div>
                
                <div class="jobs-section" id="azure-jobs">
                    <h3 style="margin-bottom: 10px; color: #9ca3af;">Recent Jobs</h3>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        const API_ENDPOINTS = {
            gcp: 'http://localhost:8080',
            aws: 'http://localhost:8082',
            azure: 'http://localhost:8083'
        };
        
        // Check service status
        async function checkServiceStatus(provider) {
            try {
                const response = await fetch(`${API_ENDPOINTS[provider]}/api/${provider}/test-auth`);
                const statusIndicator = document.getElementById(`${provider}-status`);
                if (response.ok) {
                    statusIndicator.classList.remove('offline');
                } else {
                    statusIndicator.classList.add('offline');
                }
            } catch (error) {
                document.getElementById(`${provider}-status`).classList.add('offline');
            }
        }
        
        // Run benchmark
        async function runBenchmark(provider, benchmark) {
            const secretInput = document.getElementById(`${provider}-secret`);
            const secretReference = secretInput.value;
            
            if (!secretReference) {
                alert('Please enter a secret reference');
                return;
            }
            
            const button = event.target;
            button.disabled = true;
            button.innerHTML = 'Running... <span class="loading"></span>';
            
            try {
                const payload = {
                    secret_reference: secretReference,
                    benchmark: benchmark
                };
                
                // Add provider-specific parameters
                if (provider === 'aws') {
                    payload.account_id = 'all-accounts';
                } else if (provider === 'azure') {
                    payload.subscription_id = 'all-subscriptions';
                } else if (provider === 'gcp') {
                    payload.project_id = 'all-projects';
                }
                
                const response = await fetch(`${API_ENDPOINTS[provider]}/api/${provider}/benchmark`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(payload)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    alert(`Benchmark started! Job ID: ${data.job_id}`);
                    refreshJobs(provider);
                } else {
                    alert(`Error: ${data.error || 'Failed to start benchmark'}`);
                }
            } catch (error) {
                alert(`Error: ${error.message}`);
            } finally {
                button.disabled = false;
                button.textContent = 'Run';
            }
        }
        
        // Refresh job list
        async function refreshJobs(provider) {
            try {
                const response = await fetch(`${API_ENDPOINTS[provider]}/api/${provider}/jobs`);
                const data = await response.json();
                
                const jobsContainer = document.getElementById(`${provider}-jobs`);
                const jobs = data.jobs || [];
                
                if (jobs.length === 0) {
                    jobsContainer.innerHTML = '<h3 style="margin-bottom: 10px; color: #9ca3af;">Recent Jobs</h3><p style="color: #6b7280;">No jobs found</p>';
                    return;
                }
                
                let html = '<h3 style="margin-bottom: 10px; color: #9ca3af;">Recent Jobs</h3>';
                
                jobs.slice(0, 10).forEach(job => {
                    const statusClass = job.status || 'running';
                    const createdAt = new Date(job.created_at).toLocaleString();
                    
                    html += `
                        <div class="job-item">
                            <div>
                                <div style="font-weight: 500;">${job.benchmark}</div>
                                <div style="font-size: 12px; color: #6b7280;">${createdAt}</div>
                            </div>
                            <span class="job-status ${statusClass}">${statusClass}</span>
                        </div>
                    `;
                });
                
                jobsContainer.innerHTML = html;
            } catch (error) {
                console.error(`Error fetching jobs for ${provider}:`, error);
            }
        }
        
        // Initialize
        function init() {
            // Check status for all providers
            ['gcp', 'aws', 'azure'].forEach(provider => {
                checkServiceStatus(provider);
                refreshJobs(provider);
            });
            
            // Refresh every 30 seconds
            setInterval(() => {
                ['gcp', 'aws', 'azure'].forEach(provider => {
                    checkServiceStatus(provider);
                    refreshJobs(provider);
                });
            }, 30000);
        }
        
        // Start when page loads
        window.addEventListener('load', init);
    </script>
</body>
</html>