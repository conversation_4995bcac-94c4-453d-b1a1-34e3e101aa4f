{"version": 3, "file": "static/js/488.2bc03c96.chunk.js", "mappings": "gUAAA,IAAIA,GAAG,EAAE,MAAMC,EAAEA,IAAIC,iBAAiB,YAAYC,IAAIA,EAAEC,YAAYJ,EAAEG,EAAEE,UAAUJ,EAAEE,GAAG,IAAG,EAAG,EAAEA,EAAEA,CAACH,EAAEC,EAAEE,EAAEG,KAAK,IAAIC,EAAEC,EAAE,OAAOC,IAAIR,EAAES,OAAO,IAAID,GAAGH,KAAKE,EAAEP,EAAES,OAAOH,GAAG,IAAIC,QAAG,IAASD,KAAKA,EAAEN,EAAES,MAAMT,EAAEU,MAAMH,EAAEP,EAAEW,OAAO,EAAEZ,EAAEC,IAAID,EAAEC,EAAE,GAAG,OAAOD,EAAEC,EAAE,GAAG,oBAAoB,OAAjD,CAAyDA,EAAES,MAAMP,GAAGH,EAAEC,IAAI,CAAC,EAAEK,EAAEN,IAAIa,uBAAuB,IAAIA,uBAAuB,IAAIb,OAAO,EAAEO,EAAEA,KAAK,MAAMP,EAAEc,YAAYC,iBAAiB,cAAc,GAAG,GAAGf,GAAGA,EAAEgB,cAAc,GAAGhB,EAAEgB,cAAcF,YAAYG,MAAM,OAAOjB,CAAC,EAAEQ,EAAEA,KAAK,MAAMR,EAAEO,IAAI,OAAOP,GAAGkB,iBAAiB,CAAC,EAAET,EAAE,SAACR,GAAS,IAAPE,EAACgB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,IAAE,EAAK,MAAMb,EAAEC,IAAI,IAAIE,EAAE,WAA8J,OAAnJT,GAAG,EAAES,EAAE,qBAAqBH,IAAIgB,SAASC,cAAcf,IAAI,EAAEC,EAAE,YAAYa,SAASE,aAAaf,EAAE,UAAUH,EAAEmB,OAAOhB,EAAEH,EAAEmB,KAAKC,QAAQ,KAAK,OAAa,CAACC,KAAK1B,EAAES,MAAMP,EAAES,OAAO,OAAOD,MAAM,EAAEiB,QAAQ,GAAGC,GAAG,MAAMC,KAAKb,SAASc,KAAKC,MAAM,cAAcD,KAAKE,UAAU,OAAOC,eAAezB,EAAE,EAAE0B,EAAE,IAAIC,QAAQ,SAASC,EAAErC,EAAEC,GAAG,OAAOkC,EAAEG,IAAItC,IAAImC,EAAEI,IAAIvC,EAAE,IAAIC,GAAGkC,EAAEG,IAAItC,EAAE,CAAC,MAAMwC,EAAEvC,EAAEK,EAAE,EAAEC,EAAE,GAAGkC,CAAAA,CAAEzC,GAAG,GAAGA,EAAE0C,eAAe,OAAO,MAAMzC,EAAE0C,KAAKpC,EAAE,GAAGJ,EAAEwC,KAAKpC,EAAEqC,IAAI,GAAGD,KAAKrC,GAAGL,GAAGE,GAAGH,EAAE6C,UAAU1C,EAAE0C,UAAU,KAAK7C,EAAE6C,UAAU5C,EAAE4C,UAAU,KAAKF,KAAKrC,GAAGN,EAAEU,MAAMiC,KAAKpC,EAAEuC,KAAK9C,KAAK2C,KAAKrC,EAAEN,EAAEU,MAAMiC,KAAKpC,EAAE,CAACP,IAAI2C,KAAK1C,IAAID,EAAE,EAAE,MAAMyC,EAAE,SAACzC,EAAEC,GAAS,IAAPE,EAACgB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAC,CAAC,EAAK,IAAI,GAAG4B,oBAAoBC,oBAAoBC,SAASjD,GAAG,CAAC,MAAMM,EAAE,IAAIyC,qBAAqB/C,IAAIkD,QAAQC,UAAUC,MAAM,KAAKnD,EAAED,EAAEqD,aAAa,GAAG,IAAI,OAAO/C,EAAEgD,QAAQ,CAAC7B,KAAKzB,EAAEuD,UAAS,KAAMpD,IAAIG,CAAC,CAAC,CAAC,MAAM,CAAC,EAAEkD,EAAExD,IAAI,IAAIC,GAAE,EAAG,MAAM,KAAKA,IAAID,IAAIC,GAAE,EAAG,CAAC,EAAE,IAAIwD,GAAG,EAAE,MAAMC,EAAEA,IAAI,WAAWpC,SAASqC,iBAAiBrC,SAASC,aAAa,IAAI,EAAEqC,EAAE5D,IAAI,WAAWsB,SAASqC,iBAAiBF,GAAG,IAAIA,EAAE,qBAAqBzD,EAAEyB,KAAKzB,EAAEK,UAAU,EAAEwD,IAAI,EAAEC,EAAEA,KAAK5D,iBAAiB,mBAAmB0D,GAAE,GAAI1D,iBAAiB,qBAAqB0D,GAAE,EAAG,EAAEC,EAAEA,KAAKE,oBAAoB,mBAAmBH,GAAE,GAAIG,oBAAoB,qBAAqBH,GAAE,EAAG,EAAEI,EAAEA,KAAK,GAAGP,EAAE,EAAE,CAAC,MAAMzD,EAAEQ,IAAIL,EAAEmB,SAASC,kBAAa,EAAO0C,WAAWnD,YAAYC,iBAAiB,oBAAoBmD,QAAQjE,GAAG,WAAWA,EAAE0B,MAAM1B,EAAE4C,UAAU7C,IAAI,IAAI6C,UAAUY,EAAEtD,GAAGuD,IAAII,IAAI7D,GAAG,KAAKkE,YAAY,KAAKV,EAAEC,IAAII,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,mBAAIM,GAAkB,OAAOX,CAAC,EAAE,EAAEY,EAAErE,IAAIsB,SAASC,aAAarB,iBAAiB,sBAAsB,IAAIF,MAAK,GAAIA,GAAG,EAAEsE,EAAE,CAAC,KAAK,KAAKC,EAAE,SAACvE,GAAS,IAAPO,EAACY,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAC,CAAC,EAAKkD,GAAG,KAAK,MAAMlC,EAAE6B,IAAI,IAAI3B,EAAEG,EAAE/B,EAAE,OAAO,MAAM+C,EAAEf,EAAE,SAASzC,IAAI,IAAI,MAAMC,KAAKD,EAAE,2BAA2BC,EAAE0B,OAAO6B,EAAEgB,aAAavE,EAAE4C,UAAUV,EAAEiC,kBAAkB5B,EAAE9B,MAAMqB,KAAK0C,IAAIxE,EAAE4C,UAAUrC,IAAI,GAAGgC,EAAEZ,QAAQkB,KAAK7C,GAAGoC,GAAE,IAAK,IAAImB,IAAInB,EAAElC,EAAEH,EAAEwC,EAAE8B,EAAE/D,EAAEmE,kBAAkBzE,GAAGA,IAAIuC,EAAE/B,EAAE,OAAO4B,EAAElC,EAAEH,EAAEwC,EAAE8B,EAAE/D,EAAEmE,kBAAkBpE,GAAG,KAAKkC,EAAE9B,MAAMI,YAAYG,MAAMhB,EAAEI,UAAUgC,GAAE,EAAG,GAAG,IAAI,GAAG,EAAEsC,EAAE,CAAC,GAAG,KAAKC,EAAE,SAAC5E,GAAS,IAAPO,EAACY,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAC,CAAC,EAAKoD,EAAEf,GAAG,KAAK,IAAIhD,EAAE2B,EAAE1B,EAAE,MAAM,GAAG,MAAM+C,EAAEnB,EAAE9B,EAAEiC,GAAGiB,EAAEzD,IAAI,IAAI,MAAMC,KAAKD,EAAEwD,EAAEf,EAAExC,GAAGuD,EAAElD,EAAE6B,EAAEzB,QAAQyB,EAAEzB,MAAM8C,EAAElD,EAAE6B,EAAEP,QAAQ4B,EAAEjD,EAAEC,IAAI,EAAEkD,EAAEjB,EAAE,eAAegB,GAAGC,IAAIlD,EAAEL,EAAEH,EAAEmC,EAAEwC,EAAEpE,EAAEmE,kBAAkBpD,SAASpB,iBAAiB,oBAAoB,KAAK,WAAWoB,SAASqC,kBAAkBF,EAAEC,EAAEmB,eAAerE,GAAE,GAAI,IAAIP,GAAG,KAAKuD,EAAElD,EAAE,EAAE6B,EAAE1B,EAAE,MAAM,GAAGD,EAAEL,EAAEH,EAAEmC,EAAEwC,EAAEpE,EAAEmE,kBAAkBpE,GAAG,IAAIE,KAAK,IAAI2D,WAAW3D,GAAG,IAAI,EAAE,IAAIsE,EAAE,EAAEC,EAAE,IAAIC,EAAE,EAAE,MAAMC,EAAEjF,IAAI,IAAI,MAAMC,KAAKD,EAAEC,EAAEiF,gBAAgBH,EAAEhD,KAAKoD,IAAIJ,EAAE9E,EAAEiF,eAAeF,EAAEjD,KAAK0C,IAAIO,EAAE/E,EAAEiF,eAAeJ,EAAEE,GAAGA,EAAED,GAAG,EAAE,EAAE,EAAE,EAAE,IAAIK,EAAE,MAAMC,EAAEA,IAAID,EAAEN,EAAEhE,YAAYwE,kBAAkB,EAA+G,IAAIC,EAAE,EAAE,MAAMC,EAAE/B,EAAE,GAAGC,EAAC,KAAC,IAAI+B,IAAL,GAAS7B,EAAEC,EAAEG,CAAAA,GAAIuB,EAAEF,IAAI1C,KAAKc,EAAErC,OAAO,EAAEuB,KAAKe,EAAEgC,OAAO,CAACnB,CAAAA,GAAI,MAAMvE,EAAE+B,KAAKoD,IAAIxC,KAAKc,EAAErC,OAAO,EAAEW,KAAKC,OAAOqD,IAAIE,GAAG,KAAK,OAAO5C,KAAKc,EAAEzD,EAAE,CAACyC,CAAAA,CAAEzC,GAAG,GAAG2C,KAAKiB,IAAI5D,IAAIA,EAAEkF,eAAe,gBAAgBlF,EAAE2F,UAAU,OAAO,MAAM1F,EAAE0C,KAAKc,EAAEb,IAAI,GAAG,IAAIzC,EAAEwC,KAAKe,EAAEpB,IAAItC,EAAEkF,eAAe,GAAG/E,GAAGwC,KAAKc,EAAErC,OAAO,IAAIpB,EAAE4F,SAAS3F,EAAE0E,EAAE,CAAC,GAAGxE,EAAEH,EAAE4F,SAASzF,EAAEwE,GAAGxE,EAAEyB,QAAQ,CAAC5B,GAAGG,EAAEwE,EAAE3E,EAAE4F,UAAU5F,EAAE4F,WAAWzF,EAAEwE,GAAG3E,EAAE6C,YAAY1C,EAAEyB,QAAQ,GAAGiB,WAAW1C,EAAEyB,QAAQkB,KAAK9C,IAAIG,EAAE,CAAC0B,GAAG7B,EAAEkF,cAActD,QAAQ,CAAC5B,GAAG2E,EAAE3E,EAAE4F,UAAUjD,KAAKe,EAAEnB,IAAIpC,EAAE0B,GAAG1B,GAAGwC,KAAKc,EAAEX,KAAK3C,IAAIwC,KAAKc,EAAEoC,MAAM,CAAC7F,EAAEC,IAAIA,EAAE0E,EAAE3E,EAAE2E,IAAIhC,KAAKc,EAAErC,OAAO,GAAG,CAAC,MAAMpB,EAAE2C,KAAKc,EAAEqC,OAAO,IAAI,IAAI,MAAM7F,KAAKD,EAAE2C,KAAKe,EAAEqC,OAAO9F,EAAE4B,GAAG,CAACc,KAAKkB,IAAI1D,EAAE,CAAC,EAAE,MAAM6F,EAAEhG,IAAI,MAAMC,EAAEgE,WAAWgC,qBAAqB9B,WAAW,WAAW7C,SAASqC,gBAAgB3D,KAAKA,EAAEwD,EAAExD,GAAGsB,SAASpB,iBAAiB,mBAAmBF,EAAE,CAACkG,MAAK,IAAKjG,GAAG,KAAKD,IAAIsB,SAASyC,oBAAoB,mBAAmB/D,EAAE,IAAI,EAAEmG,EAAE,CAAC,IAAI,KAAKC,EAAE,SAACpG,GAAS,IAAPM,EAACa,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAC,CAAC,EAAK8C,WAAWoC,wBAAwB,kBAAkBA,uBAAuBC,WAAWjC,GAAG,KAAvlC,qBAAqBvD,aAAasE,IAAIA,EAAE3C,EAAE,QAAQwC,EAAE,CAACxD,KAAK,QAAQ8B,UAAS,EAAGgD,kBAAkB,KAAggC,IAAIhG,EAAEC,EAAEC,EAAE,OAAO,MAAM0B,EAAEE,EAAE/B,EAAEkF,GAAGhD,EAAExC,IAAIgG,GAAG,KAAK,IAAI,MAAM/F,KAAKD,EAAEmC,EAAEM,EAAExC,GAAG,MAAMA,EAAEkC,EAAEoC,IAAItE,GAAGA,EAAE0E,IAAInE,EAAEE,QAAQF,EAAEE,MAAMT,EAAE0E,EAAEnE,EAAEoB,QAAQ3B,EAAE2B,QAAQrB,IAAI,GAAG,EAAEiD,EAAEf,EAAE,QAAQD,EAAE,CAAC+D,kBAAkBjG,EAAEiG,mBAAmB,KAAKhG,EAAEJ,EAAEH,EAAEQ,EAAE2F,EAAE7F,EAAEoE,kBAAkBlB,IAAIA,EAAEF,QAAQ,CAAC7B,KAAK,cAAc8B,UAAS,IAAKjC,SAASpB,iBAAiB,oBAAoB,KAAK,WAAWoB,SAASqC,kBAAkBnB,EAAEgB,EAAEqB,eAAetE,GAAE,GAAI,IAAIN,GAAG,KAAKkC,EAAE6B,IAAIxD,EAAEC,EAAE,OAAOF,EAAEJ,EAAEH,EAAEQ,EAAE2F,EAAE7F,EAAEoE,iBAAiB,IAAI,GAAG,EAAE,MAAM8B,EAAE5C,EAAEnB,CAAAA,CAAEzC,GAAG2C,KAAKiB,IAAI5D,EAAE,EAAE,MAAMyG,EAAE,CAAC,KAAK,KAAKC,EAAE,SAAC1G,GAAS,IAAPO,EAACY,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAC,CAAC,EAAKkD,GAAG,KAAK,MAAMlC,EAAE6B,IAAI,IAAIxB,EAAEiB,EAAEhD,EAAE,OAAO,MAAMiD,EAAErB,EAAE9B,EAAEiG,GAAG5C,EAAE5D,IAAIO,EAAEmE,mBAAmB1E,EAAEA,EAAE2G,OAAO,IAAI,IAAI,MAAM1G,KAAKD,EAAE0D,EAAEjB,EAAExC,GAAGA,EAAE4C,UAAUV,EAAEiC,kBAAkBX,EAAE/C,MAAMqB,KAAK0C,IAAIxE,EAAE4C,UAAUrC,IAAI,GAAGiD,EAAE7B,QAAQ,CAAC3B,GAAGuC,IAAI,EAAEsB,EAAErB,EAAE,2BAA2BmB,GAAG,GAAGE,EAAE,CAACtB,EAAErC,EAAEH,EAAEyD,EAAEgD,EAAElG,EAAEmE,kBAAkB,MAAMlE,EAAEgD,GAAG,KAAKI,EAAEE,EAAEe,eAAef,EAAEU,aAAahC,GAAE,EAAG,IAAI,IAAI,MAAMxC,IAAI,CAAC,UAAU,QAAQ,oBAAoBE,iBAAiBF,GAAG,IAAIgG,EAAExF,IAAI,CAACoG,SAAQ,EAAGV,MAAK,IAAKjG,GAAGA,IAAIwD,EAAEhD,EAAE,OAAO+B,EAAErC,EAAEH,EAAEyD,EAAEgD,EAAElG,EAAEmE,kBAAkBpE,GAAG,KAAKmD,EAAE/C,MAAMI,YAAYG,MAAMhB,EAAEI,UAAUmC,GAAE,EAAG,GAAG,GAAG,IAAI,EAAEqE,EAAE,CAAC,IAAI,MAAMC,EAAE9G,IAAIsB,SAASC,aAAa8C,GAAG,IAAIyC,EAAE9G,KAAK,aAAasB,SAASyF,WAAW7G,iBAAiB,QAAQ,IAAI4G,EAAE9G,KAAI,GAAImE,WAAWnE,EAAE,EAAEgH,EAAE,SAAChH,GAAS,IAAPM,EAACa,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAC,CAAC,EAASgB,EAAE1B,EAAE,QAAQ4B,EAAElC,EAAEH,EAAEmC,EAAE0E,EAAEvG,EAAEoE,kBAAkBoC,GAAG,KAAK,MAAMtE,EAAEjC,IAAIiC,IAAIL,EAAEzB,MAAMqB,KAAK0C,IAAIjC,EAAExB,cAAcR,IAAI,GAAG2B,EAAEP,QAAQ,CAACY,GAAGH,GAAE,GAAIpC,GAAG,KAAKkC,EAAE1B,EAAE,OAAO,GAAG4B,EAAElC,EAAEH,EAAEmC,EAAE0E,EAAEvG,EAAEoE,kBAAkBrC,GAAE,EAAG,IAAI,GAAG,C", "sources": ["../node_modules/web-vitals/dist/web-vitals.js"], "sourcesContent": ["let e=-1;const t=t=>{addEventListener(\"pageshow\",(n=>{n.persisted&&(e=n.timeStamp,t(n))}),!0)},n=(e,t,n,i)=>{let o,s;return r=>{t.value>=0&&(r||i)&&(s=t.value-(o??0),(s||void 0===o)&&(o=t.value,t.delta=s,t.rating=((e,t)=>e>t[1]?\"poor\":e>t[0]?\"needs-improvement\":\"good\")(t.value,n),e(t)))}},i=e=>{requestAnimationFrame((()=>requestAnimationFrame((()=>e()))))},o=()=>{const e=performance.getEntriesByType(\"navigation\")[0];if(e&&e.responseStart>0&&e.responseStart<performance.now())return e},s=()=>{const e=o();return e?.activationStart??0},r=(t,n=-1)=>{const i=o();let r=\"navigate\";e>=0?r=\"back-forward-cache\":i&&(document.prerendering||s()>0?r=\"prerender\":document.wasDiscarded?r=\"restore\":i.type&&(r=i.type.replace(/_/g,\"-\")));return{name:t,value:n,rating:\"good\",delta:0,entries:[],id:`v5-${Date.now()}-${Math.floor(8999999999999*Math.random())+1e12}`,navigationType:r}},c=new WeakMap;function a(e,t){return c.get(e)||c.set(e,new t),c.get(e)}class d{t;i=0;o=[];h(e){if(e.hadRecentInput)return;const t=this.o[0],n=this.o.at(-1);this.i&&t&&n&&e.startTime-n.startTime<1e3&&e.startTime-t.startTime<5e3?(this.i+=e.value,this.o.push(e)):(this.i=e.value,this.o=[e]),this.t?.(e)}}const h=(e,t,n={})=>{try{if(PerformanceObserver.supportedEntryTypes.includes(e)){const i=new PerformanceObserver((e=>{Promise.resolve().then((()=>{t(e.getEntries())}))}));return i.observe({type:e,buffered:!0,...n}),i}}catch{}},f=e=>{let t=!1;return()=>{t||(e(),t=!0)}};let u=-1;const l=()=>\"hidden\"!==document.visibilityState||document.prerendering?1/0:0,m=e=>{\"hidden\"===document.visibilityState&&u>-1&&(u=\"visibilitychange\"===e.type?e.timeStamp:0,v())},g=()=>{addEventListener(\"visibilitychange\",m,!0),addEventListener(\"prerenderingchange\",m,!0)},v=()=>{removeEventListener(\"visibilitychange\",m,!0),removeEventListener(\"prerenderingchange\",m,!0)},p=()=>{if(u<0){const e=s(),n=document.prerendering?void 0:globalThis.performance.getEntriesByType(\"visibility-state\").filter((t=>\"hidden\"===t.name&&t.startTime>e))[0]?.startTime;u=n??l(),g(),t((()=>{setTimeout((()=>{u=l(),g()}))}))}return{get firstHiddenTime(){return u}}},y=e=>{document.prerendering?addEventListener(\"prerenderingchange\",(()=>e()),!0):e()},b=[1800,3e3],P=(e,o={})=>{y((()=>{const c=p();let a,d=r(\"FCP\");const f=h(\"paint\",(e=>{for(const t of e)\"first-contentful-paint\"===t.name&&(f.disconnect(),t.startTime<c.firstHiddenTime&&(d.value=Math.max(t.startTime-s(),0),d.entries.push(t),a(!0)))}));f&&(a=n(e,d,b,o.reportAllChanges),t((t=>{d=r(\"FCP\"),a=n(e,d,b,o.reportAllChanges),i((()=>{d.value=performance.now()-t.timeStamp,a(!0)}))})))}))},T=[.1,.25],E=(e,o={})=>{P(f((()=>{let s,c=r(\"CLS\",0);const f=a(o,d),u=e=>{for(const t of e)f.h(t);f.i>c.value&&(c.value=f.i,c.entries=f.o,s())},l=h(\"layout-shift\",u);l&&(s=n(e,c,T,o.reportAllChanges),document.addEventListener(\"visibilitychange\",(()=>{\"hidden\"===document.visibilityState&&(u(l.takeRecords()),s(!0))})),t((()=>{f.i=0,c=r(\"CLS\",0),s=n(e,c,T,o.reportAllChanges),i((()=>s()))})),setTimeout(s))})))};let _=0,L=1/0,M=0;const C=e=>{for(const t of e)t.interactionId&&(L=Math.min(L,t.interactionId),M=Math.max(M,t.interactionId),_=M?(M-L)/7+1:0)};let I;const w=()=>I?_:performance.interactionCount??0,F=()=>{\"interactionCount\"in performance||I||(I=h(\"event\",C,{type:\"event\",buffered:!0,durationThreshold:0}))};let k=0;class A{u=[];l=new Map;m;v;p(){k=w(),this.u.length=0,this.l.clear()}P(){const e=Math.min(this.u.length-1,Math.floor((w()-k)/50));return this.u[e]}h(e){if(this.m?.(e),!e.interactionId&&\"first-input\"!==e.entryType)return;const t=this.u.at(-1);let n=this.l.get(e.interactionId);if(n||this.u.length<10||e.duration>t.T){if(n?e.duration>n.T?(n.entries=[e],n.T=e.duration):e.duration===n.T&&e.startTime===n.entries[0].startTime&&n.entries.push(e):(n={id:e.interactionId,entries:[e],T:e.duration},this.l.set(n.id,n),this.u.push(n)),this.u.sort(((e,t)=>t.T-e.T)),this.u.length>10){const e=this.u.splice(10);for(const t of e)this.l.delete(t.id)}this.v?.(n)}}}const B=e=>{const t=globalThis.requestIdleCallback||setTimeout;\"hidden\"===document.visibilityState?e():(e=f(e),document.addEventListener(\"visibilitychange\",e,{once:!0}),t((()=>{e(),document.removeEventListener(\"visibilitychange\",e)})))},N=[200,500],S=(e,i={})=>{globalThis.PerformanceEventTiming&&\"interactionId\"in PerformanceEventTiming.prototype&&y((()=>{F();let o,s=r(\"INP\");const c=a(i,A),d=e=>{B((()=>{for(const t of e)c.h(t);const t=c.P();t&&t.T!==s.value&&(s.value=t.T,s.entries=t.entries,o())}))},f=h(\"event\",d,{durationThreshold:i.durationThreshold??40});o=n(e,s,N,i.reportAllChanges),f&&(f.observe({type:\"first-input\",buffered:!0}),document.addEventListener(\"visibilitychange\",(()=>{\"hidden\"===document.visibilityState&&(d(f.takeRecords()),o(!0))})),t((()=>{c.p(),s=r(\"INP\"),o=n(e,s,N,i.reportAllChanges)})))}))};class q{m;h(e){this.m?.(e)}}const x=[2500,4e3],O=(e,o={})=>{y((()=>{const c=p();let d,u=r(\"LCP\");const l=a(o,q),m=e=>{o.reportAllChanges||(e=e.slice(-1));for(const t of e)l.h(t),t.startTime<c.firstHiddenTime&&(u.value=Math.max(t.startTime-s(),0),u.entries=[t],d())},g=h(\"largest-contentful-paint\",m);if(g){d=n(e,u,x,o.reportAllChanges);const s=f((()=>{m(g.takeRecords()),g.disconnect(),d(!0)}));for(const e of[\"keydown\",\"click\",\"visibilitychange\"])addEventListener(e,(()=>B(s)),{capture:!0,once:!0});t((t=>{u=r(\"LCP\"),d=n(e,u,x,o.reportAllChanges),i((()=>{u.value=performance.now()-t.timeStamp,d(!0)}))}))}}))},$=[800,1800],D=e=>{document.prerendering?y((()=>D(e))):\"complete\"!==document.readyState?addEventListener(\"load\",(()=>D(e)),!0):setTimeout(e)},H=(e,i={})=>{let c=r(\"TTFB\"),a=n(e,c,$,i.reportAllChanges);D((()=>{const d=o();d&&(c.value=Math.max(d.responseStart-s(),0),c.entries=[d],a(!0),t((()=>{c=r(\"TTFB\",0),a=n(e,c,$,i.reportAllChanges),a(!0)})))}))};export{T as CLSThresholds,b as FCPThresholds,N as INPThresholds,x as LCPThresholds,$ as TTFBThresholds,E as onCLS,P as onFCP,S as onINP,O as onLCP,H as onTTFB};\n"], "names": ["e", "t", "addEventListener", "n", "persisted", "timeStamp", "i", "o", "s", "r", "value", "delta", "rating", "requestAnimationFrame", "performance", "getEntriesByType", "responseStart", "now", "activationStart", "arguments", "length", "undefined", "document", "prerendering", "wasDiscarded", "type", "replace", "name", "entries", "id", "Date", "Math", "floor", "random", "navigationType", "c", "WeakMap", "a", "get", "set", "d", "h", "hadRecentInput", "this", "at", "startTime", "push", "PerformanceObserver", "supportedEntryTypes", "includes", "Promise", "resolve", "then", "getEntries", "observe", "buffered", "f", "u", "l", "visibilityState", "m", "v", "g", "removeEventListener", "p", "globalThis", "filter", "setTimeout", "firstHiddenTime", "y", "b", "P", "disconnect", "max", "reportAllChanges", "T", "E", "takeRecords", "_", "L", "M", "C", "interactionId", "min", "I", "w", "interactionCount", "k", "A", "Map", "clear", "entryType", "duration", "sort", "splice", "delete", "B", "requestIdleCallback", "once", "N", "S", "PerformanceEventTiming", "prototype", "durationThreshold", "q", "x", "O", "slice", "capture", "$", "D", "readyState", "H"], "sourceRoot": ""}