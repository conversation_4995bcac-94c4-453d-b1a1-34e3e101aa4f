<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Steampipe Compliance Dashboard - Secret Reference API</title>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1 {
            color: #333;
            margin-bottom: 30px;
        }
        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #555;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .results {
            margin-top: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .status.running { background-color: #2196F3; color: white; }
        .status.completed { background-color: #4CAF50; color: white; }
        .status.failed { background-color: #f44336; color: white; }
        .status.pending { background-color: #FF9800; color: white; }
        .tabs {
            display: flex;
            border-bottom: 2px solid #ddd;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            background: none;
            border: none;
            font-size: 16px;
            color: #666;
        }
        .tab.active {
            color: #4CAF50;
            border-bottom: 2px solid #4CAF50;
            margin-bottom: -2px;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .info-box {
            background-color: #e3f2fd;
            border-left: 4px solid #2196F3;
            padding: 15px;
            margin-bottom: 20px;
        }
        .error {
            color: #f44336;
            font-size: 14px;
            margin-top: 5px;
        }
        .job-item {
            border-bottom: 1px solid #ddd;
            padding: 10px 0;
        }
        .job-item:last-child {
            border-bottom: none;
        }
        .job-status {
            font-weight: bold;
            margin-left: 10px;
        }
        .job-status.completed { color: #4CAF50; }
        .job-status.running { color: #2196F3; }
        .job-status.failed { color: #f44336; }
        .summary-card {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
        }
        .summary-item {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .summary-item.ok { background-color: #e7f5e7; color: #2e7d32; }
        .summary-item.alarm { background-color: #fff3cd; color: #f57c00; }
        .summary-item.error { background-color: #ffebee; color: #c62828; }
        .summary-item.info { background-color: #e3f2fd; color: #1565c0; }
        .summary-item.skipped { background-color: #f5f5f5; color: #616161; }
        .summary-count {
            font-size: 24px;
            font-weight: bold;
            display: block;
        }
        .summary-label {
            font-size: 12px;
            text-transform: uppercase;
            margin-top: 5px;
        }
        .control-item {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 10px;
            background-color: #fff;
        }
        .control-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .control-title {
            font-weight: 600;
            color: #333;
        }
        .control-summary {
            display: flex;
            gap: 10px;
            font-size: 12px;
        }
        .control-summary span {
            padding: 2px 8px;
            border-radius: 3px;
        }
        .benchmark-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .benchmark-metadata {
            color: #666;
            font-size: 14px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Steampipe Compliance Dashboard</h1>
        <p>Secret Reference API - No Customer Management</p>

        <div class="tabs">
            <button class="tab active" onclick="showTab('gcp')">GCP</button>
            <button class="tab" onclick="showTab('aws')">AWS</button>
            <button class="tab" onclick="showTab('azure')">Azure</button>
        </div>

        <!-- GCP Tab -->
        <div id="gcp-tab" class="tab-content active">
            <div class="card">
                <h2>GCP Compliance Scan</h2>
                
                <div class="info-box">
                    <strong>How it works:</strong><br>
                    1. Store your GCP service account JSON in Google Secret Manager<br>
                    2. Use the secret name as reference (e.g., "customer1-gcp-prod")<br>
                    3. The API fetches credentials from Secret Manager using the reference
                </div>

                <div class="form-group">
                    <label for="gcp-secret-ref">Secret Reference Name:</label>
                    <input type="text" id="gcp-secret-ref" placeholder="e.g., customer1-gcp-prod">
                </div>

                <div class="form-group">
                    <label for="gcp-project">Project ID to Scan:</label>
                    <input type="text" id="gcp-project" placeholder="e.g., my-gcp-project-id">
                </div>

                <div class="form-group">
                    <label for="gcp-benchmark">Compliance Benchmark:</label>
                    <select id="gcp-benchmark">
                        <option value="cft_scorecard_v1">CFT Scorecard v1</option>
                        <option value="cis_v120">CIS v1.2.0</option>
                        <option value="cis_v130">CIS v1.3.0</option>
                        <option value="cis_v200">CIS v2.0.0</option>
                        <option value="cis_v300">CIS v3.0.0</option>
                        <option value="hipaa">HIPAA</option>
                        <option value="nist_800_53_rev_5">NIST 800-53 Rev 5</option>
                        <option value="nist_csf_v10">NIST CSF v1.0</option>
                        <option value="pci_dss_v321">PCI DSS v3.2.1</option>
                        <option value="soc_2_2017">SOC 2 2017</option>
                        <option value="all_controls">All Controls</option>
                        <option value="forseti_security_v226">Forseti Security v2.26</option>
                    </select>
                </div>

                <button onclick="testAuth('gcp')">Test Authentication</button>
                <button onclick="runBenchmark('gcp')">Run Benchmark</button>
                <button onclick="getOrgId('gcp')">Get Organization ID</button>

                <div id="gcp-results" class="results" style="display:none;"></div>
            </div>

            <div class="card">
                <h3>Job History</h3>
                <button onclick="refreshJobs('gcp')">Refresh Jobs</button>
                <div id="gcp-job-list"></div>
            </div>
        </div>

        <!-- AWS Tab -->
        <div id="aws-tab" class="tab-content">
            <div class="card">
                <h2>AWS Compliance Scan</h2>
                
                <div class="info-box">
                    <strong>How it works:</strong><br>
                    1. Store your AWS credentials in Google Secret Manager<br>
                    2. Use the secret name as reference (e.g., "customer1-aws-prod")<br>
                    3. The API fetches credentials from Secret Manager using the reference
                </div>

                <div class="form-group">
                    <label for="aws-secret-ref">Secret Reference Name:</label>
                    <input type="text" id="aws-secret-ref" placeholder="e.g., customer1-aws-prod">
                </div>

                <div class="form-group">
                    <label for="aws-account">Account ID to Scan:</label>
                    <input type="text" id="aws-account" placeholder="e.g., ************">
                </div>

                <div class="form-group">
                    <label for="aws-benchmark">Compliance Benchmark:</label>
                    <select id="aws-benchmark">
                        <option value="acsc_essential_eight">ACSC Essential Eight</option>
                        <option value="all_controls">All Controls</option>
                        <option value="audit_manager_control_tower">Audit Manager Control Tower</option>
                        <option value="cis_compute_service_v100">CIS Compute Service v1.0.0</option>
                        <option value="cis_controls_v8_ig1">CIS Controls v8 IG1</option>
                        <option value="cis_v150">CIS v1.5.0</option>
                        <option value="cis_v200">CIS v2.0.0</option>
                        <option value="cis_v300">CIS v3.0.0</option>
                        <option value="foundational_security">AWS Foundational Security Best Practices</option>
                        <option value="nist_800_53_rev_5">NIST 800-53 Rev 5</option>
                        <option value="nist_csf">NIST Cybersecurity Framework</option>
                        <option value="pci_dss_v321">PCI DSS v3.2.1</option>
                        <option value="rbi_itf_nbfc">RBI ITF NBFC</option>
                    </select>
                </div>

                <button onclick="testAuth('aws')">Test Authentication</button>
                <button onclick="runBenchmark('aws')">Run Benchmark</button>

                <div id="aws-results" class="results" style="display:none;"></div>
            </div>

            <div class="card">
                <h3>Job History</h3>
                <button onclick="refreshJobs('aws')">Refresh Jobs</button>
                <div id="aws-job-list"></div>
            </div>
        </div>

        <!-- Azure Tab -->
        <div id="azure-tab" class="tab-content">
            <div class="card">
                <h2>Azure Compliance Scan</h2>
                
                <div class="info-box">
                    <strong>How it works:</strong><br>
                    1. Store your Azure credentials in Google Secret Manager<br>
                    2. Use the secret name as reference (e.g., "customer1-azure-prod")<br>
                    3. The API fetches credentials from Secret Manager using the reference
                </div>

                <div class="form-group">
                    <label for="azure-secret-ref">Secret Reference Name:</label>
                    <input type="text" id="azure-secret-ref" placeholder="e.g., customer1-azure-prod">
                </div>

                <div class="form-group">
                    <label for="azure-subscription">Subscription ID to Scan:</label>
                    <input type="text" id="azure-subscription" placeholder="e.g., xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx">
                </div>

                <div class="form-group">
                    <label for="azure-benchmark">Compliance Benchmark:</label>
                    <select id="azure-benchmark">
                        <option value="cis_v130">CIS v1.3.0</option>
                        <option value="cis_v140">CIS v1.4.0</option>
                        <option value="cis_v150">CIS v1.5.0</option>
                        <option value="cis_v200">CIS v2.0.0</option>
                        <option value="cis_v210">CIS v2.1.0</option>
                        <option value="hipaa_hitrust_v92">HIPAA HITRUST v9.2</option>
                        <option value="nist_sp_800_53_rev_5">NIST SP 800-53 Rev 5</option>
                        <option value="pci_dss_v321">PCI DSS v3.2.1</option>
                    </select>
                </div>

                <button onclick="testAuth('azure')">Test Authentication</button>
                <button onclick="runBenchmark('azure')">Run Benchmark</button>

                <div id="azure-results" class="results" style="display:none;"></div>
            </div>

            <div class="card">
                <h3>Job History</h3>
                <button onclick="refreshJobs('azure')">Refresh Jobs</button>
                <div id="azure-job-list"></div>
            </div>
        </div>
    </div>

    <script>
        // Use relative URLs to go through nginx proxy
        const API_ENDPOINTS = {
            gcp: '',  // Will use current host
            aws: '',  // Will use current host
            azure: ''  // Will use current host
        };
        let activeJobs = {};

        function showTab(provider) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(`${provider}-tab`).classList.add('active');
            event.target.classList.add('active');
        }

        function showResults(provider, content) {
            const resultsDiv = document.getElementById(`${provider}-results`);
            resultsDiv.style.display = 'block';
            
            if (typeof content === 'string') {
                resultsDiv.textContent = content;
                return;
            }
            
            // Check if this is a benchmark result
            if (content.benchmark_results && content.benchmark_results.groups) {
                displayBenchmarkResults(resultsDiv, content, provider);
            } else {
                // Default JSON display
                resultsDiv.textContent = JSON.stringify(content, null, 2);
            }
        }
        
        function displayBenchmarkResults(container, data, provider) {
            let html = '';
            
            // Get benchmark name
            const benchmarkSelect = document.getElementById(`${provider}-benchmark`);
            const benchmarkName = benchmarkSelect.options[benchmarkSelect.selectedIndex].text;
            
            html += `<div class="benchmark-title">${benchmarkName}</div>`;
            html += `<div class="benchmark-metadata">Last updated: ${new Date().toLocaleString()}</div>`;
            
            // Check if this is a job result wrapper
            if (data.result && data.result.groups) {
                data = data.result;
            }
            
            // Calculate summary
            const summary = { ok: 0, alarm: 0, error: 0, info: 0, skipped: 0 };
            
            function countResults(groups) {
                groups.forEach(group => {
                    if (group.controls) {
                        group.controls.forEach(control => {
                            // Check control summary first
                            if (control.summary) {
                                summary.ok += control.summary.ok || 0;
                                summary.alarm += control.summary.alarm || 0;
                                summary.error += control.summary.error || 0;
                                summary.info += control.summary.info || 0;
                                summary.skipped += control.summary.skip || control.summary.skipped || 0;
                            } else if (control.results) {
                                // Fall back to counting individual results
                                control.results.forEach(result => {
                                    if (summary[result.status] !== undefined) {
                                        summary[result.status]++;
                                    }
                                });
                            } else if (control.run_error) {
                                // Control had an error
                                summary.error++;
                            }
                        });
                    }
                    if (group.groups) {
                        countResults(group.groups);
                    }
                });
            }
            
            countResults(data.benchmark_results.groups);
            
            // Display summary
            html += '<div class="summary-card">';
            Object.entries(summary).forEach(([status, count]) => {
                html += `
                    <div class="summary-item ${status}">
                        <span class="summary-count">${count}</span>
                        <div class="summary-label">${status}</div>
                    </div>
                `;
            });
            html += '</div>';
            
            // Display controls
            html += '<h3 style="margin-top: 30px; margin-bottom: 15px;">Control Results</h3>';
            
            function displayControls(groups, level = 0) {
                let controlsHtml = '';
                groups.forEach(group => {
                    if (group.title) {
                        controlsHtml += `<h4 style="margin-left: ${level * 20}px; margin-top: 20px;">${group.title}</h4>`;
                    }
                    
                    if (group.controls) {
                        group.controls.forEach(control => {
                            const controlSummary = { ok: 0, alarm: 0, error: 0, info: 0, skipped: 0 };
                            
                            // Use control summary if available
                            if (control.summary) {
                                controlSummary.ok = control.summary.ok || 0;
                                controlSummary.alarm = control.summary.alarm || 0;
                                controlSummary.error = control.summary.error || 0;
                                controlSummary.info = control.summary.info || 0;
                                controlSummary.skipped = control.summary.skip || control.summary.skipped || 0;
                            } else if (control.results) {
                                control.results.forEach(result => {
                                    if (controlSummary[result.status] !== undefined) {
                                        controlSummary[result.status]++;
                                    }
                                });
                            } else if (control.run_error) {
                                controlSummary.error = 1;
                            }
                            
                            controlsHtml += `
                                <div class="control-item" style="margin-left: ${level * 20}px;">
                                    <div class="control-header">
                                        <div class="control-title">${control.title || control.description}</div>
                                        <div class="control-summary">
                                            ${Object.entries(controlSummary)
                                                .filter(([_, count]) => count > 0)
                                                .map(([status, count]) => 
                                                    `<span class="summary-item ${status}" style="padding: 2px 8px;">${status}: ${count}</span>`
                                                ).join('')}
                                        </div>
                                    </div>
                                    ${control.description && control.description !== control.title ? 
                                        `<div style="color: #666; font-size: 14px; margin-top: 5px;">${control.description}</div>` : ''}
                                    ${control.run_error ? 
                                        `<div style="color: #f44336; font-size: 12px; margin-top: 5px;">Error: ${control.run_error}</div>` : ''}
                                </div>
                            `;
                        });
                    }
                    
                    if (group.groups) {
                        controlsHtml += displayControls(group.groups, level + 1);
                    }
                });
                return controlsHtml;
            }
            
            html += displayControls(data.benchmark_results.groups);
            
            container.innerHTML = html;
        }

        async function testAuth(provider) {
            const secretRef = document.getElementById(`${provider}-secret-ref`).value;
            if (!secretRef) {
                alert('Please enter a secret reference name');
                return;
            }

            showResults(provider, 'Testing authentication...');

            try {
                const response = await fetch(`${API_ENDPOINTS[provider]}/api/${provider}/test-auth`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ secret_reference: secretRef })
                });

                const data = await response.json();
                showResults(provider, data);
            } catch (error) {
                showResults(provider, `Error: ${error.message}`);
            }
        }

        async function runBenchmark(provider) {
            const secretRef = document.getElementById(`${provider}-secret-ref`).value;
            const benchmark = document.getElementById(`${provider}-benchmark`).value;
            let identifier;

            if (provider === 'gcp') {
                identifier = document.getElementById('gcp-project').value;
            } else if (provider === 'aws') {
                identifier = document.getElementById('aws-account').value;
            } else {
                identifier = document.getElementById('azure-subscription').value;
            }

            if (!secretRef || !identifier) {
                alert('Please fill in all required fields');
                return;
            }

            showResults(provider, 'Starting benchmark...');

            try {
                const body = {
                    secret_reference: secretRef,
                    benchmark: benchmark
                };

                if (provider === 'gcp') {
                    body.project_id = identifier;
                } else if (provider === 'aws') {
                    body.account_id = identifier;
                } else {
                    body.subscription_id = identifier;
                }

                // Use the same endpoints as main dashboard
                let endpoint;
                if (provider === 'gcp') {
                    endpoint = `${API_ENDPOINTS[provider]}/api/gcp/run-benchmark`;
                } else if (provider === 'aws') {
                    endpoint = `${API_ENDPOINTS[provider]}/api/aws/run-aws-benchmark`;
                } else {
                    endpoint = `${API_ENDPOINTS[provider]}/api/azure/run-azure-benchmark`;
                }
                
                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(body)
                });

                const data = await response.json();
                
                // Handle direct response from run-benchmark endpoints
                if (data.benchmark_results) {
                    showResults(provider, data);
                } else if (data.job_id) {
                    // Handle async job response
                    showResults(provider, `Job started: ${data.job_id}\nChecking status...`);
                    pollJobStatus(provider, data.job_id);
                } else {
                    showResults(provider, data);
                }
            } catch (error) {
                showResults(provider, `Error: ${error.message}`);
            }
        }

        async function getOrgId(provider) {
            if (provider !== 'gcp') return;
            
            const secretRef = document.getElementById(`${provider}-secret-ref`).value;
            if (!secretRef) {
                alert('Please enter a secret reference name');
                return;
            }

            showResults(provider, 'Getting organization ID...');

            try {
                const response = await fetch(`${API_ENDPOINTS.gcp}/api/gcp/get-org-id`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ secret_reference: secretRef })
                });

                const data = await response.json();
                showResults(provider, data);
            } catch (error) {
                showResults(provider, `Error: ${error.message}`);
            }
        }

        async function pollJobStatus(provider, jobId) {
            const checkStatus = async () => {
                try {
                    const response = await fetch(`${API_ENDPOINTS[provider]}/api/${provider}/job/${jobId}/status`);
                    const status = await response.json();
                    
                    showResults(provider, `Job ${jobId}: ${status.status}\nProgress: ${status.progress || 0}%`);
                    
                    if (status.status === 'completed') {
                        // Get the final result
                        const resultResponse = await fetch(`${API_ENDPOINTS[provider]}/api/${provider}/job/${jobId}/result`);
                        const result = await resultResponse.json();
                        showResults(provider, result);
                    } else if (status.status === 'failed') {
                        showResults(provider, `Job failed: ${status.error}`);
                    } else {
                        // Continue polling
                        setTimeout(checkStatus, 5000);
                    }
                } catch (error) {
                    showResults(provider, `Error checking status: ${error.message}`);
                }
            };
            
            checkStatus();
        }

        async function refreshJobs(provider) {
            try {
                const response = await fetch(`${API_ENDPOINTS[provider]}/api/${provider}/jobs`);
                const data = await response.json();
                
                const jobListDiv = document.getElementById(`${provider}-job-list`);
                
                if (data.status === 'success' && data.jobs && data.jobs.length > 0) {
                    // Sort jobs by created_at descending (newest first)
                    const sortedJobs = data.jobs.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
                    
                    let html = '';
                    sortedJobs.forEach(job => {
                        const createdAt = new Date(job.created_at).toLocaleString();
                        const benchmarkSelect = document.getElementById(`${provider}-benchmark`);
                        const benchmarkName = Array.from(benchmarkSelect.options).find(opt => opt.value === job.benchmark)?.text || job.benchmark;
                        
                        html += `<div class="job-item">
                            <div>
                                <strong>Job ID:</strong> ${job.id}
                                <span class="job-status ${job.status}">${job.status.toUpperCase()}</span>
                            </div>
                            <div><strong>Benchmark:</strong> ${benchmarkName}</div>
                            <div><strong>Target:</strong> ${job.project_id || job.account_id || job.subscription_id || 'N/A'}</div>
                            <div><strong>Created:</strong> ${createdAt}</div>
                            ${job.progress ? `<div><strong>Progress:</strong> ${job.progress}%</div>` : ''}
                            ${job.error ? `<div class="error"><strong>Error:</strong> ${job.error}</div>` : ''}
                            ${job.status === 'completed' ? `<button onclick="viewResult('${provider}', '${job.id}')">View Result</button>` : ''}
                            ${job.status === 'running' ? `<button onclick="pollJobStatus('${provider}', '${job.id}')">Check Status</button>` : ''}
                        </div>`;
                    });
                    jobListDiv.innerHTML = html;
                } else {
                    jobListDiv.innerHTML = '<p>No jobs found</p>';
                }
            } catch (error) {
                document.getElementById(`${provider}-job-list`).innerHTML = `<div class="error">Error fetching jobs: ${error.message}</div>`;
            }
        }

        async function viewResult(provider, jobId) {
            try {
                const response = await fetch(`${API_ENDPOINTS[provider]}/api/${provider}/job/${jobId}/result`);
                const result = await response.json();
                showResults(provider, result);
            } catch (error) {
                showResults(provider, `Error fetching result: ${error.message}`);
            }
        }

        // Auto-refresh jobs on tab switch
        function showTab(provider) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(`${provider}-tab`).classList.add('active');
            event.target.classList.add('active');
            
            // Refresh jobs for this provider
            refreshJobs(provider);
        }

        // Initial load - refresh GCP jobs
        window.onload = function() {
            refreshJobs('gcp');
        };
    </script>
</body>
</html>