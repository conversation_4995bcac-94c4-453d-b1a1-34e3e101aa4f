#!/bin/sh
echo "=== Cloud Run Nginx Frontend Startup with Authentication ==="
echo "K_SERVICE environment variable: ${K_SERVICE:-not set}"
echo "K_REVISION environment variable: ${K_REVISION:-not set}"

# Function to get identity token for a specific audience
get_identity_token() {
    local audience=$1
    local token=""
    
    # Check if running in Cloud Run (has K_SERVICE env var)
    if [ -n "$K_SERVICE" ]; then
        echo "Fetching identity token for $audience..." >&2
        # Get token from metadata server
        token=$(curl -s -H "Metadata-Flavor: Google" \
            "http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/identity?audience=$audience")
        
        if [ -n "$token" ]; then
            echo "Successfully fetched token for $audience" >&2
            echo "$token"
        else
            echo "Failed to fetch token for $audience" >&2
            echo ""
        fi
    else
        echo "Not running in Cloud Run, skipping token fetch" >&2
        echo ""
    fi
}

# Set backend URLs with defaults for local development
BACKEND_GCP_URL=${BACKEND_GCP_URL:-"http://localhost:8080"}
BACKEND_AWS_URL=${BACKEND_AWS_URL:-"http://localhost:8082"}
BACKEND_AZURE_URL=${BACKEND_AZURE_URL:-"http://localhost:8083"}

echo "Backend URLs:"
echo "GCP: $BACKEND_GCP_URL"
echo "AWS: $BACKEND_AWS_URL"
echo "Azure: $BACKEND_AZURE_URL"

# Get identity tokens if running in Cloud Run
GCP_TOKEN=""
AWS_TOKEN=""
AZURE_TOKEN=""

if [ -n "$K_SERVICE" ]; then
    echo "Running in Cloud Run, fetching identity tokens..."
    # Extract service URLs without paths for audience
    GCP_AUDIENCE=$(echo "$BACKEND_GCP_URL" | sed 's|/api/.*||')
    AWS_AUDIENCE=$(echo "$BACKEND_AWS_URL" | sed 's|/api/.*||')
    AZURE_AUDIENCE=$(echo "$BACKEND_AZURE_URL" | sed 's|/api/.*||')
    
    echo "Token audiences: GCP=$GCP_AUDIENCE, AWS=$AWS_AUDIENCE, Azure=$AZURE_AUDIENCE"
    
    GCP_TOKEN=$(get_identity_token "$GCP_AUDIENCE")
    AWS_TOKEN=$(get_identity_token "$AWS_AUDIENCE")
    AZURE_TOKEN=$(get_identity_token "$AZURE_AUDIENCE")
else
    echo "Not running in Cloud Run (K_SERVICE not set), skipping token fetch"
fi

# Create nginx config with authentication
echo "Creating auth config with tokens..."
echo "GCP_TOKEN length: ${#GCP_TOKEN}"
echo "AWS_TOKEN length: ${#AWS_TOKEN}"
echo "AZURE_TOKEN length: ${#AZURE_TOKEN}"

cat > /etc/nginx/conf.d/auth-config.conf <<EOF
# Store tokens as nginx variables
map \$uri \$auth_token {
    ~^/api/gcp/  "${GCP_TOKEN:+Bearer $GCP_TOKEN}";
    ~^/api/aws/  "${AWS_TOKEN:+Bearer $AWS_TOKEN}";
    ~^/api/azure/  "${AZURE_TOKEN:+Bearer $AZURE_TOKEN}";
    default      "";
}
EOF

echo "Auth config created at /etc/nginx/conf.d/auth-config.conf"
echo "Auth config contents:"
cat /etc/nginx/conf.d/auth-config.conf | head -20

# Copy the appropriate nginx config
if [ -n "$K_SERVICE" ] && [ -f /etc/nginx/nginx-cloudrun.conf ]; then
    # Running in Cloud Run, use cloudrun config
    cp /etc/nginx/nginx-cloudrun.conf /etc/nginx/nginx.conf
elif [ -f /etc/nginx/nginx.conf.template ]; then
    # Running locally, use template config
    cp /etc/nginx/nginx.conf.template /etc/nginx/nginx.conf
fi

# Replace backend URLs in nginx config BEFORE testing
sed -i "s|\$BACKEND_GCP_URL|$BACKEND_GCP_URL|g" /etc/nginx/nginx.conf
sed -i "s|\$BACKEND_AWS_URL|$BACKEND_AWS_URL|g" /etc/nginx/nginx.conf
sed -i "s|\$BACKEND_AZURE_URL|$BACKEND_AZURE_URL|g" /etc/nginx/nginx.conf

# Test nginx configuration
nginx -t 2>&1 | head -10

echo "Nginx config after replacement:"
grep -E "backend_url|proxy_pass" /etc/nginx/nginx.conf

# Create a token refresh script
cat > /usr/local/bin/refresh-tokens.sh <<'SCRIPT'
#!/bin/sh
while true; do
    sleep 3300  # Refresh every 55 minutes (tokens last 1 hour)
    
    if [ -n "$K_SERVICE" ]; then
        echo "Refreshing identity tokens..."
        
        GCP_AUDIENCE=$(echo "$BACKEND_GCP_URL" | sed 's|/api/.*||')
        AWS_AUDIENCE=$(echo "$BACKEND_AWS_URL" | sed 's|/api/.*||')
        AZURE_AUDIENCE=$(echo "$BACKEND_AZURE_URL" | sed 's|/api/.*||')
        
        GCP_TOKEN=$(curl -s -H "Metadata-Flavor: Google" \
            "http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/identity?audience=$GCP_AUDIENCE")
        AWS_TOKEN=$(curl -s -H "Metadata-Flavor: Google" \
            "http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/identity?audience=$AWS_AUDIENCE")
        AZURE_TOKEN=$(curl -s -H "Metadata-Flavor: Google" \
            "http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/identity?audience=$AZURE_AUDIENCE")
        
        # Update nginx config
        cat > /etc/nginx/conf.d/auth-config.conf <<EOF
map \$uri \$auth_token {
    ~^/api/gcp/  "${GCP_TOKEN:+Bearer $GCP_TOKEN}";
    ~^/api/aws/  "${AWS_TOKEN:+Bearer $AWS_TOKEN}";
    ~^/api/azure/  "${AZURE_TOKEN:+Bearer $AZURE_TOKEN}";
    default      "";
}
EOF
        
        # Reload nginx to pick up new tokens
        nginx -s reload
        echo "Tokens refreshed successfully"
    fi
done
SCRIPT

chmod +x /usr/local/bin/refresh-tokens.sh

# Start token refresh in background (only in Cloud Run)
if [ -n "$K_SERVICE" ]; then
    /usr/local/bin/refresh-tokens.sh &
fi

# Start nginx
exec nginx -g 'daemon off;'