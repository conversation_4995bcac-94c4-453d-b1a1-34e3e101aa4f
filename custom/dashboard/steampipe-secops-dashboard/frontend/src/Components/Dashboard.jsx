import React, { useState, useEffect } from 'react';
import { 
  ChevronDown, ChevronRight, ChevronUp, 
  AlertCircle, CheckCircle, Info, SkipForward, 
  Loader2, Building2, FolderGit2, Moon, Sun, 
  FileCheck, X, Download, Filter, BarChart3,
  RefreshCw, Clock, Calendar, Share2, Settings
} from 'lucide-react';
import BenchmarkControls from './BenchmarkControls';
import AuthSelector from './AuthSelector';
import { Cloud, CloudCog, CloudLightning } from 'lucide-react';

import * as XLSX from 'xlsx';


const CloudTabs = ({ activeProvider, onProviderChange }) => {
  const providers = [
    { 
      id: 'gcp', 
      name: 'G<PERSON>', 
      color: 'from-blue-500 to-blue-600',
      icon: Cloud,
      iconColor: 'text-blue-500',
      enabled: true
    },
    { 
      id: 'aws', 
      name: 'AWS', 
      color: 'from-orange-500 to-orange-600',
      icon: CloudCog,
      iconColor: 'text-orange-500',
      enabled: true
    },
    { 
      id: 'azure', 
      name: 'Azure', 
      color: 'from-blue-600 to-blue-700',
      icon: CloudLightning,
      iconColor: 'text-blue-600',
      enabled: true
    }
  ];

  return (
    <div className="fixed left-0 top-1/4 flex flex-col items-start p-4 space-y-2">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-2 w-16">
        {providers.map((provider) => {
          const IconComponent = provider.icon;
          const isDisabled = !provider.enabled;
          
          return (
            <div key={provider.id} className="relative group">
              <button
                onClick={() => provider.enabled && onProviderChange(provider.id)}
                disabled={isDisabled}
                className={`w-full p-2 rounded-lg flex flex-col items-center justify-center transition-all duration-200 ${
                  activeProvider === provider.id
                    ? `bg-gradient-to-r ${provider.color} text-white shadow-md`
                    : `text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 ${provider.iconColor} ${
                        isDisabled ? 'opacity-50 cursor-not-allowed' : ''
                      }`
                }`}
              >
                <IconComponent className="w-6 h-6" />
                <span className="text-xs font-medium mt-1">{provider.name}</span>
              </button>
              {isDisabled && (
                <div className="absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
                  Coming soon
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

const isApiNotEnabledError = (errorMessage) => {
  if (!errorMessage) return false;
  
  const patterns = [
    /API has not been used in project .* before or it is disabled/,
    /Error 403: .* API has not been enabled/,
    /API .* is not enabled/
  ];
  
  return patterns.some(pattern => pattern.test(errorMessage));
};

const BenchmarkDashboard = () => {
  const [activeProvider, setActiveProvider] = useState('gcp');
  const [darkMode, setDarkMode] = useState(() => {
    if (typeof window !== 'undefined') {
      const savedMode = localStorage.getItem('darkMode');
      return savedMode ? JSON.parse(savedMode) : 
             window.matchMedia('(prefers-color-scheme: dark)').matches;
    }
    return false;
  });
  
  const [controlsData, setControlsData] = useState(null);
  const [controls, setControls] = useState([]);
  const [cachedControlsData, setCachedControlsData] = useState(null);
  const [isControlsOpen, setIsControlsOpen] = useState(false);
  const [benchmarkData, setBenchmarkData] = useState(null);
  const [organizationId, setOrganizationId] = useState(null);
  
  const [activeRequests, setActiveRequests] = useState({
    gcp: { org: false, projects: false },
    aws: { org: false, projects: false },
    azure: { org: false, projects: false }
  });
  
  const [orgLoadingState, setOrgLoadingState] = useState({
    gcp: false,
    aws: false,
    azure: false
  });
  const [projectsLoadingState, setProjectsLoadingState] = useState({
    gcp: false,
    aws: false,
    azure: false
  });
  
  const orgLoading = orgLoadingState[activeProvider];
  const projectsLoading = projectsLoadingState[activeProvider];
  
  useEffect(() => {
    console.log(`Provider changed to: ${activeProvider}`);
    console.log(`Loading states:`, {
      orgLoading: orgLoadingState[activeProvider],
      projectsLoading: projectsLoadingState[activeProvider]
    });
    console.log(`Active requests:`, activeRequests);
  }, [activeProvider, orgLoadingState, projectsLoadingState, activeRequests]);
  
  const [orgError, setOrgError] = useState(null);
  
  const [orgIdCache, setOrgIdCache] = useState({
    gcp: null,
    aws: null,
    azure: null
  });
  
  const [projectsCache, setProjectsCache] = useState({
    gcp: [],
    aws: [],
    azure: []
  });
  
  const [projects, setProjects] = useState([]);
  const [selectedProject, setSelectedProject] = useState("");
  const [projectsError, setProjectsError] = useState(null);
  const [expandedProjects, setExpandedProjects] = useState({});
  const [progress, setProgress] = useState(0); 
  const [lastRunDate, setLastRunDate] = useState(null);
  const [showSettings, setShowSettings] = useState(false);
  const [secretConfig, setSecretConfig] = useState(null);
  const [authConfig, setAuthConfig] = useState({
    type: 'adc',
    secretReference: null,
    smCredentials: null
  });
  
  const [selectedCompliance, setSelectedCompliance] = useState("");
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(false);

  // Helper function to get API headers with secret configuration
  const getApiHeaders = () => {
    const headers = {
      'Content-Type': 'application/json'
    };
    
    // Add secret configuration if available
    const savedConfig = localStorage.getItem(`secretManager_${activeProvider}`);
    if (savedConfig) {
      const config = JSON.parse(savedConfig);
      if (config.secretName && config.projectId) {
        headers['X-Secret-Manager-Project'] = config.projectId;
        headers['X-Secret-Manager-Secret'] = config.secretName;
      }
    }
    
    return headers;
  };
  const [error, setError] = useState(null);
  const [expandedControls, setExpandedControls] = useState({});
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState({
    ok: true,
    alarm: true,
    error: true,
    info: true,
    skipped: true
  });
  const [groupBy, setGroupBy] = useState('none');
  const [searchQuery, setSearchQuery] = useState('');
  const [showExportOptions, setShowExportOptions] = useState(false);
  const [favoriteProjects, setFavoriteProjects] = useState([]);
  const [isCardView, setIsCardView] = useState(false);

  const [allProjectsBenchmarkProgress, setAllProjectsBenchmarkProgress] = useState({
    current: 0,
    total: 0,
    inProgress: false,
    currentProject: '',
    completedProjects: []
  });

  const toggleProject = (projectId) => {
    setExpandedProjects(prev => ({
      ...prev,
      [projectId]: !prev[projectId]
    }));
  };

  const handleShowControls = () => {
    if (!selectedCompliance) return;
    if (controlsData) {
      setIsControlsOpen(true);
    }
  };

  const statusTypes = ['ok', 'alarm', 'error', 'info', 'skipped'];
  const [benchmarkResults, setBenchmarkResults] = useState({
    title: 'Benchmark Results',
    controls: []
  });
  const statusLabels = {
    ok: 'Passed',
    alarm: 'Failed',
    error: 'Error',
    info: 'Info',
    skipped: 'Skipped'
  };

  const [summaryData, setSummaryData] = useState({
    ok: 0,
    alarm: 0,
    error: 0,
    info: 0,
    skipped: 0
  });

  const complianceOptions = {
    gcp: [
      { id: "cft_scorecard_v1", name: "CFT Scorecard v1" },
      { id: "cis_v120", name: "CIS v1.2.0" },
      { id: "cis_v130", name: "CIS v1.3.0" },
      { id: "cis_v200", name: "CIS v2.0.0" },
      { id: "cis_v300", name: "CIS v3.0.0" },
      { id: "hipaa", name: "HIPAA" },
      { id: "nist_800_53_rev_5", name: "NIST 800-53 Rev 5" },
      { id: "nist_csf_v10", name: "NIST CSF v1.0" },
      { id: "pci_dss_v321", name: "PCI DSS v3.2.1" },
      { id: "soc_2_2017", name: "SOC 2 2017" },
      { id: "all_controls", name: "All Controls" },
      { id: "forseti_security_v226", name: "Forseti Security v2.26" },
    ],
    aws: [
      { id: "acsc_essential_eight", name: "ACSC Essential Eight" },
      { id: "all_controls", name: "All Controls" },
      { id: "audit_manager_control_tower", name: "Audit Manager Control Tower" },
      { id: "cis_compute_service_v100", name: "CIS Compute Service v1.0.0" },
      { id: "cis_controls_v8_ig1", name: "CIS Controls v8 IG1" },
      { id: "cis_v120", name: "CIS v1.2.0" },
      { id: "cis_v130", name: "CIS v1.3.0" },
      { id: "cis_v200", name: "CIS v2.0.0" },
      { id: "cis_v140", name: "CIS v1.4.0" },
      { id: "cis_v150", name: "CIS v1.5.0" },
      { id: "cis_v300", name: "CIS v3.0.0" },
      { id: "cis_v400", name: "CIS v4.0.0" },
      { id: "cisa_cyber_essentials", name: "CISA Cyber Essentials" },
      { id: "conformance_pack", name: "Conformance Pack" },
      { id: "fedramp_low_rev_4", name: "FedRAMP Low Rev4" },
      { id: "fedramp_moderate_rev_4", name: "FedRAMP Moderate Rev4" },
      { id: "ffiec", name: "FFIEC" },
      { id: "foundational_security", name: "Foundational Security" },
      { id: "gdpr", name: "GDPR" },
      { id: "gxp_21_cfr_part_11", name: "GxP 21 CFR Part 11" },
      { id: "gxp_eu_annex_11", name: "GxP EU Annex 11" },
      { id: "hipaa_final_omnibus_security_rule_2013", name: "HIPAA Final Omnibus 2013" },
      { id: "hipaa_security_rule_2003", name: "HIPAA Security Rule 2003" },
      { id: "nist_800_53_rev_4", name: "NIST 800-53 Rev4" },
      { id: "nist_800_53_rev_5", name: "NIST 800-53 Rev5" },
      { id: "nist_800_171_rev_2", name: "NIST 800-171 Rev2" },
      { id: "nist_800_172", name: "NIST 800-172" },
      { id: "nist_csf", name: "NIST CSF" },
      { id: "pci_dss_v321", name: "PCI DSS v3.2.1" },
      { id: "rbi_cyber_security", name: "RBI Cyber Security" },
      { id: "rbi_itf_nbfc", name: "RBI ITF NBFC" },
      { id: "soc_2", name: "SOC 2" }
    ],
    azure: [
      { id: "all_controls", name: "All Controls" },
      { id: "cis_v130", name: "CIS v1.3.0" },
      { id: "cis_v140", name: "CIS v1.4.0" },
      { id: "cis_v150", name: "CIS v1.5.0" },
      { id: "cis_v200", name: "CIS v2.0.0" },
      { id: "cis_v210", name: "CIS v2.1.0" },
      { id: "cis_v300", name: "CIS v3.0.0" },
      { id: "fedramp_high", name: "FedRAMP High" },
      { id: "hipaa_hitrust_v92", name: "HIPAA HITRUST v9.2" },
      { id: "nist_sp_800_53_rev_5", name: "NIST SP 800-53 Rev 5" },
      { id: "nist_sp_800_171_rev_2", name: "NIST SP 800-171 Rev 2" },
      { id: "pci_dss_v321", name: "PCI DSS v3.2.1" },
      { id: "rbi_itf_nbfc_v2017", name: "RBI ITF NBFC v2017" },
      { id: "regulatory_compliance", name: "Regulatory Compliance" }
    ]
  };

  const toggleDarkMode = () => {
    const newMode = !darkMode;
    setDarkMode(newMode);
    if (typeof window !== 'undefined') {
      localStorage.setItem('darkMode', JSON.stringify(newMode));
    }
  };

  useEffect(() => {
    const controller = new AbortController();
    const signal = controller.signal;
    
    const fetchOrganizationId = async () => {
      setOrgLoadingState(prev => ({
        ...prev,
        [activeProvider]: true
      }));
      
      setActiveRequests(prev => ({
        ...prev,
        [activeProvider]: { ...prev[activeProvider], org: true }
      }));
      
      if (orgIdCache[activeProvider]) {
        setOrganizationId(orgIdCache[activeProvider]);
        setOrgError(null);
        
        setOrgLoadingState(prev => ({
          ...prev,
          [activeProvider]: false
        }));
        
        setActiveRequests(prev => ({
          ...prev,
          [activeProvider]: { ...prev[activeProvider], org: false }
        }));
        
        return;
      }
      
      setProjects([]);
      setSelectedProject("");
      
      if (activeProvider === 'azure') {
        try {
          let body = {};
          
          // Add authentication configuration if using secret manager
          if (authConfig.type === 'secret' && authConfig.secretReference) {
            body.secret_reference = authConfig.secretReference;
            if (authConfig.smCredentials) {
              body.sm_credentials = authConfig.smCredentials;
            }
          }

          const response = await fetch('/api/azure/get-tenant', {
            method: Object.keys(body).length > 0 ? 'POST' : 'GET',
            headers: {
              ...getApiHeaders(),
              'Accept': 'application/json'
            },
            body: Object.keys(body).length > 0 ? JSON.stringify(body) : undefined,
            signal
          });
  
          if (signal.aborted) return;
          
          if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
  
          const data = await response.json();
          
          if (signal.aborted) return;
          
          console.log("Backend Response:", data); 
          if (data.status === 'success') {
            const tenantId = data.tenant.tenant_id;
            setOrganizationId(tenantId);
            setOrgIdCache(prev => ({
              ...prev,
              azure: tenantId
            }));
            setOrgError(null);
          } else {
            throw new Error(data.error || 'Failed to fetch tenant ID');
          }
        } catch (err) {
          if (err.name === 'AbortError') {
            console.log('Fetch aborted');
            return;
          }
          
          setOrgError(err.message);
          setOrganizationId(null);
        } finally {
          if (!signal.aborted) {
            setOrgLoadingState(prev => ({
              ...prev,
              [activeProvider]: false
            }));
            
            setActiveRequests(prev => ({
              ...prev,
              [activeProvider]: { ...prev[activeProvider], org: false }
            }));
          }
        }
        return;
      }
  
      try {
        const endpoint = activeProvider === 'aws' 
          ? '/api/aws/get-aws-org-id' 
          : '/api/gcp/get-org-id';
        
        let body = {};
        
        // Add authentication configuration if using secret manager
        if (authConfig.type === 'secret' && authConfig.secretReference) {
          body.secret_reference = authConfig.secretReference;
          if (authConfig.smCredentials) {
            body.sm_credentials = authConfig.smCredentials;
          }
        }
        
        const response = await fetch(endpoint, { 
          method: Object.keys(body).length > 0 ? 'POST' : 'GET',
          headers: getApiHeaders(),
          body: Object.keys(body).length > 0 ? JSON.stringify(body) : undefined,
          signal 
        });
        
        if (signal.aborted) return;
        
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        
        const data = await response.json();
        
        if (signal.aborted) return;
        
        if (data.status === 'success') {
          const orgId = data.organization_id || data.account_id;
          setOrganizationId(orgId);
          setOrgIdCache(prev => ({
            ...prev,
            [activeProvider]: orgId
          }));
          setOrgError(null);
        } else {
          throw new Error(data.error || 'Failed to fetch organization ID');
        }
      } catch (err) {
        if (err.name === 'AbortError') {
          console.log('Fetch aborted');
          return;
        }
        
        setOrgError(err.message);
        setOrganizationId(null);
      } finally {
        if (!signal.aborted) {
          setOrgLoadingState(prev => ({
            ...prev,
            [activeProvider]: false
          }));
          
          setActiveRequests(prev => ({
            ...prev,
            [activeProvider]: { ...prev[activeProvider], org: false }
          }));
        }
      }
    };

    fetchOrganizationId();
    
    return () => {
      controller.abort();
    };
  }, [activeProvider, authConfig]);

  useEffect(() => {
    if (!organizationId) return;
    
    const controller = new AbortController();
    const signal = controller.signal;
    
    const fetchProjects = async () => {
      setProjectsLoadingState(prev => ({
        ...prev,
        [activeProvider]: true
      }));
      
      setActiveRequests(prev => ({
        ...prev,
        [activeProvider]: { ...prev[activeProvider], projects: true }
      }));
      
      if (projectsCache[activeProvider] && projectsCache[activeProvider].length > 0) {
        setProjects(projectsCache[activeProvider]);
        setSelectedProject(projectsCache[activeProvider][0].project_id || 
                          projectsCache[activeProvider][0].subscription_id || 
                          "all_projects");
        setProjectsError(null);
        
        setProjectsLoadingState(prev => ({
          ...prev,
          [activeProvider]: false
        }));
        
        setActiveRequests(prev => ({
          ...prev,
          [activeProvider]: { ...prev[activeProvider], projects: false }
        }));
        
        return;
      }
  
      try {
        setProjectsError(null);
  
        let endpoint, body;
        if (activeProvider === 'azure') {
          endpoint = '/api/azure/get-subscriptions';
          body = { tenant_id: organizationId };
        } else if (activeProvider === 'aws') {
          endpoint = '/api/aws/get-accounts';
          body = { organization_id: organizationId };
        } else {
          endpoint = '/api/gcp/get-projects';
          body = { organization_id: organizationId };
        }

        // Add authentication configuration if using secret manager
        if (authConfig.type === 'secret' && authConfig.secretReference) {
          body.secret_reference = authConfig.secretReference;
          if (authConfig.smCredentials) {
            body.sm_credentials = authConfig.smCredentials;
          }
        }
  
        const response = await fetch(endpoint, {
          method: 'POST',
          headers: getApiHeaders(),
          body: JSON.stringify(body),
          signal
        });
        
        if (signal.aborted) return;
  
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
  
        const data = await response.json();
        
        if (signal.aborted) return;
        
        console.log("API Response:", data);
  
        if (data.status === 'success') {
          const fetchedProjects = data.projects || data.subscriptions || [];
          
          const allProjectsOption = {
            project_id: "all_projects",
            name: `All ${activeProvider === 'aws' ? 'Accounts' : 'Projects'} (Aggregated)`,
            project_number: "N/A"
          };
          
          const fullProjectsList = [allProjectsOption, ...fetchedProjects];
          
          setProjects(fullProjectsList);
          
          setProjectsCache(prev => ({
            ...prev,
            [activeProvider]: fullProjectsList
          }));
  
          if (fetchedProjects.length > 0) {
            setSelectedProject("all_projects");
          }
        } else {
          throw new Error(data.error || 'Failed to fetch data');
        }
      } catch (err) {
        if (err.name === 'AbortError') {
          console.log('Fetch aborted');
          return;
        }
        
        console.error("Error fetching projects/subscriptions:", err);
        setProjectsError(err.message);
        setProjects([]);
      } finally {
        if (!signal.aborted) {
          setProjectsLoadingState(prev => ({
            ...prev,
            [activeProvider]: false
          }));
          
          setActiveRequests(prev => ({
            ...prev,
            [activeProvider]: { ...prev[activeProvider], projects: false }
          }));
        }
      }
    };
  
    fetchProjects();
    
    return () => {
      controller.abort();
    };
  }, [organizationId, activeProvider, authConfig]);

  useEffect(() => {
    setOrgLoadingState(prev => ({
      ...prev,
      [activeProvider]: activeRequests[activeProvider].org
    }));
    
    setProjectsLoadingState(prev => ({
      ...prev,
      [activeProvider]: activeRequests[activeProvider].projects
    }));
  }, [activeProvider, activeRequests]);

  const AzureComingSoon = () => (
    <div className="flex flex-col items-center justify-center p-8 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
      <CloudLightning className="w-16 h-16 text-blue-600 mb-4" />
      <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Azure Support Coming Soon</h2>
      <p className="text-gray-600 dark:text-gray-400 text-center">
        We're working on adding Azure cloud support. Check back later for updates!
      </p>
    </div>
  );

  const toggleFilter = (status) => {
    setSelectedFilters(prev => ({
      ...prev,
      [status]: !prev[status]
    }));
  };

  const toggleControl = (controlId) => {
    setExpandedControls(prev => ({
      ...prev,
      [controlId]: !prev[controlId]
    }));
  };
  
  const processBenchmarkResults = (jsonData, projectId = null) => {
    if (!jsonData?.benchmark_results?.groups) {
      throw new Error('Invalid response format: missing benchmark_results or groups');
    }
  
    setControlsData(jsonData);
  
    const allControls = [];
    const extractControls = (groups) => {
      groups.forEach(group => {
        if (group.controls && Array.isArray(group.controls)) {
          group.controls.forEach(control => {
            if (control.results && Array.isArray(control.results)) {
              control.results = control.results.map(result => {
                const projectTag = result.dimensions?.find(
                  dim => ['project_id', 'account_id', 'subscription_id'].includes(dim.key)
                );
  
                if (projectTag) {
                  result.tags = result.tags || {};
                  result.tags.project_id = projectTag.value;
                }
  
                return result;
              });
            }
          });
          allControls.push(...group.controls);
        }
        if (group.groups && Array.isArray(group.groups)) {
          extractControls(group.groups);
        }
      });
    };
  
    extractControls(jsonData.benchmark_results.groups);
  
    const normalizedControls = allControls.map(control => {
      const enrichedControl = projectId ? { ...control, projectId } : control;
  
      let processedResults = enrichedControl.results || [];
      if (Array.isArray(processedResults)) {
        processedResults = processedResults.map(result => {
          if (result.status === 'error' && result.reason && isApiNotEnabledError(result.reason)) {
            return { ...result, status: 'skipped' };
          }
          return result;
        });
      }
  
      const summary = {
        ok: 0,
        alarm: 0,
        error: 0,
        info: 0,
        skipped: 0
      };
  
      processedResults.forEach(result => {
        const status = result.status in summary ? result.status : 'error';
        summary[status]++;
      });
  
      const category = control.control_id.split('.')[0] || 'Uncategorized';
  
      return {
        ...enrichedControl,
        results: processedResults,
        summary,
        status: enrichedControl.status || 'info',
        category
      };
    });
  
    return normalizedControls;
  };

  const runBenchmark = async () => {
    if (!selectedProject || !selectedCompliance) {
      setError("Please select both a project/account/subscription and a compliance benchmark");
      return;
    }
    
    // No longer blocking GCP all projects - users can wait if they want the full scan
  
    setControlsData(null);
    setData(null);
    setError(null);
    setLoading(true);
    
    let progressCounter = 0;
    const progressInterval = setInterval(() => {
      progressCounter += 5;
      if (progressCounter > 95) clearInterval(progressInterval);
      else setProgress(progressCounter);
    }, 300);
  
    try {
      let endpoint, payload;
      if (activeProvider === 'azure') {
        endpoint = '/api/azure/run-azure-benchmark';
        payload = {
          benchmark: selectedCompliance,
          subscription_id: selectedProject === "all_projects" ? "azure" : selectedProject
        };
      } else if (activeProvider === 'aws') {
        endpoint = '/api/aws/run-aws-benchmark';
        payload = {
          benchmark: selectedCompliance,
          account_id: selectedProject === "all_projects" ? "aws" : selectedProject
        };
      } else {
        endpoint = '/api/gcp/run-benchmark';
        payload = {
          project_id: selectedProject === "all_projects" ? "gcp" : selectedProject,
          benchmark: selectedCompliance
        };
      }

      // Add authentication configuration if using secret manager
      if (authConfig.type === 'secret' && authConfig.secretReference) {
        payload.secret_reference = authConfig.secretReference;
        if (authConfig.smCredentials) {
          payload.sm_credentials = authConfig.smCredentials;
        }
      }
  
      // Check if this is likely to be a large response that needs streaming
      const isLargeResponse = (
        (activeProvider === 'aws' && 
         selectedCompliance === 'nist_800_53_rev_5' && 
         selectedProject === 'all_projects') ||
        (activeProvider === 'gcp' && 
         selectedProject === 'all_projects')
      );
  
      if (isLargeResponse) {
        // Handle streaming response
        const response = await fetch(endpoint, {
          method: 'POST',
          headers: getApiHeaders(),
          body: JSON.stringify(payload),
        });
  
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        
        const reader = response.body.getReader();
        const decoder = new TextDecoder("utf-8");
        let completeData = "";
        let isFirstChunk = true;
        let metadata = null;
        
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          
          const chunk = decoder.decode(value, { stream: true });
          
          if (isFirstChunk) {
            // First chunk contains metadata with a newline separator
            const parts = chunk.split('\n', 2);
            if (parts.length > 1) {
              metadata = JSON.parse(parts[0]);
              completeData += parts[1];
            } else {
              completeData += chunk;
            }
            isFirstChunk = false;
            
            // Update progress indicator to show streaming
            clearInterval(progressInterval);
            setProgress(10);
          } else {
            completeData += chunk;
            
            // Update progress based on how much we've received
            if (metadata && metadata.total_size) {
              const progress = Math.floor((completeData.length / metadata.total_size) * 90) + 10;
              setProgress(Math.min(progress, 99));
            }
          }
        }
        
        // Process the complete data
        const result = JSON.parse(completeData);
        const normalizedControls = processBenchmarkResults(result);
        
        setData({ ...result, benchmark_results: normalizedControls });
        setBenchmarkResults({
          title: complianceOptions[activeProvider].find(opt => opt.id === selectedCompliance)?.name || 'Benchmark Results',
          controls: normalizedControls
        });
  
        const summary = normalizedControls.reduce((acc, control) => ({
          ok: acc.ok + (control.summary?.ok || 0),
          alarm: acc.alarm + (control.summary?.alarm || 0),
          error: acc.error + (control.summary?.error || 0),
          info: acc.info + (control.summary?.info || 0),
          skipped: acc.skipped + (control.summary?.skipped || 0)
        }), { ok: 0, alarm: 0, error: 0, info: 0, skipped: 0 });
  
        setSummaryData(summary);
        setControls(normalizedControls);
        setLastRunDate(new Date());
      } else {
        // Handle regular response
        const response = await fetch(endpoint, {
          method: 'POST',
          headers: getApiHeaders(),
          body: JSON.stringify(payload),
        });
  
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
  
        const result = await response.json();
        const normalizedControls = processBenchmarkResults(result);
  
        setData({ ...result, benchmark_results: normalizedControls });
        setBenchmarkResults({
          title: complianceOptions[activeProvider].find(opt => opt.id === selectedCompliance)?.name || 'Benchmark Results',
          controls: normalizedControls
        });
  
        const summary = normalizedControls.reduce((acc, control) => ({
          ok: acc.ok + (control.summary?.ok || 0),
          alarm: acc.alarm + (control.summary?.alarm || 0),
          error: acc.error + (control.summary?.error || 0),
          info: acc.info + (control.summary?.info || 0),
          skipped: acc.skipped + (control.summary?.skipped || 0)
        }), { ok: 0, alarm: 0, error: 0, info: 0, skipped: 0 });
  
        setSummaryData(summary);
        setControls(normalizedControls);
        setLastRunDate(new Date());
      }
    } catch (err) {
      console.error('Benchmark error:', err);
      setError(`Benchmark Error: ${err.message}`);
      setControlsData(null);
      setBenchmarkResults({ title: 'Benchmark Results', controls: [] });
      setSummaryData({ ok: 0, alarm: 0, error: 0, info: 0, skipped: 0 });
      setControls([]);
    } finally {
      clearInterval(progressInterval);
      setProgress(100);
      setTimeout(() => setProgress(0), 1000);
      setLoading(false);
      setAllProjectsBenchmarkProgress({
        current: 0,
        total: 0,
        inProgress: false,
        currentProject: '',
        completedProjects: []
      });
    }
  };

  useEffect(() => {
    const root = window.document.documentElement;
    if (darkMode) {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }
  }, [darkMode]);

  const toggleFavorite = (projectId) => {
    setFavoriteProjects(prev => 
      prev.includes(projectId) ? 
        prev.filter(p => p !== projectId) : 
        [...prev, projectId]
    );
  };

  const exportToExcel = () => {
    if (!data || !data.benchmark_results) {
      console.error('No benchmark results to export');
      return;
    }

    // Prepare the data for export
    const exportData = data.benchmark_results.flatMap(control => {
      if (!control.results || control.results.length === 0) {
        return [{
          'Control ID': control.control_id,
          'Control Title': control.title,
          'Status': control.status,
          'Category': control.category,
          'Resource': 'N/A',
          'Reason': control.run_error || 'No results',
          'Project/Account/Subscription': 'N/A'
        }];
      }
      
      return control.results.map(result => {
        // Find the project/account/subscription ID from dimensions
        const projectDim = result.dimensions?.find(dim => 
          ['project_id', 'account_id', 'subscription_id'].includes(dim.key)
        );
        
        return {
          'Control ID': control.control_id,
          'Control Title': control.title,
          'Status': result.status,
          'Category': control.category,
          'Resource': result.resource || 'N/A',
          'Reason': result.reason || 'N/A',
          'Project/Account/Subscription': projectDim?.value || 'N/A'
        };
      });
    });

    // Create a worksheet
    const ws = XLSX.utils.json_to_sheet(exportData);
    
    // Create a workbook
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "Benchmark Results");
    
    // Generate file name
    const fileName = `benchmark-results-${activeProvider}-${selectedCompliance}-${new Date().toISOString().split('T')[0]}.xlsx`;
    
    // Export the file
    XLSX.writeFile(wb, fileName);
  };

  const StatusIcon = ({ status, className = "" }) => {
    const baseClasses = `${className}`;
    switch (status?.toLowerCase()) {
      case 'ok': return <CheckCircle className={`${baseClasses} text-green-500`} />;
      case 'alarm': return <AlertCircle className={`${baseClasses} text-red-500`} />;
      case 'error': return <AlertCircle className={`${baseClasses} text-gray-500`} />;
      case 'info': return <Info className={`${baseClasses} text-blue-500`} />;
      case 'skipped': return <SkipForward className={`${baseClasses} text-gray-500`} />;
      default: return null;
    }
  };

  const filteredControls = controls
    .filter(control => {
      const searchLower = searchQuery.toLowerCase();
      const matchesSearch = searchQuery === '' || 
        control.title?.toLowerCase().includes(searchLower) ||
        control.control_id?.toLowerCase().includes(searchLower);
      
      const matchesStatusFilter = Object.keys(selectedFilters).some(status => 
        selectedFilters[status] && (control.summary?.[status] || 0) > 0
      );
      
      return matchesSearch && matchesStatusFilter;
    });

  return (
    <div className="flex min-h-screen">
      <CloudTabs
        activeProvider={activeProvider}
        onProviderChange={setActiveProvider}
      />
      <div className="flex-1">
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-200">
          {(orgLoading || projectsLoading) && (
            <div className="fixed top-0 left-0 right-0 bg-indigo-500 text-white py-2 px-4 flex items-center justify-center z-50 shadow-md">
              <Loader2 className="w-5 h-5 animate-spin mr-2" />
              <span>
                Loading {activeProvider.toUpperCase()} {orgLoading ? 'organization' : ''} 
                {orgLoading && projectsLoading ? ' and ' : ''}
                {projectsLoading ? (activeProvider === 'aws' ? 'accounts' : activeProvider === 'azure' ? 'subscriptions' : 'projects') : ''}...
              </span>
            </div>
          )}
          
          <div className="p-6 max-w-7xl mx-auto">
            <div className="flex justify-between items-center mb-8">
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Compliance Benchmark Dashboard
              </h1>
              <div className="flex items-center gap-4">
                <button
                  onClick={() => setShowSettings(!showSettings)}
                  className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700"
                >
                  <Settings className="w-4 h-4 mr-2" />
                  Settings
                </button>
                <button
                  onClick={toggleDarkMode}
                  className="p-2 rounded-lg bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200"
                  aria-label={darkMode ? "Switch to light mode" : "Switch to dark mode"}
                >
                  {darkMode ? (
                    <Moon className="w-5 h-5 text-yellow-500" />
                  ) : (
                    <Sun className="w-5 h-5 text-gray-700" />
                  )}
                </button>
              </div>
            </div>
  
            {/* Settings Panel */}
            {showSettings && (
              <div className="mb-6">
                <AuthSelector 
                  provider={activeProvider}
                  onAuthChange={(config) => {
                    setAuthConfig(config);
                    console.log('Auth config updated:', config);
                    
                    // Clear caches to force refresh
                    setOrgIdCache({});
                    setProjectsCache({});
                    setOrganizationId(null);
                    setProjects([]);
                    setSelectedProject("");
                  }}
                />
              </div>
            )}
  
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6 transition-all duration-300 hover:shadow-xl">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
                <BarChart3 className="w-5 h-5 text-indigo-500" />
                Benchmark Configuration
              </h2>
  
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-600 rounded-lg p-4 transition-all duration-200 hover:shadow-md">
                  <div className="flex items-center gap-3">
                    <Building2 className="w-5 h-5 text-indigo-500" />
                    <div>
                      <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                        {activeProvider === "azure" ? "Tenant ID" : "Organization ID"}
                      </div>
                      <div className="text-lg font-semibold text-gray-900 dark:text-white">
                        {orgLoading ? (
                          <div className="flex items-center gap-2">
                            <Loader2 className="w-4 h-4 animate-spin" />
                            <span>Loading...</span>
                          </div>
                        ) : orgError ? (
                          <span className="text-red-500 text-sm">{orgError}</span>
                        ) : (
                          <div className="flex items-center">
                            <span className="truncate max-w-xs">{organizationId || "N/A"}</span>
                            {organizationId && (
                              <button
                                className="ml-2 text-indigo-500 hover:text-indigo-600"
                                onClick={() => navigator.clipboard.writeText(organizationId)}
                              >
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  className="h-4 w-4"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"
                                  />
                                </svg>
                              </button>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
  
                <div className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-600 rounded-lg p-4 transition-all duration-200 hover:shadow-md">
                  <div className="flex items-center gap-3">
                    <FolderGit2 className="w-5 h-5 text-indigo-500" />
                    <div className="w-full">
                      <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                        {activeProvider === "aws"
                          ? "AWS Account"
                          : activeProvider === "azure"
                          ? "Azure Subscription"
                          : "GCP Project"}
                      </div>
                      {projectsLoading ? (
                        <div className="flex items-center gap-2 py-2 animate-pulse">
                          <Loader2 className="w-5 h-5 animate-spin text-indigo-500" />
                          <span className="text-indigo-500 font-medium">Loading {activeProvider === "azure" ? "subscriptions" : activeProvider === "aws" ? "accounts" : "projects"}...</span>
                        </div>
                      ) : projectsError ? (
                        <span className="text-red-500 text-sm">{projectsError}</span>
                      ) : projects.length > 0 ? (
                        <div className="relative">
                          <select
                            value={selectedProject}
                            onChange={(e) => setSelectedProject(e.target.value)}
                            className="w-full appearance-none bg-transparent text-lg font-semibold text-gray-900 dark:text-white focus:outline-none pr-8"
                          >
                            <option value="" className="dark:bg-gray-800">
                              Select a {activeProvider === "azure" ? "subscription" : "project"}
                            </option>
                            {favoriteProjects.length > 0 && (
                              <optgroup label="Favorites" className="dark:bg-gray-800">
                                {projects
                                  .filter((p) => favoriteProjects.includes(p.project_id || p.subscription_id))
                                  .map((project) => (
                                    <option
                                      key={`fav-${project.project_id || project.subscription_id}`}
                                      value={project.project_id || project.subscription_id}
                                      className="dark:bg-gray-800"
                                    >
                                      ★ {project.name}
                                    </option>
                                  ))}
                              </optgroup>
                            )}
                            <optgroup
                              label={`All ${activeProvider === "azure" ? "Subscriptions" : "Projects"}`}
                              className="dark:bg-gray-800"
                            >
                              {projects.map((project) => (
                                <option
                                  key={project.project_id || project.subscription_id}
                                  value={project.project_id || project.subscription_id}
                                  className="dark:bg-gray-800"
                                >
                                  {project.name}
                                </option>
                              ))}
                            </optgroup>
                          </select>
                          <div className="absolute inset-y-0 right-0 flex items-center pointer-events-none">
                            <ChevronDown className="w-4 h-4 text-gray-500" />
                          </div>
                        </div>
                      ) : (
                        <div className="text-gray-900 dark:text-white">
                          {activeProvider === "aws"
                            ? "No accounts found"
                            : activeProvider === "azure"
                            ? "No subscriptions found"
                            : "No projects found"}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
  
                <div className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-600 rounded-lg p-4 transition-all duration-200 hover:shadow-md">
                  <div className="flex items-center gap-3">
                    <FileCheck className="w-5 h-5 text-indigo-500" />
                    <div className="w-full">
                      <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                        Compliance Benchmark
                      </div>
                      <div className="flex gap-2">
                        <div className="relative flex-grow">
                          <select
                            value={selectedCompliance}
                            onChange={(e) => setSelectedCompliance(e.target.value)}
                            className="w-full appearance-none bg-transparent text-lg font-semibold text-gray-900 dark:text-white focus:outline-none pr-8"
                          >
                            <option value="">Select a benchmark</option>
                            {complianceOptions[activeProvider].map((option) => (
                              <option key={option.id} value={option.id}>
                                {option.name}
                              </option>
                            ))}
                          </select>
                          <div className="absolute inset-y-0 right-0 flex items-center pointer-events-none">
                            <ChevronDown className="w-4 h-4 text-gray-500" />
                          </div>
                        </div>
                        {selectedCompliance && (
                          <button
                            onClick={handleShowControls}
                            disabled={loading}
                            className="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-200 dark:hover:bg-gray-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 transition-colors duration-200"
                          >
                            {loading ? (
                              <>
                                <Loader2 className="w-4 h-4 animate-spin" />
                                Loading...
                              </>
                            ) : (
                              "Show Controls"
                            )}
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
  
              {lastRunDate && (
                <div className="mt-4 flex items-center text-sm text-gray-500 dark:text-gray-400">
                  <Clock className="w-4 h-4 mr-1" />
                  Last benchmark run: {lastRunDate.toLocaleString()}
                </div>
              )}
  
              <div className="mt-6 flex justify-end">
                <button
                  onClick={runBenchmark}
                  disabled={!selectedProject || !selectedCompliance || loading}
                  className="px-4 py-2 bg-gradient-to-br from-indigo-600 to-indigo-500 text-white rounded-md hover:from-indigo-700 hover:to-indigo-600 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center gap-2 transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  {loading ? (
                    <>
                      <Loader2 className="w-5 h-5 animate-spin" />
                      Running Benchmark...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="w-5 h-5" />
                      Run Benchmark
                    </>
                  )}
                </button>
              </div>
  
              {error && (
                <div className="mt-4 p-4 bg-red-50 dark:bg-red-900/20 text-red-500 rounded-md flex items-start gap-3 animate-fadeIn">
                  <AlertCircle className="w-5 h-5 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="font-medium">Error:</p>
                    <p>{error}</p>
                  </div>
                </div>
              )}
            </div>
  
            {loading && (
              <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 mb-6 overflow-hidden">
                <div
                  className="bg-gradient-to-r from-indigo-500 to-indigo-400 h-2.5 rounded-full transition-all duration-300 ease-in-out"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
            )}
  
            <BenchmarkControls
              data={controlsData}
              isOpen={isControlsOpen}
              onClose={() => setIsControlsOpen(false)}
            />
                
            {data?.benchmark_results && (
              <div className="space-y-6">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900 dark:text-white">{benchmarkResults.title}</h1>
                    <p className="text-gray-500 dark:text-gray-400 mt-1">Last updated: {new Date().toLocaleDateString()}</p>
                  </div>
                  <div className="flex gap-3">
                    <button
                      onClick={exportToExcel}
                      disabled={!data || loading}
                      className={`px-4 py-2 text-sm font-medium rounded-md shadow-sm flex items-center gap-2 ${
                        !data || loading
                          ? 'bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                          : 'bg-green-600 hover:bg-green-700 text-white'
                      }`}
                    >
                      <Download className="w-4 h-4" />
                      Export Results
                    </button>
                    <div className="relative">
                      <button 
                        onClick={() => setIsFilterOpen(!isFilterOpen)}
                        className="px-4 py-2 text-sm font-medium text-white bg-gradient-to-br from-indigo-600 to-indigo-500 rounded-md shadow-sm hover:from-indigo-700 hover:to-indigo-600 flex items-center gap-2"
                      >
                        <Filter className="w-4 h-4" />
                        Filters
                        {isFilterOpen ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
                      </button>
                      {isFilterOpen && (
                        <div className="absolute right-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-md shadow-lg py-2 z-50 border border-gray-200 dark:border-gray-700">
                          <div className="px-4 py-2 border-b border-gray-200 dark:border-gray-700">
                            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-200">Filter by Status</h3>
                          </div>
                          <div className="max-h-60 overflow-y-auto">
                            {Object.entries(selectedFilters).map(([status, isSelected]) => (
                              <label 
                                key={status} 
                                className="flex items-center justify-between px-4 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
                              >
                                <div className="flex items-center gap-2">
                                  <StatusIcon status={status} className="w-4 h-4" />
                                  <span className="text-gray-700 dark:text-white">{statusLabels[status]}</span>
                                </div>
                                <input
                                  type="checkbox"
                                  checked={isSelected}
                                  onChange={() => toggleFilter(status)}
                                  className="rounded text-indigo-600 focus:ring-indigo-500 dark:accent-indigo-500"
                                />
                              </label>
                            ))}
                          </div>
                          <div className="px-4 py-2 border-t border-gray-200 dark:border-gray-700">
                            <div className="flex justify-between">
                              <button
                                onClick={() => setSelectedFilters({
                                  ok: true,
                                  alarm: true,
                                  error: true,
                                  info: true,
                                  skipped: true
                                })}
                                className="text-xs text-indigo-600 dark:text-indigo-400 hover:underline"
                              >
                                Select All
                              </button>
                              <button
                                onClick={() => setSelectedFilters({
                                  ok: false,
                                  alarm: false,
                                  error: false,
                                  info: false,
                                  skipped: false
                                })}
                                className="text-xs text-indigo-600 dark:text-indigo-400 hover:underline"
                              >
                                Clear All
                              </button>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
  
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 mb-8">
                  {statusTypes.map((status) => (
                    <div key={status} className="bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-700 rounded-lg shadow-sm p-6 flex items-center gap-4 hover:shadow-md transition-shadow duration-200">
                      <StatusIcon status={status} className="w-8 h-8" />
                      <div>
                        <div className="text-2xl font-bold text-gray-900 dark:text-white">
                          {summaryData[status] || 0}
                        </div>
                        <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                          {statusLabels[status]}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
  
                <div className="space-y-4">
                  {filteredControls.map((control) => (
                    <div key={control.control_id} className="bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-700 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                      <button
                        onClick={() => toggleControl(control.control_id)}
                        className="w-full px-6 py-4 flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700 text-left focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 rounded-lg"
                      >
                        <div className="flex items-center gap-3">
                          {expandedControls[control.control_id] ? 
                            <ChevronDown className="w-5 h-5 text-gray-400" /> : 
                            <ChevronRight className="w-5 h-5 text-gray-400" />
                          }
                          <div>
                            <span className="text-lg font-medium text-gray-900 dark:text-white">{control.title}</span>
                            <div className="text-sm text-gray-500 dark:text-gray-400">Control ID: {control.control_id}</div>
                          </div>
                        </div>
                        <div className="flex items-center gap-4">
                          {Object.entries(control.summary || {}).map(([status, count]) => (
                            count > 0 && (
                              <div key={status} className="flex items-center gap-1 bg-gray-50 dark:bg-gray-700 px-3 py-1 rounded-full">
                                <StatusIcon status={status} className="w-5 h-5" />
                                <span className="text-sm font-medium text-gray-600 dark:text-gray-300">{count}</span>
                              </div>
                            )
                          ))}
                        </div>
                      </button>
                      
                      {expandedControls[control.control_id] && (
                        <div className="px-6 py-4 border-t border-gray-100 dark:border-gray-700">
                          {control.results ? (
                            <div className="space-y-4">
                              {control.results.map((result, index) => (
                                <div key={index} className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                  <div className="flex items-start gap-3">
                                    <StatusIcon status={result.status} className="w-5 h-5 mt-1" />
                                    <div>
                                      <div className="text-sm text-gray-900 dark:text-white font-medium">{result.reason}</div>
                                      {result.resource && (
                                        <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                          Resource: <code className="px-2 py-1 bg-gray-100 dark:bg-gray-600 rounded">{result.resource}</code>
                                        </div>
                                      )}
                                      {Array.isArray(result.dimensions) && result.dimensions.length > 0 && result.dimensions.map((dimension, index) => {
                                        const labelMap = {
                                          'gcp': 'Project Id:',
                                          'aws': 'Account Id:',
                                          'azure': 'Subscription Id:'
                                        };
                                        
                                        const label = labelMap[activeProvider] || 'Dimension:';
                                        
                                        return (
                                          <div key={index} className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                            {label} <code>{dimension.value}</code>
                                          </div>
                                        );
                                      })}
                                      {result.status === 'skipped' && isApiNotEnabledError(result.reason) && (
                                        <div className="text-sm text-gray-500 dark:text-gray-400 mt-1 italic">
                                          (API not enabled for this service)
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          ) : control.run_error ? (
                            <div className={`text-sm p-4 rounded-lg ${
                              isApiNotEnabledError(control.run_error) 
                                ? "text-gray-500 bg-gray-50 dark:bg-gray-700 dark:text-gray-400" 
                                : "text-red-500 bg-red-50 dark:bg-red-900/20"
                            }`}>
                              {isApiNotEnabledError(control.run_error) ? (
                                <>
                                  <span className="font-medium">Skipped:</span> {control.run_error.split('\nDetails:')[0]}
                                  <div className="mt-1 italic">(API not enabled for this service)</div>
                                </>
                              ) : (
                                <>Error: {control.run_error.split('\nDetails:')[0]}</>
                              )}
                            </div>
                          ) : (
                            <div className="text-sm text-gray-500 dark:text-gray-400">No results available</div>
                          )}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BenchmarkDashboard;