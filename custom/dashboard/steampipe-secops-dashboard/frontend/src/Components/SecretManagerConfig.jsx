import React, { useState, useEffect } from 'react';
import { Key, Save, AlertCircle, CheckCircle } from 'lucide-react';

const SecretManagerConfig = ({ provider, onSecretChange }) => {
  const [secretName, setSecretName] = useState('');
  const [projectId, setProjectId] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState(null);

  useEffect(() => {
    // Load saved configuration from localStorage
    const savedConfig = localStorage.getItem(`secretManager_${provider}`);
    if (savedConfig) {
      const config = JSON.parse(savedConfig);
      setSecretName(config.secretName || '');
      setProjectId(config.projectId || process.env.REACT_APP_PROJECT_ID || '');
    } else {
      setProjectId(process.env.REACT_APP_PROJECT_ID || '');
    }
  }, [provider]);

  const handleSave = async () => {
    if (!secretName || !projectId) {
      setSaveStatus({ type: 'error', message: 'Please fill in all fields' });
      return;
    }

    setIsSaving(true);
    setSaveStatus(null);

    try {
      // Save configuration to localStorage
      const config = { secretName, projectId, provider };
      localStorage.setItem(`secretManager_${provider}`, JSON.stringify(config));
      
      // Notify parent component
      if (onSecretChange) {
        onSecretChange(config);
      }

      setSaveStatus({ type: 'success', message: 'Configuration saved successfully' });
      
      // Clear success message after 3 seconds
      setTimeout(() => setSaveStatus(null), 3000);
    } catch (error) {
      console.error('Error saving configuration:', error);
      setSaveStatus({ type: 'error', message: 'Failed to save configuration' });
    } finally {
      setIsSaving(false);
    }
  };

  const getProviderLabel = () => {
    switch (provider) {
      case 'gcp':
        return 'GCP';
      case 'aws':
        return 'AWS';
      case 'azure':
        return 'Azure';
      default:
        return provider.toUpperCase();
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
      <div className="flex items-center mb-4">
        <Key className="w-5 h-5 mr-2 text-gray-600 dark:text-gray-400" />
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          {getProviderLabel()} Secret Manager Configuration
        </h3>
      </div>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            GCP Project ID
          </label>
          <input
            type="text"
            value={projectId}
            onChange={(e) => setProjectId(e.target.value)}
            placeholder="your-gcp-project-id"
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
            The GCP project where your secrets are stored
          </p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Secret Name
          </label>
          <input
            type="text"
            value={secretName}
            onChange={(e) => setSecretName(e.target.value)}
            placeholder={`steampipe-${provider}-credentials`}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          />
          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
            The name of the secret in Google Secret Manager containing {getProviderLabel()} credentials
          </p>
        </div>

        {saveStatus && (
          <div className={`flex items-center p-3 rounded-md ${
            saveStatus.type === 'success' 
              ? 'bg-green-50 text-green-800 dark:bg-green-900/20 dark:text-green-400' 
              : 'bg-red-50 text-red-800 dark:bg-red-900/20 dark:text-red-400'
          }`}>
            {saveStatus.type === 'success' ? (
              <CheckCircle className="w-4 h-4 mr-2" />
            ) : (
              <AlertCircle className="w-4 h-4 mr-2" />
            )}
            <span className="text-sm">{saveStatus.message}</span>
          </div>
        )}

        <button
          onClick={handleSave}
          disabled={isSaving || !secretName || !projectId}
          className={`flex items-center justify-center w-full px-4 py-2 text-sm font-medium text-white rounded-md transition-colors ${
            isSaving || !secretName || !projectId
              ? 'bg-gray-400 cursor-not-allowed'
              : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
          }`}
        >
          {isSaving ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Saving...
            </>
          ) : (
            <>
              <Save className="w-4 h-4 mr-2" />
              Save Configuration
            </>
          )}
        </button>
      </div>

      <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md">
        <p className="text-xs text-blue-800 dark:text-blue-300">
          <strong>Note:</strong> This configuration will be used when the application is deployed to Cloud Run. 
          The secret must exist in Google Secret Manager and the Cloud Run service account must have access to it.
        </p>
      </div>
    </div>
  );
};

export default SecretManagerConfig;