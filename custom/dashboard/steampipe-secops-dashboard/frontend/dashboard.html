<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Steampipe Compliance Dashboard - ADC Version</title>
    <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background-color: #fff;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #2563eb;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #6b7280;
            font-size: 1.1rem;
        }
        
        .tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 30px;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .tab {
            padding: 12px 24px;
            background: none;
            border: none;
            color: #6b7280;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            border-bottom: 2px solid transparent;
            margin-bottom: -2px;
        }
        
        .tab:hover {
            color: #4b5563;
        }
        
        .tab.active {
            color: #2563eb;
            border-bottom-color: #2563eb;
        }
        
        .cloud-card {
            background-color: #fff;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .cloud-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        
        .cloud-title {
            font-size: 1.5rem;
            color: #1f2937;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #10b981;
        }
        
        .status-indicator.offline {
            background-color: #ef4444;
        }
        
        .status-indicator.checking {
            background-color: #f59e0b;
        }
        
        .benchmarks-section {
            margin-top: 20px;
        }
        
        .benchmark-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background-color: #f9fafb;
            margin-bottom: 10px;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }
        
        .benchmark-name {
            color: #374151;
            font-weight: 500;
        }
        
        .run-button {
            background-color: #2563eb;
            color: white;
            border: none;
            padding: 8px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.2s;
        }
        
        .run-button:hover {
            background-color: #1d4ed8;
        }
        
        .run-button:disabled {
            background-color: #9ca3af;
            cursor: not-allowed;
        }
        
        .info-section {
            background-color: #eff6ff;
            padding: 16px;
            border-radius: 6px;
            margin-bottom: 20px;
            border: 1px solid #dbeafe;
        }
        
        .info-section h3 {
            color: #1e40af;
            margin-bottom: 8px;
        }
        
        .info-section p {
            color: #3730a3;
            font-size: 14px;
        }
        
        .jobs-section {
            margin-top: 24px;
        }
        
        .jobs-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .jobs-header h3 {
            color: #374151;
        }
        
        .refresh-button {
            background-color: #f3f4f6;
            color: #374151;
            border: 1px solid #e5e7eb;
            padding: 6px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }
        
        .refresh-button:hover {
            background-color: #e5e7eb;
        }
        
        .job-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            background-color: #f9fafb;
            margin-bottom: 8px;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }
        
        .job-info {
            flex: 1;
        }
        
        .job-title {
            font-weight: 500;
            color: #1f2937;
            margin-bottom: 4px;
        }
        
        .job-details {
            font-size: 13px;
            color: #6b7280;
        }
        
        .job-status {
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 13px;
            font-weight: 500;
        }
        
        .job-status.completed {
            background-color: #d1fae5;
            color: #065f46;
        }
        
        .job-status.failed {
            background-color: #fee2e2;
            color: #991b1b;
        }
        
        .job-status.running {
            background-color: #fed7aa;
            color: #92400e;
        }
        
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        
        .modal-content {
            background-color: white;
            padding: 24px;
            border-radius: 8px;
            max-width: 900px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .modal-title {
            font-size: 1.5rem;
            color: #1f2937;
        }
        
        .close-button {
            background: none;
            border: none;
            font-size: 24px;
            color: #6b7280;
            cursor: pointer;
        }
        
        .close-button:hover {
            color: #374151;
        }
        
        .results-content {
            background-color: #f9fafb;
            padding: 16px;
            border-radius: 6px;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 13px;
            line-height: 1.5;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        
        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #e5e7eb;
            border-top-color: #2563eb;
            border-radius: 50%;
            animation: spin 0.8s linear infinite;
            margin-left: 8px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .error-message {
            background-color: #fee2e2;
            color: #991b1b;
            padding: 12px;
            border-radius: 6px;
            margin-top: 12px;
            font-size: 14px;
        }
        
        .success-message {
            background-color: #d1fae5;
            color: #065f46;
            padding: 12px;
            border-radius: 6px;
            margin-top: 12px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect, useCallback } = React;

        // API endpoints for each service
        const API_ENDPOINTS = {
            gcp: window.location.hostname === 'localhost' 
                ? 'http://localhost:8080' 
                : `https://gcp-${window.location.hostname}`,
            aws: window.location.hostname === 'localhost' 
                ? 'http://localhost:8082' 
                : `https://aws-${window.location.hostname}`,
            azure: window.location.hostname === 'localhost' 
                ? 'http://localhost:8083' 
                : `https://azure-${window.location.hostname}`
        };

        // Available benchmarks for each provider
        const BENCHMARKS = {
            gcp: [
                { id: 'cis_v300', name: 'CIS v3.0.0' },
                { id: 'cis_v200', name: 'CIS v2.0.0' },
                { id: 'cis_v120', name: 'CIS v1.2.0' },
                { id: 'nist_800_53_rev_5', name: 'NIST 800-53 Rev 5' },
                { id: 'nist_csf_v10', name: 'NIST CSF v1.0' }
            ],
            aws: [
                { id: 'cis_v300', name: 'CIS v3.0.0' },
                { id: 'cis_v200', name: 'CIS v2.0.0' },
                { id: 'cis_v150', name: 'CIS v1.5.0' },
                { id: 'foundational_security', name: 'AWS Foundational Security' },
                { id: 'nist_800_53_rev_5', name: 'NIST 800-53 Rev 5' }
            ],
            azure: [
                { id: 'cis_v210', name: 'CIS v2.1.0' },
                { id: 'cis_v200', name: 'CIS v2.0.0' },
                { id: 'cis_v130', name: 'CIS v1.3.0' },
                { id: 'hipaa_hitrust_v92', name: 'HIPAA HITRUST v9.2' },
                { id: 'nist_sp_800_53_rev_5', name: 'NIST SP 800-53 Rev 5' }
            ]
        };

        function Dashboard() {
            const [activeProvider, setActiveProvider] = useState('gcp');
            const [serviceStatus, setServiceStatus] = useState({
                gcp: 'checking',
                aws: 'checking',
                azure: 'checking'
            });
            const [jobs, setJobs] = useState({
                gcp: [],
                aws: [],
                azure: []
            });
            const [loading, setLoading] = useState({});
            const [selectedResult, setSelectedResult] = useState(null);
            const [error, setError] = useState(null);
            const [success, setSuccess] = useState(null);

            // Check service status
            const checkServiceStatus = useCallback(async (provider) => {
                try {
                    const response = await fetch(`${API_ENDPOINTS[provider]}/health`);
                    setServiceStatus(prev => ({
                        ...prev,
                        [provider]: response.ok ? 'online' : 'offline'
                    }));
                } catch (error) {
                    setServiceStatus(prev => ({
                        ...prev,
                        [provider]: 'offline'
                    }));
                }
            }, []);

            // Fetch jobs for a provider
            const fetchJobs = useCallback(async (provider) => {
                try {
                    const response = await fetch(`${API_ENDPOINTS[provider]}/api/${provider}/jobs`);
                    if (response.ok) {
                        const data = await response.json();
                        setJobs(prev => ({
                            ...prev,
                            [provider]: data.jobs || []
                        }));
                    }
                } catch (error) {
                    console.error(`Error fetching jobs for ${provider}:`, error);
                }
            }, []);

            // Run benchmark
            const runBenchmark = async (provider, benchmarkId) => {
                setError(null);
                setSuccess(null);
                setLoading(prev => ({ ...prev, [`${provider}-${benchmarkId}`]: true }));

                try {
                    const payload = {
                        benchmark: benchmarkId,
                        // No customer_id or secret_reference needed with ADC
                    };

                    // Add default project/account/subscription for "all" scans
                    if (provider === 'gcp') {
                        payload.project_id = 'all-projects';
                    } else if (provider === 'aws') {
                        payload.account_id = 'all-accounts';
                    } else if (provider === 'azure') {
                        payload.subscription_id = 'all-subscriptions';
                    }

                    const response = await fetch(`${API_ENDPOINTS[provider]}/api/${provider}/benchmark`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(payload)
                    });

                    const data = await response.json();

                    if (response.ok) {
                        setSuccess(`Benchmark started successfully! Job ID: ${data.job_id}`);
                        // Refresh jobs after starting
                        setTimeout(() => fetchJobs(provider), 1000);
                    } else {
                        setError(data.error || 'Failed to start benchmark');
                    }
                } catch (error) {
                    setError(`Error: ${error.message}`);
                } finally {
                    setLoading(prev => ({ ...prev, [`${provider}-${benchmarkId}`]: false }));
                }
            };

            // View job result
            const viewJobResult = async (provider, jobId) => {
                try {
                    const response = await fetch(`${API_ENDPOINTS[provider]}/api/${provider}/job/${jobId}/result`);
                    const data = await response.json();
                    
                    if (response.ok) {
                        // Handle different response formats
                        let results = data;
                        
                        // Extract benchmark_results if present
                        if (data.benchmark_results) {
                            results = data.benchmark_results;
                        } else if (data.result && data.result.benchmark_results) {
                            results = data.result.benchmark_results;
                        } else if (data.groups) {
                            results = data;
                        }
                        
                        setSelectedResult({
                            provider,
                            jobId,
                            data: results
                        });
                    } else {
                        setError(data.error || 'Failed to fetch results');
                    }
                } catch (error) {
                    setError(`Error fetching results: ${error.message}`);
                }
            };

            // Initialize
            useEffect(() => {
                // Check status for all providers
                Object.keys(API_ENDPOINTS).forEach(provider => {
                    checkServiceStatus(provider);
                    fetchJobs(provider);
                });

                // Set up periodic refresh
                const interval = setInterval(() => {
                    Object.keys(API_ENDPOINTS).forEach(provider => {
                        checkServiceStatus(provider);
                        fetchJobs(provider);
                    });
                }, 30000); // Refresh every 30 seconds

                return () => clearInterval(interval);
            }, [checkServiceStatus, fetchJobs]);

            // Clear messages after 5 seconds
            useEffect(() => {
                if (error || success) {
                    const timer = setTimeout(() => {
                        setError(null);
                        setSuccess(null);
                    }, 5000);
                    return () => clearTimeout(timer);
                }
            }, [error, success]);

            return (
                <div className="container">
                    <div className="header">
                        <h1>Steampipe Compliance Dashboard</h1>
                        <p>Unified compliance scanning using Application Default Credentials (ADC)</p>
                    </div>

                    {error && <div className="error-message">{error}</div>}
                    {success && <div className="success-message">{success}</div>}

                    <div className="tabs">
                        <button 
                            className={`tab ${activeProvider === 'gcp' ? 'active' : ''}`}
                            onClick={() => setActiveProvider('gcp')}
                        >
                            Google Cloud Platform
                        </button>
                        <button 
                            className={`tab ${activeProvider === 'aws' ? 'active' : ''}`}
                            onClick={() => setActiveProvider('aws')}
                        >
                            Amazon Web Services
                        </button>
                        <button 
                            className={`tab ${activeProvider === 'azure' ? 'active' : ''}`}
                            onClick={() => setActiveProvider('azure')}
                        >
                            Microsoft Azure
                        </button>
                    </div>

                    <div className="cloud-card">
                        <div className="cloud-header">
                            <div className="cloud-title">
                                {activeProvider.toUpperCase()} Compliance Scans
                            </div>
                            <div className={`status-indicator ${serviceStatus[activeProvider]}`} />
                        </div>

                        <div className="info-section">
                            <h3>Authentication Mode: ADC</h3>
                            <p>
                                This service uses Application Default Credentials. 
                                {activeProvider === 'gcp' && ' Ensure GOOGLE_APPLICATION_CREDENTIALS is set or running on GCP.'}
                                {activeProvider === 'aws' && ' Ensure AWS credentials are configured via IAM role or AWS CLI.'}
                                {activeProvider === 'azure' && ' Ensure Azure credentials are configured via managed identity or Azure CLI.'}
                            </p>
                        </div>

                        <div className="benchmarks-section">
                            <h3 style={{ marginBottom: '16px', color: '#374151' }}>Available Benchmarks</h3>
                            {BENCHMARKS[activeProvider].map(benchmark => (
                                <div key={benchmark.id} className="benchmark-item">
                                    <span className="benchmark-name">{benchmark.name}</span>
                                    <button 
                                        className="run-button"
                                        onClick={() => runBenchmark(activeProvider, benchmark.id)}
                                        disabled={loading[`${activeProvider}-${benchmark.id}`] || serviceStatus[activeProvider] !== 'online'}
                                    >
                                        {loading[`${activeProvider}-${benchmark.id}`] ? (
                                            <>Running<span className="loading" /></>
                                        ) : (
                                            'Run'
                                        )}
                                    </button>
                                </div>
                            ))}
                        </div>

                        <div className="jobs-section">
                            <div className="jobs-header">
                                <h3>Recent Jobs</h3>
                                <button 
                                    className="refresh-button"
                                    onClick={() => fetchJobs(activeProvider)}
                                >
                                    Refresh
                                </button>
                            </div>
                            
                            {jobs[activeProvider].length === 0 ? (
                                <p style={{ color: '#6b7280', fontSize: '14px' }}>No jobs found</p>
                            ) : (
                                jobs[activeProvider].slice(0, 10).map(job => (
                                    <div key={job.id} className="job-item">
                                        <div className="job-info">
                                            <div className="job-title">{job.benchmark}</div>
                                            <div className="job-details">
                                                {new Date(job.created_at).toLocaleString()}
                                                {job.project_id && ` • Project: ${job.project_id}`}
                                            </div>
                                        </div>
                                        <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                                            {job.status === 'completed' && (
                                                <button 
                                                    className="refresh-button"
                                                    onClick={() => viewJobResult(activeProvider, job.id)}
                                                    style={{ fontSize: '13px', padding: '4px 12px' }}
                                                >
                                                    View Results
                                                </button>
                                            )}
                                            <span className={`job-status ${job.status}`}>
                                                {job.status}
                                            </span>
                                        </div>
                                    </div>
                                ))
                            )}
                        </div>
                    </div>

                    {selectedResult && (
                        <div className="results-modal" onClick={() => setSelectedResult(null)}>
                            <div className="modal-content" onClick={(e) => e.stopPropagation()}>
                                <div className="modal-header">
                                    <h2 className="modal-title">
                                        Job Results - {selectedResult.jobId}
                                    </h2>
                                    <button 
                                        className="close-button"
                                        onClick={() => setSelectedResult(null)}
                                    >
                                        ×
                                    </button>
                                </div>
                                <div className="results-content">
                                    {JSON.stringify(selectedResult.data, null, 2)}
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            );
        }

        // Render the app
        ReactDOM.render(<Dashboard />, document.getElementById('root'));
    </script>
</body>
</html>