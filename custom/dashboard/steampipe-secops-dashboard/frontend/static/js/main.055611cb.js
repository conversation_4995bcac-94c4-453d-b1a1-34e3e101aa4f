/*! For license information please see main.055611cb.js.LICENSE.txt */
(()=>{"use strict";var e={49:(e,t)=>{var r=Symbol.for("react.element"),n=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),l=Symbol.for("react.profiler"),o=Symbol.for("react.provider"),s=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),f=Symbol.for("react.memo"),h=Symbol.for("react.lazy"),d=Symbol.iterator;var p={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function v(e,t,r){this.props=e,this.context=t,this.refs=g,this.updater=r||p}function b(){}function y(e,t,r){this.props=e,this.context=t,this.refs=g,this.updater=r||p}v.prototype.isReactComponent={},v.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},v.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},b.prototype=v.prototype;var w=y.prototype=new b;w.constructor=y,m(w,v.prototype),w.isPureReactComponent=!0;var T=Array.isArray,E=Object.prototype.hasOwnProperty,S={current:null},x={key:!0,ref:!0,__self:!0,__source:!0};function k(e,t,n){var a,i={},l=null,o=null;if(null!=t)for(a in void 0!==t.ref&&(o=t.ref),void 0!==t.key&&(l=""+t.key),t)E.call(t,a)&&!x.hasOwnProperty(a)&&(i[a]=t[a]);var s=arguments.length-2;if(1===s)i.children=n;else if(1<s){for(var c=Array(s),u=0;u<s;u++)c[u]=arguments[u+2];i.children=c}if(e&&e.defaultProps)for(a in s=e.defaultProps)void 0===i[a]&&(i[a]=s[a]);return{$$typeof:r,type:e,key:l,ref:o,props:i,_owner:S.current}}function _(e){return"object"===typeof e&&null!==e&&e.$$typeof===r}var C=/\/+/g;function A(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function N(e,t,a,i,l){var o=typeof e;"undefined"!==o&&"boolean"!==o||(e=null);var s=!1;if(null===e)s=!0;else switch(o){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case r:case n:s=!0}}if(s)return l=l(s=e),e=""===i?"."+A(s,0):i,T(l)?(a="",null!=e&&(a=e.replace(C,"$&/")+"/"),N(l,t,a,"",(function(e){return e}))):null!=l&&(_(l)&&(l=function(e,t){return{$$typeof:r,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(l,a+(!l.key||s&&s.key===l.key?"":(""+l.key).replace(C,"$&/")+"/")+e)),t.push(l)),1;if(s=0,i=""===i?".":i+":",T(e))for(var c=0;c<e.length;c++){var u=i+A(o=e[c],c);s+=N(o,t,a,u,l)}else if(u=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=d&&e[d]||e["@@iterator"])?e:null}(e),"function"===typeof u)for(e=u.call(e),c=0;!(o=e.next()).done;)s+=N(o=o.value,t,a,u=i+A(o,c++),l);else if("object"===o)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function O(e,t,r){if(null==e)return e;var n=[],a=0;return N(e,n,"","",(function(e){return t.call(r,e,a++)})),n}function R(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var P={current:null},I={transition:null},D={ReactCurrentDispatcher:P,ReactCurrentBatchConfig:I,ReactCurrentOwner:S};function L(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:O,forEach:function(e,t,r){O(e,(function(){t.apply(this,arguments)}),r)},count:function(e){var t=0;return O(e,(function(){t++})),t},toArray:function(e){return O(e,(function(e){return e}))||[]},only:function(e){if(!_(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=v,t.Fragment=a,t.Profiler=l,t.PureComponent=y,t.StrictMode=i,t.Suspense=u,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=D,t.act=L,t.cloneElement=function(e,t,n){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var a=m({},e.props),i=e.key,l=e.ref,o=e._owner;if(null!=t){if(void 0!==t.ref&&(l=t.ref,o=S.current),void 0!==t.key&&(i=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(c in t)E.call(t,c)&&!x.hasOwnProperty(c)&&(a[c]=void 0===t[c]&&void 0!==s?s[c]:t[c])}var c=arguments.length-2;if(1===c)a.children=n;else if(1<c){s=Array(c);for(var u=0;u<c;u++)s[u]=arguments[u+2];a.children=s}return{$$typeof:r,type:e.type,key:i,ref:l,props:a,_owner:o}},t.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:o,_context:e},e.Consumer=e},t.createElement=k,t.createFactory=function(e){var t=k.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:c,render:e}},t.isValidElement=_,t.lazy=function(e){return{$$typeof:h,_payload:{_status:-1,_result:e},_init:R}},t.memo=function(e,t){return{$$typeof:f,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=I.transition;I.transition={};try{e()}finally{I.transition=t}},t.unstable_act=L,t.useCallback=function(e,t){return P.current.useCallback(e,t)},t.useContext=function(e){return P.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return P.current.useDeferredValue(e)},t.useEffect=function(e,t){return P.current.useEffect(e,t)},t.useId=function(){return P.current.useId()},t.useImperativeHandle=function(e,t,r){return P.current.useImperativeHandle(e,t,r)},t.useInsertionEffect=function(e,t){return P.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return P.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return P.current.useMemo(e,t)},t.useReducer=function(e,t,r){return P.current.useReducer(e,t,r)},t.useRef=function(e){return P.current.useRef(e)},t.useState=function(e){return P.current.useState(e)},t.useSyncExternalStore=function(e,t,r){return P.current.useSyncExternalStore(e,t,r)},t.useTransition=function(){return P.current.useTransition()},t.version="18.3.1"},119:(e,t,r)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=r(345)},340:(e,t,r)=>{e.exports=r(761)},345:(e,t,r)=>{var n=r(950),a=r(340);function i(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,r=1;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var l=new Set,o={};function s(e,t){c(e,t),c(e+"Capture",t)}function c(e,t){for(o[e]=t,e=0;e<t.length;e++)l.add(t[e])}var u=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),f=Object.prototype.hasOwnProperty,h=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,d={},p={};function m(e,t,r,n,a,i,l){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=n,this.attributeNamespace=a,this.mustUseProperty=r,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=l}var g={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){g[e]=new m(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];g[t]=new m(t,1,!1,e[1],null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){g[e]=new m(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){g[e]=new m(e,2,!1,e,null,!1,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){g[e]=new m(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((function(e){g[e]=new m(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((function(e){g[e]=new m(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((function(e){g[e]=new m(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((function(e){g[e]=new m(e,5,!1,e.toLowerCase(),null,!1,!1)}));var v=/[\-:]([a-z])/g;function b(e){return e[1].toUpperCase()}function y(e,t,r,n){var a=g.hasOwnProperty(t)?g[t]:null;(null!==a?0!==a.type:n||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,r,n){if(null===t||"undefined"===typeof t||function(e,t,r,n){if(null!==r&&0===r.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!n&&(null!==r?!r.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,r,n))return!0;if(n)return!1;if(null!==r)switch(r.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,r,a,n)&&(r=null),n||null===a?function(e){return!!f.call(p,e)||!f.call(d,e)&&(h.test(e)?p[e]=!0:(d[e]=!0,!1))}(t)&&(null===r?e.removeAttribute(t):e.setAttribute(t,""+r)):a.mustUseProperty?e[a.propertyName]=null===r?3!==a.type&&"":r:(t=a.attributeName,n=a.attributeNamespace,null===r?e.removeAttribute(t):(r=3===(a=a.type)||4===a&&!0===r?"":""+r,n?e.setAttributeNS(n,t,r):e.setAttribute(t,r))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(v,b);g[t]=new m(t,1,!1,e,null,!1,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(v,b);g[t]=new m(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(v,b);g[t]=new m(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!1,!1)})),g.xlinkHref=new m("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!0,!0)}));var w=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,T=Symbol.for("react.element"),E=Symbol.for("react.portal"),S=Symbol.for("react.fragment"),x=Symbol.for("react.strict_mode"),k=Symbol.for("react.profiler"),_=Symbol.for("react.provider"),C=Symbol.for("react.context"),A=Symbol.for("react.forward_ref"),N=Symbol.for("react.suspense"),O=Symbol.for("react.suspense_list"),R=Symbol.for("react.memo"),P=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var I=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var D=Symbol.iterator;function L(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=D&&e[D]||e["@@iterator"])?e:null}var M,F=Object.assign;function j(e){if(void 0===M)try{throw Error()}catch(r){var t=r.stack.trim().match(/\n( *(at )?)/);M=t&&t[1]||""}return"\n"+M+e}var U=!1;function B(e,t){if(!e||U)return"";U=!0;var r=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var n=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){n=c}e.call(t.prototype)}else{try{throw Error()}catch(c){n=c}e()}}catch(c){if(c&&n&&"string"===typeof c.stack){for(var a=c.stack.split("\n"),i=n.stack.split("\n"),l=a.length-1,o=i.length-1;1<=l&&0<=o&&a[l]!==i[o];)o--;for(;1<=l&&0<=o;l--,o--)if(a[l]!==i[o]){if(1!==l||1!==o)do{if(l--,0>--o||a[l]!==i[o]){var s="\n"+a[l].replace(" at new "," at ");return e.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",e.displayName)),s}}while(1<=l&&0<=o);break}}}finally{U=!1,Error.prepareStackTrace=r}return(e=e?e.displayName||e.name:"")?j(e):""}function z(e){switch(e.tag){case 5:return j(e.type);case 16:return j("Lazy");case 13:return j("Suspense");case 19:return j("SuspenseList");case 0:case 2:case 15:return e=B(e.type,!1);case 11:return e=B(e.type.render,!1);case 1:return e=B(e.type,!0);default:return""}}function H(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case S:return"Fragment";case E:return"Portal";case k:return"Profiler";case x:return"StrictMode";case N:return"Suspense";case O:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case C:return(e.displayName||"Context")+".Consumer";case _:return(e._context.displayName||"Context")+".Provider";case A:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case R:return null!==(t=e.displayName||null)?t:H(e.type)||"Memo";case P:t=e._payload,e=e._init;try{return H(e(t))}catch(r){}}return null}function W(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return H(t);case 8:return t===x?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function V(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function $(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function G(e){e._valueTracker||(e._valueTracker=function(e){var t=$(e)?"checked":"value",r=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),n=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof r&&"function"===typeof r.get&&"function"===typeof r.set){var a=r.get,i=r.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){n=""+e,i.call(this,e)}}),Object.defineProperty(e,t,{enumerable:r.enumerable}),{getValue:function(){return n},setValue:function(e){n=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function K(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var r=t.getValue(),n="";return e&&(n=$(e)?e.checked?"true":"false":e.value),(e=n)!==r&&(t.setValue(e),!0)}function Y(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function X(e,t){var r=t.checked;return F({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=r?r:e._wrapperState.initialChecked})}function Q(e,t){var r=null==t.defaultValue?"":t.defaultValue,n=null!=t.checked?t.checked:t.defaultChecked;r=V(null!=t.value?t.value:r),e._wrapperState={initialChecked:n,initialValue:r,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function q(e,t){null!=(t=t.checked)&&y(e,"checked",t,!1)}function J(e,t){q(e,t);var r=V(t.value),n=t.type;if(null!=r)"number"===n?(0===r&&""===e.value||e.value!=r)&&(e.value=""+r):e.value!==""+r&&(e.value=""+r);else if("submit"===n||"reset"===n)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,r):t.hasOwnProperty("defaultValue")&&ee(e,t.type,V(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function Z(e,t,r){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var n=t.type;if(!("submit"!==n&&"reset"!==n||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,r||t===e.value||(e.value=t),e.defaultValue=t}""!==(r=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==r&&(e.name=r)}function ee(e,t,r){"number"===t&&Y(e.ownerDocument)===e||(null==r?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+r&&(e.defaultValue=""+r))}var te=Array.isArray;function re(e,t,r,n){if(e=e.options,t){t={};for(var a=0;a<r.length;a++)t["$"+r[a]]=!0;for(r=0;r<e.length;r++)a=t.hasOwnProperty("$"+e[r].value),e[r].selected!==a&&(e[r].selected=a),a&&n&&(e[r].defaultSelected=!0)}else{for(r=""+V(r),t=null,a=0;a<e.length;a++){if(e[a].value===r)return e[a].selected=!0,void(n&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function ne(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(i(91));return F({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ae(e,t){var r=t.value;if(null==r){if(r=t.children,t=t.defaultValue,null!=r){if(null!=t)throw Error(i(92));if(te(r)){if(1<r.length)throw Error(i(93));r=r[0]}t=r}null==t&&(t=""),r=t}e._wrapperState={initialValue:V(r)}}function ie(e,t){var r=V(t.value),n=V(t.defaultValue);null!=r&&((r=""+r)!==e.value&&(e.value=r),null==t.defaultValue&&e.defaultValue!==r&&(e.defaultValue=r)),null!=n&&(e.defaultValue=""+n)}function le(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function oe(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function se(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?oe(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ce,ue,fe=(ue=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ce=ce||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ce.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,r,n){MSApp.execUnsafeLocalFunction((function(){return ue(e,t)}))}:ue);function he(e,t){if(t){var r=e.firstChild;if(r&&r===e.lastChild&&3===r.nodeType)return void(r.nodeValue=t)}e.textContent=t}var de={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},pe=["Webkit","ms","Moz","O"];function me(e,t,r){return null==t||"boolean"===typeof t||""===t?"":r||"number"!==typeof t||0===t||de.hasOwnProperty(e)&&de[e]?(""+t).trim():t+"px"}function ge(e,t){for(var r in e=e.style,t)if(t.hasOwnProperty(r)){var n=0===r.indexOf("--"),a=me(r,t[r],n);"float"===r&&(r="cssFloat"),n?e.setProperty(r,a):e[r]=a}}Object.keys(de).forEach((function(e){pe.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),de[t]=de[e]}))}));var ve=F({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function be(e,t){if(t){if(ve[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(i(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(i(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(i(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(i(62))}}function ye(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var we=null;function Te(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Ee=null,Se=null,xe=null;function ke(e){if(e=ya(e)){if("function"!==typeof Ee)throw Error(i(280));var t=e.stateNode;t&&(t=Ta(t),Ee(e.stateNode,e.type,t))}}function _e(e){Se?xe?xe.push(e):xe=[e]:Se=e}function Ce(){if(Se){var e=Se,t=xe;if(xe=Se=null,ke(e),t)for(e=0;e<t.length;e++)ke(t[e])}}function Ae(e,t){return e(t)}function Ne(){}var Oe=!1;function Re(e,t,r){if(Oe)return e(t,r);Oe=!0;try{return Ae(e,t,r)}finally{Oe=!1,(null!==Se||null!==xe)&&(Ne(),Ce())}}function Pe(e,t){var r=e.stateNode;if(null===r)return null;var n=Ta(r);if(null===n)return null;r=n[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(n=!n.disabled)||(n=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!n;break e;default:e=!1}if(e)return null;if(r&&"function"!==typeof r)throw Error(i(231,t,typeof r));return r}var Ie=!1;if(u)try{var De={};Object.defineProperty(De,"passive",{get:function(){Ie=!0}}),window.addEventListener("test",De,De),window.removeEventListener("test",De,De)}catch(ue){Ie=!1}function Le(e,t,r,n,a,i,l,o,s){var c=Array.prototype.slice.call(arguments,3);try{t.apply(r,c)}catch(u){this.onError(u)}}var Me=!1,Fe=null,je=!1,Ue=null,Be={onError:function(e){Me=!0,Fe=e}};function ze(e,t,r,n,a,i,l,o,s){Me=!1,Fe=null,Le.apply(Be,arguments)}function He(e){var t=e,r=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(r=t.return),e=t.return}while(e)}return 3===t.tag?r:null}function We(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function Ve(e){if(He(e)!==e)throw Error(i(188))}function $e(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=He(e)))throw Error(i(188));return t!==e?null:e}for(var r=e,n=t;;){var a=r.return;if(null===a)break;var l=a.alternate;if(null===l){if(null!==(n=a.return)){r=n;continue}break}if(a.child===l.child){for(l=a.child;l;){if(l===r)return Ve(a),e;if(l===n)return Ve(a),t;l=l.sibling}throw Error(i(188))}if(r.return!==n.return)r=a,n=l;else{for(var o=!1,s=a.child;s;){if(s===r){o=!0,r=a,n=l;break}if(s===n){o=!0,n=a,r=l;break}s=s.sibling}if(!o){for(s=l.child;s;){if(s===r){o=!0,r=l,n=a;break}if(s===n){o=!0,n=l,r=a;break}s=s.sibling}if(!o)throw Error(i(189))}}if(r.alternate!==n)throw Error(i(190))}if(3!==r.tag)throw Error(i(188));return r.stateNode.current===r?e:t}(e))?Ge(e):null}function Ge(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=Ge(e);if(null!==t)return t;e=e.sibling}return null}var Ke=a.unstable_scheduleCallback,Ye=a.unstable_cancelCallback,Xe=a.unstable_shouldYield,Qe=a.unstable_requestPaint,qe=a.unstable_now,Je=a.unstable_getCurrentPriorityLevel,Ze=a.unstable_ImmediatePriority,et=a.unstable_UserBlockingPriority,tt=a.unstable_NormalPriority,rt=a.unstable_LowPriority,nt=a.unstable_IdlePriority,at=null,it=null;var lt=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(ot(e)/st|0)|0},ot=Math.log,st=Math.LN2;var ct=64,ut=4194304;function ft(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ht(e,t){var r=e.pendingLanes;if(0===r)return 0;var n=0,a=e.suspendedLanes,i=e.pingedLanes,l=268435455&r;if(0!==l){var o=l&~a;0!==o?n=ft(o):0!==(i&=l)&&(n=ft(i))}else 0!==(l=r&~a)?n=ft(l):0!==i&&(n=ft(i));if(0===n)return 0;if(0!==t&&t!==n&&0===(t&a)&&((a=n&-n)>=(i=t&-t)||16===a&&0!==(4194240&i)))return t;if(0!==(4&n)&&(n|=16&r),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=n;0<t;)a=1<<(r=31-lt(t)),n|=e[r],t&=~a;return n}function dt(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function pt(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function mt(){var e=ct;return 0===(4194240&(ct<<=1))&&(ct=64),e}function gt(e){for(var t=[],r=0;31>r;r++)t.push(e);return t}function vt(e,t,r){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-lt(t)]=r}function bt(e,t){var r=e.entangledLanes|=t;for(e=e.entanglements;r;){var n=31-lt(r),a=1<<n;a&t|e[n]&t&&(e[n]|=t),r&=~a}}var yt=0;function wt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var Tt,Et,St,xt,kt,_t=!1,Ct=[],At=null,Nt=null,Ot=null,Rt=new Map,Pt=new Map,It=[],Dt="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Lt(e,t){switch(e){case"focusin":case"focusout":At=null;break;case"dragenter":case"dragleave":Nt=null;break;case"mouseover":case"mouseout":Ot=null;break;case"pointerover":case"pointerout":Rt.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Pt.delete(t.pointerId)}}function Mt(e,t,r,n,a,i){return null===e||e.nativeEvent!==i?(e={blockedOn:t,domEventName:r,eventSystemFlags:n,nativeEvent:i,targetContainers:[a]},null!==t&&(null!==(t=ya(t))&&Et(t)),e):(e.eventSystemFlags|=n,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function Ft(e){var t=ba(e.target);if(null!==t){var r=He(t);if(null!==r)if(13===(t=r.tag)){if(null!==(t=We(r)))return e.blockedOn=t,void kt(e.priority,(function(){St(r)}))}else if(3===t&&r.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===r.tag?r.stateNode.containerInfo:null)}e.blockedOn=null}function jt(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var r=Xt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==r)return null!==(t=ya(r))&&Et(t),e.blockedOn=r,!1;var n=new(r=e.nativeEvent).constructor(r.type,r);we=n,r.target.dispatchEvent(n),we=null,t.shift()}return!0}function Ut(e,t,r){jt(e)&&r.delete(t)}function Bt(){_t=!1,null!==At&&jt(At)&&(At=null),null!==Nt&&jt(Nt)&&(Nt=null),null!==Ot&&jt(Ot)&&(Ot=null),Rt.forEach(Ut),Pt.forEach(Ut)}function zt(e,t){e.blockedOn===t&&(e.blockedOn=null,_t||(_t=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,Bt)))}function Ht(e){function t(t){return zt(t,e)}if(0<Ct.length){zt(Ct[0],e);for(var r=1;r<Ct.length;r++){var n=Ct[r];n.blockedOn===e&&(n.blockedOn=null)}}for(null!==At&&zt(At,e),null!==Nt&&zt(Nt,e),null!==Ot&&zt(Ot,e),Rt.forEach(t),Pt.forEach(t),r=0;r<It.length;r++)(n=It[r]).blockedOn===e&&(n.blockedOn=null);for(;0<It.length&&null===(r=It[0]).blockedOn;)Ft(r),null===r.blockedOn&&It.shift()}var Wt=w.ReactCurrentBatchConfig,Vt=!0;function $t(e,t,r,n){var a=yt,i=Wt.transition;Wt.transition=null;try{yt=1,Kt(e,t,r,n)}finally{yt=a,Wt.transition=i}}function Gt(e,t,r,n){var a=yt,i=Wt.transition;Wt.transition=null;try{yt=4,Kt(e,t,r,n)}finally{yt=a,Wt.transition=i}}function Kt(e,t,r,n){if(Vt){var a=Xt(e,t,r,n);if(null===a)Vn(e,t,n,Yt,r),Lt(e,n);else if(function(e,t,r,n,a){switch(t){case"focusin":return At=Mt(At,e,t,r,n,a),!0;case"dragenter":return Nt=Mt(Nt,e,t,r,n,a),!0;case"mouseover":return Ot=Mt(Ot,e,t,r,n,a),!0;case"pointerover":var i=a.pointerId;return Rt.set(i,Mt(Rt.get(i)||null,e,t,r,n,a)),!0;case"gotpointercapture":return i=a.pointerId,Pt.set(i,Mt(Pt.get(i)||null,e,t,r,n,a)),!0}return!1}(a,e,t,r,n))n.stopPropagation();else if(Lt(e,n),4&t&&-1<Dt.indexOf(e)){for(;null!==a;){var i=ya(a);if(null!==i&&Tt(i),null===(i=Xt(e,t,r,n))&&Vn(e,t,n,Yt,r),i===a)break;a=i}null!==a&&n.stopPropagation()}else Vn(e,t,n,null,r)}}var Yt=null;function Xt(e,t,r,n){if(Yt=null,null!==(e=ba(e=Te(n))))if(null===(t=He(e)))e=null;else if(13===(r=t.tag)){if(null!==(e=We(t)))return e;e=null}else if(3===r){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Yt=e,null}function Qt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Je()){case Ze:return 1;case et:return 4;case tt:case rt:return 16;case nt:return 536870912;default:return 16}default:return 16}}var qt=null,Jt=null,Zt=null;function er(){if(Zt)return Zt;var e,t,r=Jt,n=r.length,a="value"in qt?qt.value:qt.textContent,i=a.length;for(e=0;e<n&&r[e]===a[e];e++);var l=n-e;for(t=1;t<=l&&r[n-t]===a[i-t];t++);return Zt=a.slice(e,1<t?1-t:void 0)}function tr(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function rr(){return!0}function nr(){return!1}function ar(e){function t(t,r,n,a,i){for(var l in this._reactName=t,this._targetInst=n,this.type=r,this.nativeEvent=a,this.target=i,this.currentTarget=null,e)e.hasOwnProperty(l)&&(t=e[l],this[l]=t?t(a):a[l]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?rr:nr,this.isPropagationStopped=nr,this}return F(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=rr)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=rr)},persist:function(){},isPersistent:rr}),t}var ir,lr,or,sr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},cr=ar(sr),ur=F({},sr,{view:0,detail:0}),fr=ar(ur),hr=F({},ur,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:xr,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==or&&(or&&"mousemove"===e.type?(ir=e.screenX-or.screenX,lr=e.screenY-or.screenY):lr=ir=0,or=e),ir)},movementY:function(e){return"movementY"in e?e.movementY:lr}}),dr=ar(hr),pr=ar(F({},hr,{dataTransfer:0})),mr=ar(F({},ur,{relatedTarget:0})),gr=ar(F({},sr,{animationName:0,elapsedTime:0,pseudoElement:0})),vr=F({},sr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),br=ar(vr),yr=ar(F({},sr,{data:0})),wr={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Tr={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Er={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Sr(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=Er[e])&&!!t[e]}function xr(){return Sr}var kr=F({},ur,{key:function(e){if(e.key){var t=wr[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tr(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?Tr[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:xr,charCode:function(e){return"keypress"===e.type?tr(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tr(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),_r=ar(kr),Cr=ar(F({},hr,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Ar=ar(F({},ur,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:xr})),Nr=ar(F({},sr,{propertyName:0,elapsedTime:0,pseudoElement:0})),Or=F({},hr,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Rr=ar(Or),Pr=[9,13,27,32],Ir=u&&"CompositionEvent"in window,Dr=null;u&&"documentMode"in document&&(Dr=document.documentMode);var Lr=u&&"TextEvent"in window&&!Dr,Mr=u&&(!Ir||Dr&&8<Dr&&11>=Dr),Fr=String.fromCharCode(32),jr=!1;function Ur(e,t){switch(e){case"keyup":return-1!==Pr.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Br(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var zr=!1;var Hr={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Wr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Hr[e.type]:"textarea"===t}function Vr(e,t,r,n){_e(n),0<(t=Gn(t,"onChange")).length&&(r=new cr("onChange","change",null,r,n),e.push({event:r,listeners:t}))}var $r=null,Gr=null;function Kr(e){jn(e,0)}function Yr(e){if(K(wa(e)))return e}function Xr(e,t){if("change"===e)return t}var Qr=!1;if(u){var qr;if(u){var Jr="oninput"in document;if(!Jr){var Zr=document.createElement("div");Zr.setAttribute("oninput","return;"),Jr="function"===typeof Zr.oninput}qr=Jr}else qr=!1;Qr=qr&&(!document.documentMode||9<document.documentMode)}function en(){$r&&($r.detachEvent("onpropertychange",tn),Gr=$r=null)}function tn(e){if("value"===e.propertyName&&Yr(Gr)){var t=[];Vr(t,Gr,e,Te(e)),Re(Kr,t)}}function rn(e,t,r){"focusin"===e?(en(),Gr=r,($r=t).attachEvent("onpropertychange",tn)):"focusout"===e&&en()}function nn(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Yr(Gr)}function an(e,t){if("click"===e)return Yr(t)}function ln(e,t){if("input"===e||"change"===e)return Yr(t)}var on="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function sn(e,t){if(on(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(n=0;n<r.length;n++){var a=r[n];if(!f.call(t,a)||!on(e[a],t[a]))return!1}return!0}function cn(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function un(e,t){var r,n=cn(e);for(e=0;n;){if(3===n.nodeType){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=cn(n)}}function fn(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?fn(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function hn(){for(var e=window,t=Y();t instanceof e.HTMLIFrameElement;){try{var r="string"===typeof t.contentWindow.location.href}catch(n){r=!1}if(!r)break;t=Y((e=t.contentWindow).document)}return t}function dn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function pn(e){var t=hn(),r=e.focusedElem,n=e.selectionRange;if(t!==r&&r&&r.ownerDocument&&fn(r.ownerDocument.documentElement,r)){if(null!==n&&dn(r))if(t=n.start,void 0===(e=n.end)&&(e=t),"selectionStart"in r)r.selectionStart=t,r.selectionEnd=Math.min(e,r.value.length);else if((e=(t=r.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var a=r.textContent.length,i=Math.min(n.start,a);n=void 0===n.end?i:Math.min(n.end,a),!e.extend&&i>n&&(a=n,n=i,i=a),a=un(r,i);var l=un(r,n);a&&l&&(1!==e.rangeCount||e.anchorNode!==a.node||e.anchorOffset!==a.offset||e.focusNode!==l.node||e.focusOffset!==l.offset)&&((t=t.createRange()).setStart(a.node,a.offset),e.removeAllRanges(),i>n?(e.addRange(t),e.extend(l.node,l.offset)):(t.setEnd(l.node,l.offset),e.addRange(t)))}for(t=[],e=r;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof r.focus&&r.focus(),r=0;r<t.length;r++)(e=t[r]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var mn=u&&"documentMode"in document&&11>=document.documentMode,gn=null,vn=null,bn=null,yn=!1;function wn(e,t,r){var n=r.window===r?r.document:9===r.nodeType?r:r.ownerDocument;yn||null==gn||gn!==Y(n)||("selectionStart"in(n=gn)&&dn(n)?n={start:n.selectionStart,end:n.selectionEnd}:n={anchorNode:(n=(n.ownerDocument&&n.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:n.anchorOffset,focusNode:n.focusNode,focusOffset:n.focusOffset},bn&&sn(bn,n)||(bn=n,0<(n=Gn(vn,"onSelect")).length&&(t=new cr("onSelect","select",null,t,r),e.push({event:t,listeners:n}),t.target=gn)))}function Tn(e,t){var r={};return r[e.toLowerCase()]=t.toLowerCase(),r["Webkit"+e]="webkit"+t,r["Moz"+e]="moz"+t,r}var En={animationend:Tn("Animation","AnimationEnd"),animationiteration:Tn("Animation","AnimationIteration"),animationstart:Tn("Animation","AnimationStart"),transitionend:Tn("Transition","TransitionEnd")},Sn={},xn={};function kn(e){if(Sn[e])return Sn[e];if(!En[e])return e;var t,r=En[e];for(t in r)if(r.hasOwnProperty(t)&&t in xn)return Sn[e]=r[t];return e}u&&(xn=document.createElement("div").style,"AnimationEvent"in window||(delete En.animationend.animation,delete En.animationiteration.animation,delete En.animationstart.animation),"TransitionEvent"in window||delete En.transitionend.transition);var _n=kn("animationend"),Cn=kn("animationiteration"),An=kn("animationstart"),Nn=kn("transitionend"),On=new Map,Rn="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Pn(e,t){On.set(e,t),s(t,[e])}for(var In=0;In<Rn.length;In++){var Dn=Rn[In];Pn(Dn.toLowerCase(),"on"+(Dn[0].toUpperCase()+Dn.slice(1)))}Pn(_n,"onAnimationEnd"),Pn(Cn,"onAnimationIteration"),Pn(An,"onAnimationStart"),Pn("dblclick","onDoubleClick"),Pn("focusin","onFocus"),Pn("focusout","onBlur"),Pn(Nn,"onTransitionEnd"),c("onMouseEnter",["mouseout","mouseover"]),c("onMouseLeave",["mouseout","mouseover"]),c("onPointerEnter",["pointerout","pointerover"]),c("onPointerLeave",["pointerout","pointerover"]),s("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),s("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),s("onBeforeInput",["compositionend","keypress","textInput","paste"]),s("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ln="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Mn=new Set("cancel close invalid load scroll toggle".split(" ").concat(Ln));function Fn(e,t,r){var n=e.type||"unknown-event";e.currentTarget=r,function(e,t,r,n,a,l,o,s,c){if(ze.apply(this,arguments),Me){if(!Me)throw Error(i(198));var u=Fe;Me=!1,Fe=null,je||(je=!0,Ue=u)}}(n,t,void 0,e),e.currentTarget=null}function jn(e,t){t=0!==(4&t);for(var r=0;r<e.length;r++){var n=e[r],a=n.event;n=n.listeners;e:{var i=void 0;if(t)for(var l=n.length-1;0<=l;l--){var o=n[l],s=o.instance,c=o.currentTarget;if(o=o.listener,s!==i&&a.isPropagationStopped())break e;Fn(a,o,c),i=s}else for(l=0;l<n.length;l++){if(s=(o=n[l]).instance,c=o.currentTarget,o=o.listener,s!==i&&a.isPropagationStopped())break e;Fn(a,o,c),i=s}}}if(je)throw e=Ue,je=!1,Ue=null,e}function Un(e,t){var r=t[ma];void 0===r&&(r=t[ma]=new Set);var n=e+"__bubble";r.has(n)||(Wn(t,e,2,!1),r.add(n))}function Bn(e,t,r){var n=0;t&&(n|=4),Wn(r,e,n,t)}var zn="_reactListening"+Math.random().toString(36).slice(2);function Hn(e){if(!e[zn]){e[zn]=!0,l.forEach((function(t){"selectionchange"!==t&&(Mn.has(t)||Bn(t,!1,e),Bn(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[zn]||(t[zn]=!0,Bn("selectionchange",!1,t))}}function Wn(e,t,r,n){switch(Qt(t)){case 1:var a=$t;break;case 4:a=Gt;break;default:a=Kt}r=a.bind(null,t,r,e),a=void 0,!Ie||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),n?void 0!==a?e.addEventListener(t,r,{capture:!0,passive:a}):e.addEventListener(t,r,!0):void 0!==a?e.addEventListener(t,r,{passive:a}):e.addEventListener(t,r,!1)}function Vn(e,t,r,n,a){var i=n;if(0===(1&t)&&0===(2&t)&&null!==n)e:for(;;){if(null===n)return;var l=n.tag;if(3===l||4===l){var o=n.stateNode.containerInfo;if(o===a||8===o.nodeType&&o.parentNode===a)break;if(4===l)for(l=n.return;null!==l;){var s=l.tag;if((3===s||4===s)&&((s=l.stateNode.containerInfo)===a||8===s.nodeType&&s.parentNode===a))return;l=l.return}for(;null!==o;){if(null===(l=ba(o)))return;if(5===(s=l.tag)||6===s){n=i=l;continue e}o=o.parentNode}}n=n.return}Re((function(){var n=i,a=Te(r),l=[];e:{var o=On.get(e);if(void 0!==o){var s=cr,c=e;switch(e){case"keypress":if(0===tr(r))break e;case"keydown":case"keyup":s=_r;break;case"focusin":c="focus",s=mr;break;case"focusout":c="blur",s=mr;break;case"beforeblur":case"afterblur":s=mr;break;case"click":if(2===r.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":s=dr;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":s=pr;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":s=Ar;break;case _n:case Cn:case An:s=gr;break;case Nn:s=Nr;break;case"scroll":s=fr;break;case"wheel":s=Rr;break;case"copy":case"cut":case"paste":s=br;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":s=Cr}var u=0!==(4&t),f=!u&&"scroll"===e,h=u?null!==o?o+"Capture":null:o;u=[];for(var d,p=n;null!==p;){var m=(d=p).stateNode;if(5===d.tag&&null!==m&&(d=m,null!==h&&(null!=(m=Pe(p,h))&&u.push($n(p,m,d)))),f)break;p=p.return}0<u.length&&(o=new s(o,c,null,r,a),l.push({event:o,listeners:u}))}}if(0===(7&t)){if(s="mouseout"===e||"pointerout"===e,(!(o="mouseover"===e||"pointerover"===e)||r===we||!(c=r.relatedTarget||r.fromElement)||!ba(c)&&!c[pa])&&(s||o)&&(o=a.window===a?a:(o=a.ownerDocument)?o.defaultView||o.parentWindow:window,s?(s=n,null!==(c=(c=r.relatedTarget||r.toElement)?ba(c):null)&&(c!==(f=He(c))||5!==c.tag&&6!==c.tag)&&(c=null)):(s=null,c=n),s!==c)){if(u=dr,m="onMouseLeave",h="onMouseEnter",p="mouse","pointerout"!==e&&"pointerover"!==e||(u=Cr,m="onPointerLeave",h="onPointerEnter",p="pointer"),f=null==s?o:wa(s),d=null==c?o:wa(c),(o=new u(m,p+"leave",s,r,a)).target=f,o.relatedTarget=d,m=null,ba(a)===n&&((u=new u(h,p+"enter",c,r,a)).target=d,u.relatedTarget=f,m=u),f=m,s&&c)e:{for(h=c,p=0,d=u=s;d;d=Kn(d))p++;for(d=0,m=h;m;m=Kn(m))d++;for(;0<p-d;)u=Kn(u),p--;for(;0<d-p;)h=Kn(h),d--;for(;p--;){if(u===h||null!==h&&u===h.alternate)break e;u=Kn(u),h=Kn(h)}u=null}else u=null;null!==s&&Yn(l,o,s,u,!1),null!==c&&null!==f&&Yn(l,f,c,u,!0)}if("select"===(s=(o=n?wa(n):window).nodeName&&o.nodeName.toLowerCase())||"input"===s&&"file"===o.type)var g=Xr;else if(Wr(o))if(Qr)g=ln;else{g=nn;var v=rn}else(s=o.nodeName)&&"input"===s.toLowerCase()&&("checkbox"===o.type||"radio"===o.type)&&(g=an);switch(g&&(g=g(e,n))?Vr(l,g,r,a):(v&&v(e,o,n),"focusout"===e&&(v=o._wrapperState)&&v.controlled&&"number"===o.type&&ee(o,"number",o.value)),v=n?wa(n):window,e){case"focusin":(Wr(v)||"true"===v.contentEditable)&&(gn=v,vn=n,bn=null);break;case"focusout":bn=vn=gn=null;break;case"mousedown":yn=!0;break;case"contextmenu":case"mouseup":case"dragend":yn=!1,wn(l,r,a);break;case"selectionchange":if(mn)break;case"keydown":case"keyup":wn(l,r,a)}var b;if(Ir)e:{switch(e){case"compositionstart":var y="onCompositionStart";break e;case"compositionend":y="onCompositionEnd";break e;case"compositionupdate":y="onCompositionUpdate";break e}y=void 0}else zr?Ur(e,r)&&(y="onCompositionEnd"):"keydown"===e&&229===r.keyCode&&(y="onCompositionStart");y&&(Mr&&"ko"!==r.locale&&(zr||"onCompositionStart"!==y?"onCompositionEnd"===y&&zr&&(b=er()):(Jt="value"in(qt=a)?qt.value:qt.textContent,zr=!0)),0<(v=Gn(n,y)).length&&(y=new yr(y,e,null,r,a),l.push({event:y,listeners:v}),b?y.data=b:null!==(b=Br(r))&&(y.data=b))),(b=Lr?function(e,t){switch(e){case"compositionend":return Br(t);case"keypress":return 32!==t.which?null:(jr=!0,Fr);case"textInput":return(e=t.data)===Fr&&jr?null:e;default:return null}}(e,r):function(e,t){if(zr)return"compositionend"===e||!Ir&&Ur(e,t)?(e=er(),Zt=Jt=qt=null,zr=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Mr&&"ko"!==t.locale?null:t.data}}(e,r))&&(0<(n=Gn(n,"onBeforeInput")).length&&(a=new yr("onBeforeInput","beforeinput",null,r,a),l.push({event:a,listeners:n}),a.data=b))}jn(l,t)}))}function $n(e,t,r){return{instance:e,listener:t,currentTarget:r}}function Gn(e,t){for(var r=t+"Capture",n=[];null!==e;){var a=e,i=a.stateNode;5===a.tag&&null!==i&&(a=i,null!=(i=Pe(e,r))&&n.unshift($n(e,i,a)),null!=(i=Pe(e,t))&&n.push($n(e,i,a))),e=e.return}return n}function Kn(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Yn(e,t,r,n,a){for(var i=t._reactName,l=[];null!==r&&r!==n;){var o=r,s=o.alternate,c=o.stateNode;if(null!==s&&s===n)break;5===o.tag&&null!==c&&(o=c,a?null!=(s=Pe(r,i))&&l.unshift($n(r,s,o)):a||null!=(s=Pe(r,i))&&l.push($n(r,s,o))),r=r.return}0!==l.length&&e.push({event:t,listeners:l})}var Xn=/\r\n?/g,Qn=/\u0000|\uFFFD/g;function qn(e){return("string"===typeof e?e:""+e).replace(Xn,"\n").replace(Qn,"")}function Jn(e,t,r){if(t=qn(t),qn(e)!==t&&r)throw Error(i(425))}function Zn(){}var ea=null,ta=null;function ra(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var na="function"===typeof setTimeout?setTimeout:void 0,aa="function"===typeof clearTimeout?clearTimeout:void 0,ia="function"===typeof Promise?Promise:void 0,la="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof ia?function(e){return ia.resolve(null).then(e).catch(oa)}:na;function oa(e){setTimeout((function(){throw e}))}function sa(e,t){var r=t,n=0;do{var a=r.nextSibling;if(e.removeChild(r),a&&8===a.nodeType)if("/$"===(r=a.data)){if(0===n)return e.removeChild(a),void Ht(t);n--}else"$"!==r&&"$?"!==r&&"$!"!==r||n++;r=a}while(r);Ht(t)}function ca(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function ua(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var r=e.data;if("$"===r||"$!"===r||"$?"===r){if(0===t)return e;t--}else"/$"===r&&t++}e=e.previousSibling}return null}var fa=Math.random().toString(36).slice(2),ha="__reactFiber$"+fa,da="__reactProps$"+fa,pa="__reactContainer$"+fa,ma="__reactEvents$"+fa,ga="__reactListeners$"+fa,va="__reactHandles$"+fa;function ba(e){var t=e[ha];if(t)return t;for(var r=e.parentNode;r;){if(t=r[pa]||r[ha]){if(r=t.alternate,null!==t.child||null!==r&&null!==r.child)for(e=ua(e);null!==e;){if(r=e[ha])return r;e=ua(e)}return t}r=(e=r).parentNode}return null}function ya(e){return!(e=e[ha]||e[pa])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function wa(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(i(33))}function Ta(e){return e[da]||null}var Ea=[],Sa=-1;function xa(e){return{current:e}}function ka(e){0>Sa||(e.current=Ea[Sa],Ea[Sa]=null,Sa--)}function _a(e,t){Sa++,Ea[Sa]=e.current,e.current=t}var Ca={},Aa=xa(Ca),Na=xa(!1),Oa=Ca;function Ra(e,t){var r=e.type.contextTypes;if(!r)return Ca;var n=e.stateNode;if(n&&n.__reactInternalMemoizedUnmaskedChildContext===t)return n.__reactInternalMemoizedMaskedChildContext;var a,i={};for(a in r)i[a]=t[a];return n&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Pa(e){return null!==(e=e.childContextTypes)&&void 0!==e}function Ia(){ka(Na),ka(Aa)}function Da(e,t,r){if(Aa.current!==Ca)throw Error(i(168));_a(Aa,t),_a(Na,r)}function La(e,t,r){var n=e.stateNode;if(t=t.childContextTypes,"function"!==typeof n.getChildContext)return r;for(var a in n=n.getChildContext())if(!(a in t))throw Error(i(108,W(e)||"Unknown",a));return F({},r,n)}function Ma(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Ca,Oa=Aa.current,_a(Aa,e),_a(Na,Na.current),!0}function Fa(e,t,r){var n=e.stateNode;if(!n)throw Error(i(169));r?(e=La(e,t,Oa),n.__reactInternalMemoizedMergedChildContext=e,ka(Na),ka(Aa),_a(Aa,e)):ka(Na),_a(Na,r)}var ja=null,Ua=!1,Ba=!1;function za(e){null===ja?ja=[e]:ja.push(e)}function Ha(){if(!Ba&&null!==ja){Ba=!0;var e=0,t=yt;try{var r=ja;for(yt=1;e<r.length;e++){var n=r[e];do{n=n(!0)}while(null!==n)}ja=null,Ua=!1}catch(a){throw null!==ja&&(ja=ja.slice(e+1)),Ke(Ze,Ha),a}finally{yt=t,Ba=!1}}return null}var Wa=[],Va=0,$a=null,Ga=0,Ka=[],Ya=0,Xa=null,Qa=1,qa="";function Ja(e,t){Wa[Va++]=Ga,Wa[Va++]=$a,$a=e,Ga=t}function Za(e,t,r){Ka[Ya++]=Qa,Ka[Ya++]=qa,Ka[Ya++]=Xa,Xa=e;var n=Qa;e=qa;var a=32-lt(n)-1;n&=~(1<<a),r+=1;var i=32-lt(t)+a;if(30<i){var l=a-a%5;i=(n&(1<<l)-1).toString(32),n>>=l,a-=l,Qa=1<<32-lt(t)+a|r<<a|n,qa=i+e}else Qa=1<<i|r<<a|n,qa=e}function ei(e){null!==e.return&&(Ja(e,1),Za(e,1,0))}function ti(e){for(;e===$a;)$a=Wa[--Va],Wa[Va]=null,Ga=Wa[--Va],Wa[Va]=null;for(;e===Xa;)Xa=Ka[--Ya],Ka[Ya]=null,qa=Ka[--Ya],Ka[Ya]=null,Qa=Ka[--Ya],Ka[Ya]=null}var ri=null,ni=null,ai=!1,ii=null;function li(e,t){var r=Rc(5,null,null,0);r.elementType="DELETED",r.stateNode=t,r.return=e,null===(t=e.deletions)?(e.deletions=[r],e.flags|=16):t.push(r)}function oi(e,t){switch(e.tag){case 5:var r=e.type;return null!==(t=1!==t.nodeType||r.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,ri=e,ni=ca(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,ri=e,ni=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(r=null!==Xa?{id:Qa,overflow:qa}:null,e.memoizedState={dehydrated:t,treeContext:r,retryLane:1073741824},(r=Rc(18,null,null,0)).stateNode=t,r.return=e,e.child=r,ri=e,ni=null,!0);default:return!1}}function si(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function ci(e){if(ai){var t=ni;if(t){var r=t;if(!oi(e,t)){if(si(e))throw Error(i(418));t=ca(r.nextSibling);var n=ri;t&&oi(e,t)?li(n,r):(e.flags=-4097&e.flags|2,ai=!1,ri=e)}}else{if(si(e))throw Error(i(418));e.flags=-4097&e.flags|2,ai=!1,ri=e}}}function ui(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;ri=e}function fi(e){if(e!==ri)return!1;if(!ai)return ui(e),ai=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!ra(e.type,e.memoizedProps)),t&&(t=ni)){if(si(e))throw hi(),Error(i(418));for(;t;)li(e,t),t=ca(t.nextSibling)}if(ui(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(i(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var r=e.data;if("/$"===r){if(0===t){ni=ca(e.nextSibling);break e}t--}else"$"!==r&&"$!"!==r&&"$?"!==r||t++}e=e.nextSibling}ni=null}}else ni=ri?ca(e.stateNode.nextSibling):null;return!0}function hi(){for(var e=ni;e;)e=ca(e.nextSibling)}function di(){ni=ri=null,ai=!1}function pi(e){null===ii?ii=[e]:ii.push(e)}var mi=w.ReactCurrentBatchConfig;function gi(e,t,r){if(null!==(e=r.ref)&&"function"!==typeof e&&"object"!==typeof e){if(r._owner){if(r=r._owner){if(1!==r.tag)throw Error(i(309));var n=r.stateNode}if(!n)throw Error(i(147,e));var a=n,l=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===l?t.ref:(t=function(e){var t=a.refs;null===e?delete t[l]:t[l]=e},t._stringRef=l,t)}if("string"!==typeof e)throw Error(i(284));if(!r._owner)throw Error(i(290,e))}return e}function vi(e,t){throw e=Object.prototype.toString.call(t),Error(i(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function bi(e){return(0,e._init)(e._payload)}function yi(e){function t(t,r){if(e){var n=t.deletions;null===n?(t.deletions=[r],t.flags|=16):n.push(r)}}function r(r,n){if(!e)return null;for(;null!==n;)t(r,n),n=n.sibling;return null}function n(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function a(e,t){return(e=Ic(e,t)).index=0,e.sibling=null,e}function l(t,r,n){return t.index=n,e?null!==(n=t.alternate)?(n=n.index)<r?(t.flags|=2,r):n:(t.flags|=2,r):(t.flags|=1048576,r)}function o(t){return e&&null===t.alternate&&(t.flags|=2),t}function s(e,t,r,n){return null===t||6!==t.tag?((t=Fc(r,e.mode,n)).return=e,t):((t=a(t,r)).return=e,t)}function c(e,t,r,n){var i=r.type;return i===S?f(e,t,r.props.children,n,r.key):null!==t&&(t.elementType===i||"object"===typeof i&&null!==i&&i.$$typeof===P&&bi(i)===t.type)?((n=a(t,r.props)).ref=gi(e,t,r),n.return=e,n):((n=Dc(r.type,r.key,r.props,null,e.mode,n)).ref=gi(e,t,r),n.return=e,n)}function u(e,t,r,n){return null===t||4!==t.tag||t.stateNode.containerInfo!==r.containerInfo||t.stateNode.implementation!==r.implementation?((t=jc(r,e.mode,n)).return=e,t):((t=a(t,r.children||[])).return=e,t)}function f(e,t,r,n,i){return null===t||7!==t.tag?((t=Lc(r,e.mode,n,i)).return=e,t):((t=a(t,r)).return=e,t)}function h(e,t,r){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=Fc(""+t,e.mode,r)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case T:return(r=Dc(t.type,t.key,t.props,null,e.mode,r)).ref=gi(e,null,t),r.return=e,r;case E:return(t=jc(t,e.mode,r)).return=e,t;case P:return h(e,(0,t._init)(t._payload),r)}if(te(t)||L(t))return(t=Lc(t,e.mode,r,null)).return=e,t;vi(e,t)}return null}function d(e,t,r,n){var a=null!==t?t.key:null;if("string"===typeof r&&""!==r||"number"===typeof r)return null!==a?null:s(e,t,""+r,n);if("object"===typeof r&&null!==r){switch(r.$$typeof){case T:return r.key===a?c(e,t,r,n):null;case E:return r.key===a?u(e,t,r,n):null;case P:return d(e,t,(a=r._init)(r._payload),n)}if(te(r)||L(r))return null!==a?null:f(e,t,r,n,null);vi(e,r)}return null}function p(e,t,r,n,a){if("string"===typeof n&&""!==n||"number"===typeof n)return s(t,e=e.get(r)||null,""+n,a);if("object"===typeof n&&null!==n){switch(n.$$typeof){case T:return c(t,e=e.get(null===n.key?r:n.key)||null,n,a);case E:return u(t,e=e.get(null===n.key?r:n.key)||null,n,a);case P:return p(e,t,r,(0,n._init)(n._payload),a)}if(te(n)||L(n))return f(t,e=e.get(r)||null,n,a,null);vi(t,n)}return null}function m(a,i,o,s){for(var c=null,u=null,f=i,m=i=0,g=null;null!==f&&m<o.length;m++){f.index>m?(g=f,f=null):g=f.sibling;var v=d(a,f,o[m],s);if(null===v){null===f&&(f=g);break}e&&f&&null===v.alternate&&t(a,f),i=l(v,i,m),null===u?c=v:u.sibling=v,u=v,f=g}if(m===o.length)return r(a,f),ai&&Ja(a,m),c;if(null===f){for(;m<o.length;m++)null!==(f=h(a,o[m],s))&&(i=l(f,i,m),null===u?c=f:u.sibling=f,u=f);return ai&&Ja(a,m),c}for(f=n(a,f);m<o.length;m++)null!==(g=p(f,a,m,o[m],s))&&(e&&null!==g.alternate&&f.delete(null===g.key?m:g.key),i=l(g,i,m),null===u?c=g:u.sibling=g,u=g);return e&&f.forEach((function(e){return t(a,e)})),ai&&Ja(a,m),c}function g(a,o,s,c){var u=L(s);if("function"!==typeof u)throw Error(i(150));if(null==(s=u.call(s)))throw Error(i(151));for(var f=u=null,m=o,g=o=0,v=null,b=s.next();null!==m&&!b.done;g++,b=s.next()){m.index>g?(v=m,m=null):v=m.sibling;var y=d(a,m,b.value,c);if(null===y){null===m&&(m=v);break}e&&m&&null===y.alternate&&t(a,m),o=l(y,o,g),null===f?u=y:f.sibling=y,f=y,m=v}if(b.done)return r(a,m),ai&&Ja(a,g),u;if(null===m){for(;!b.done;g++,b=s.next())null!==(b=h(a,b.value,c))&&(o=l(b,o,g),null===f?u=b:f.sibling=b,f=b);return ai&&Ja(a,g),u}for(m=n(a,m);!b.done;g++,b=s.next())null!==(b=p(m,a,g,b.value,c))&&(e&&null!==b.alternate&&m.delete(null===b.key?g:b.key),o=l(b,o,g),null===f?u=b:f.sibling=b,f=b);return e&&m.forEach((function(e){return t(a,e)})),ai&&Ja(a,g),u}return function e(n,i,l,s){if("object"===typeof l&&null!==l&&l.type===S&&null===l.key&&(l=l.props.children),"object"===typeof l&&null!==l){switch(l.$$typeof){case T:e:{for(var c=l.key,u=i;null!==u;){if(u.key===c){if((c=l.type)===S){if(7===u.tag){r(n,u.sibling),(i=a(u,l.props.children)).return=n,n=i;break e}}else if(u.elementType===c||"object"===typeof c&&null!==c&&c.$$typeof===P&&bi(c)===u.type){r(n,u.sibling),(i=a(u,l.props)).ref=gi(n,u,l),i.return=n,n=i;break e}r(n,u);break}t(n,u),u=u.sibling}l.type===S?((i=Lc(l.props.children,n.mode,s,l.key)).return=n,n=i):((s=Dc(l.type,l.key,l.props,null,n.mode,s)).ref=gi(n,i,l),s.return=n,n=s)}return o(n);case E:e:{for(u=l.key;null!==i;){if(i.key===u){if(4===i.tag&&i.stateNode.containerInfo===l.containerInfo&&i.stateNode.implementation===l.implementation){r(n,i.sibling),(i=a(i,l.children||[])).return=n,n=i;break e}r(n,i);break}t(n,i),i=i.sibling}(i=jc(l,n.mode,s)).return=n,n=i}return o(n);case P:return e(n,i,(u=l._init)(l._payload),s)}if(te(l))return m(n,i,l,s);if(L(l))return g(n,i,l,s);vi(n,l)}return"string"===typeof l&&""!==l||"number"===typeof l?(l=""+l,null!==i&&6===i.tag?(r(n,i.sibling),(i=a(i,l)).return=n,n=i):(r(n,i),(i=Fc(l,n.mode,s)).return=n,n=i),o(n)):r(n,i)}}var wi=yi(!0),Ti=yi(!1),Ei=xa(null),Si=null,xi=null,ki=null;function _i(){ki=xi=Si=null}function Ci(e){var t=Ei.current;ka(Ei),e._currentValue=t}function Ai(e,t,r){for(;null!==e;){var n=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==n&&(n.childLanes|=t)):null!==n&&(n.childLanes&t)!==t&&(n.childLanes|=t),e===r)break;e=e.return}}function Ni(e,t){Si=e,ki=xi=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(yo=!0),e.firstContext=null)}function Oi(e){var t=e._currentValue;if(ki!==e)if(e={context:e,memoizedValue:t,next:null},null===xi){if(null===Si)throw Error(i(308));xi=e,Si.dependencies={lanes:0,firstContext:e}}else xi=xi.next=e;return t}var Ri=null;function Pi(e){null===Ri?Ri=[e]:Ri.push(e)}function Ii(e,t,r,n){var a=t.interleaved;return null===a?(r.next=r,Pi(t)):(r.next=a.next,a.next=r),t.interleaved=r,Di(e,n)}function Di(e,t){e.lanes|=t;var r=e.alternate;for(null!==r&&(r.lanes|=t),r=e,e=e.return;null!==e;)e.childLanes|=t,null!==(r=e.alternate)&&(r.childLanes|=t),r=e,e=e.return;return 3===r.tag?r.stateNode:null}var Li=!1;function Mi(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Fi(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function ji(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Ui(e,t,r){var n=e.updateQueue;if(null===n)return null;if(n=n.shared,0!==(2&As)){var a=n.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),n.pending=t,Di(e,r)}return null===(a=n.interleaved)?(t.next=t,Pi(n)):(t.next=a.next,a.next=t),n.interleaved=t,Di(e,r)}function Bi(e,t,r){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&r))){var n=t.lanes;r|=n&=e.pendingLanes,t.lanes=r,bt(e,r)}}function zi(e,t){var r=e.updateQueue,n=e.alternate;if(null!==n&&r===(n=n.updateQueue)){var a=null,i=null;if(null!==(r=r.firstBaseUpdate)){do{var l={eventTime:r.eventTime,lane:r.lane,tag:r.tag,payload:r.payload,callback:r.callback,next:null};null===i?a=i=l:i=i.next=l,r=r.next}while(null!==r);null===i?a=i=t:i=i.next=t}else a=i=t;return r={baseState:n.baseState,firstBaseUpdate:a,lastBaseUpdate:i,shared:n.shared,effects:n.effects},void(e.updateQueue=r)}null===(e=r.lastBaseUpdate)?r.firstBaseUpdate=t:e.next=t,r.lastBaseUpdate=t}function Hi(e,t,r,n){var a=e.updateQueue;Li=!1;var i=a.firstBaseUpdate,l=a.lastBaseUpdate,o=a.shared.pending;if(null!==o){a.shared.pending=null;var s=o,c=s.next;s.next=null,null===l?i=c:l.next=c,l=s;var u=e.alternate;null!==u&&((o=(u=u.updateQueue).lastBaseUpdate)!==l&&(null===o?u.firstBaseUpdate=c:o.next=c,u.lastBaseUpdate=s))}if(null!==i){var f=a.baseState;for(l=0,u=c=s=null,o=i;;){var h=o.lane,d=o.eventTime;if((n&h)===h){null!==u&&(u=u.next={eventTime:d,lane:0,tag:o.tag,payload:o.payload,callback:o.callback,next:null});e:{var p=e,m=o;switch(h=t,d=r,m.tag){case 1:if("function"===typeof(p=m.payload)){f=p.call(d,f,h);break e}f=p;break e;case 3:p.flags=-65537&p.flags|128;case 0:if(null===(h="function"===typeof(p=m.payload)?p.call(d,f,h):p)||void 0===h)break e;f=F({},f,h);break e;case 2:Li=!0}}null!==o.callback&&0!==o.lane&&(e.flags|=64,null===(h=a.effects)?a.effects=[o]:h.push(o))}else d={eventTime:d,lane:h,tag:o.tag,payload:o.payload,callback:o.callback,next:null},null===u?(c=u=d,s=f):u=u.next=d,l|=h;if(null===(o=o.next)){if(null===(o=a.shared.pending))break;o=(h=o).next,h.next=null,a.lastBaseUpdate=h,a.shared.pending=null}}if(null===u&&(s=f),a.baseState=s,a.firstBaseUpdate=c,a.lastBaseUpdate=u,null!==(t=a.shared.interleaved)){a=t;do{l|=a.lane,a=a.next}while(a!==t)}else null===i&&(a.shared.lanes=0);Ms|=l,e.lanes=l,e.memoizedState=f}}function Wi(e,t,r){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var n=e[t],a=n.callback;if(null!==a){if(n.callback=null,n=r,"function"!==typeof a)throw Error(i(191,a));a.call(n)}}}var Vi={},$i=xa(Vi),Gi=xa(Vi),Ki=xa(Vi);function Yi(e){if(e===Vi)throw Error(i(174));return e}function Xi(e,t){switch(_a(Ki,t),_a(Gi,e),_a($i,Vi),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:se(null,"");break;default:t=se(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}ka($i),_a($i,t)}function Qi(){ka($i),ka(Gi),ka(Ki)}function qi(e){Yi(Ki.current);var t=Yi($i.current),r=se(t,e.type);t!==r&&(_a(Gi,e),_a($i,r))}function Ji(e){Gi.current===e&&(ka($i),ka(Gi))}var Zi=xa(0);function el(e){for(var t=e;null!==t;){if(13===t.tag){var r=t.memoizedState;if(null!==r&&(null===(r=r.dehydrated)||"$?"===r.data||"$!"===r.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var tl=[];function rl(){for(var e=0;e<tl.length;e++)tl[e]._workInProgressVersionPrimary=null;tl.length=0}var nl=w.ReactCurrentDispatcher,al=w.ReactCurrentBatchConfig,il=0,ll=null,ol=null,sl=null,cl=!1,ul=!1,fl=0,hl=0;function dl(){throw Error(i(321))}function pl(e,t){if(null===t)return!1;for(var r=0;r<t.length&&r<e.length;r++)if(!on(e[r],t[r]))return!1;return!0}function ml(e,t,r,n,a,l){if(il=l,ll=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,nl.current=null===e||null===e.memoizedState?Jl:Zl,e=r(n,a),ul){l=0;do{if(ul=!1,fl=0,25<=l)throw Error(i(301));l+=1,sl=ol=null,t.updateQueue=null,nl.current=eo,e=r(n,a)}while(ul)}if(nl.current=ql,t=null!==ol&&null!==ol.next,il=0,sl=ol=ll=null,cl=!1,t)throw Error(i(300));return e}function gl(){var e=0!==fl;return fl=0,e}function vl(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===sl?ll.memoizedState=sl=e:sl=sl.next=e,sl}function bl(){if(null===ol){var e=ll.alternate;e=null!==e?e.memoizedState:null}else e=ol.next;var t=null===sl?ll.memoizedState:sl.next;if(null!==t)sl=t,ol=e;else{if(null===e)throw Error(i(310));e={memoizedState:(ol=e).memoizedState,baseState:ol.baseState,baseQueue:ol.baseQueue,queue:ol.queue,next:null},null===sl?ll.memoizedState=sl=e:sl=sl.next=e}return sl}function yl(e,t){return"function"===typeof t?t(e):t}function wl(e){var t=bl(),r=t.queue;if(null===r)throw Error(i(311));r.lastRenderedReducer=e;var n=ol,a=n.baseQueue,l=r.pending;if(null!==l){if(null!==a){var o=a.next;a.next=l.next,l.next=o}n.baseQueue=a=l,r.pending=null}if(null!==a){l=a.next,n=n.baseState;var s=o=null,c=null,u=l;do{var f=u.lane;if((il&f)===f)null!==c&&(c=c.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),n=u.hasEagerState?u.eagerState:e(n,u.action);else{var h={lane:f,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};null===c?(s=c=h,o=n):c=c.next=h,ll.lanes|=f,Ms|=f}u=u.next}while(null!==u&&u!==l);null===c?o=n:c.next=s,on(n,t.memoizedState)||(yo=!0),t.memoizedState=n,t.baseState=o,t.baseQueue=c,r.lastRenderedState=n}if(null!==(e=r.interleaved)){a=e;do{l=a.lane,ll.lanes|=l,Ms|=l,a=a.next}while(a!==e)}else null===a&&(r.lanes=0);return[t.memoizedState,r.dispatch]}function Tl(e){var t=bl(),r=t.queue;if(null===r)throw Error(i(311));r.lastRenderedReducer=e;var n=r.dispatch,a=r.pending,l=t.memoizedState;if(null!==a){r.pending=null;var o=a=a.next;do{l=e(l,o.action),o=o.next}while(o!==a);on(l,t.memoizedState)||(yo=!0),t.memoizedState=l,null===t.baseQueue&&(t.baseState=l),r.lastRenderedState=l}return[l,n]}function El(){}function Sl(e,t){var r=ll,n=bl(),a=t(),l=!on(n.memoizedState,a);if(l&&(n.memoizedState=a,yo=!0),n=n.queue,Ll(_l.bind(null,r,n,e),[e]),n.getSnapshot!==t||l||null!==sl&&1&sl.memoizedState.tag){if(r.flags|=2048,Ol(9,kl.bind(null,r,n,a,t),void 0,null),null===Ns)throw Error(i(349));0!==(30&il)||xl(r,t,a)}return a}function xl(e,t,r){e.flags|=16384,e={getSnapshot:t,value:r},null===(t=ll.updateQueue)?(t={lastEffect:null,stores:null},ll.updateQueue=t,t.stores=[e]):null===(r=t.stores)?t.stores=[e]:r.push(e)}function kl(e,t,r,n){t.value=r,t.getSnapshot=n,Cl(t)&&Al(e)}function _l(e,t,r){return r((function(){Cl(t)&&Al(e)}))}function Cl(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!on(e,r)}catch(n){return!0}}function Al(e){var t=Di(e,1);null!==t&&rc(t,e,1,-1)}function Nl(e){var t=vl();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:yl,lastRenderedState:e},t.queue=e,e=e.dispatch=Kl.bind(null,ll,e),[t.memoizedState,e]}function Ol(e,t,r,n){return e={tag:e,create:t,destroy:r,deps:n,next:null},null===(t=ll.updateQueue)?(t={lastEffect:null,stores:null},ll.updateQueue=t,t.lastEffect=e.next=e):null===(r=t.lastEffect)?t.lastEffect=e.next=e:(n=r.next,r.next=e,e.next=n,t.lastEffect=e),e}function Rl(){return bl().memoizedState}function Pl(e,t,r,n){var a=vl();ll.flags|=e,a.memoizedState=Ol(1|t,r,void 0,void 0===n?null:n)}function Il(e,t,r,n){var a=bl();n=void 0===n?null:n;var i=void 0;if(null!==ol){var l=ol.memoizedState;if(i=l.destroy,null!==n&&pl(n,l.deps))return void(a.memoizedState=Ol(t,r,i,n))}ll.flags|=e,a.memoizedState=Ol(1|t,r,i,n)}function Dl(e,t){return Pl(8390656,8,e,t)}function Ll(e,t){return Il(2048,8,e,t)}function Ml(e,t){return Il(4,2,e,t)}function Fl(e,t){return Il(4,4,e,t)}function jl(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Ul(e,t,r){return r=null!==r&&void 0!==r?r.concat([e]):null,Il(4,4,jl.bind(null,t,e),r)}function Bl(){}function zl(e,t){var r=bl();t=void 0===t?null:t;var n=r.memoizedState;return null!==n&&null!==t&&pl(t,n[1])?n[0]:(r.memoizedState=[e,t],e)}function Hl(e,t){var r=bl();t=void 0===t?null:t;var n=r.memoizedState;return null!==n&&null!==t&&pl(t,n[1])?n[0]:(e=e(),r.memoizedState=[e,t],e)}function Wl(e,t,r){return 0===(21&il)?(e.baseState&&(e.baseState=!1,yo=!0),e.memoizedState=r):(on(r,t)||(r=mt(),ll.lanes|=r,Ms|=r,e.baseState=!0),t)}function Vl(e,t){var r=yt;yt=0!==r&&4>r?r:4,e(!0);var n=al.transition;al.transition={};try{e(!1),t()}finally{yt=r,al.transition=n}}function $l(){return bl().memoizedState}function Gl(e,t,r){var n=tc(e);if(r={lane:n,action:r,hasEagerState:!1,eagerState:null,next:null},Yl(e))Xl(t,r);else if(null!==(r=Ii(e,t,r,n))){rc(r,e,n,ec()),Ql(r,t,n)}}function Kl(e,t,r){var n=tc(e),a={lane:n,action:r,hasEagerState:!1,eagerState:null,next:null};if(Yl(e))Xl(t,a);else{var i=e.alternate;if(0===e.lanes&&(null===i||0===i.lanes)&&null!==(i=t.lastRenderedReducer))try{var l=t.lastRenderedState,o=i(l,r);if(a.hasEagerState=!0,a.eagerState=o,on(o,l)){var s=t.interleaved;return null===s?(a.next=a,Pi(t)):(a.next=s.next,s.next=a),void(t.interleaved=a)}}catch(c){}null!==(r=Ii(e,t,a,n))&&(rc(r,e,n,a=ec()),Ql(r,t,n))}}function Yl(e){var t=e.alternate;return e===ll||null!==t&&t===ll}function Xl(e,t){ul=cl=!0;var r=e.pending;null===r?t.next=t:(t.next=r.next,r.next=t),e.pending=t}function Ql(e,t,r){if(0!==(4194240&r)){var n=t.lanes;r|=n&=e.pendingLanes,t.lanes=r,bt(e,r)}}var ql={readContext:Oi,useCallback:dl,useContext:dl,useEffect:dl,useImperativeHandle:dl,useInsertionEffect:dl,useLayoutEffect:dl,useMemo:dl,useReducer:dl,useRef:dl,useState:dl,useDebugValue:dl,useDeferredValue:dl,useTransition:dl,useMutableSource:dl,useSyncExternalStore:dl,useId:dl,unstable_isNewReconciler:!1},Jl={readContext:Oi,useCallback:function(e,t){return vl().memoizedState=[e,void 0===t?null:t],e},useContext:Oi,useEffect:Dl,useImperativeHandle:function(e,t,r){return r=null!==r&&void 0!==r?r.concat([e]):null,Pl(4194308,4,jl.bind(null,t,e),r)},useLayoutEffect:function(e,t){return Pl(4194308,4,e,t)},useInsertionEffect:function(e,t){return Pl(4,2,e,t)},useMemo:function(e,t){var r=vl();return t=void 0===t?null:t,e=e(),r.memoizedState=[e,t],e},useReducer:function(e,t,r){var n=vl();return t=void 0!==r?r(t):t,n.memoizedState=n.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},n.queue=e,e=e.dispatch=Gl.bind(null,ll,e),[n.memoizedState,e]},useRef:function(e){return e={current:e},vl().memoizedState=e},useState:Nl,useDebugValue:Bl,useDeferredValue:function(e){return vl().memoizedState=e},useTransition:function(){var e=Nl(!1),t=e[0];return e=Vl.bind(null,e[1]),vl().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,r){var n=ll,a=vl();if(ai){if(void 0===r)throw Error(i(407));r=r()}else{if(r=t(),null===Ns)throw Error(i(349));0!==(30&il)||xl(n,t,r)}a.memoizedState=r;var l={value:r,getSnapshot:t};return a.queue=l,Dl(_l.bind(null,n,l,e),[e]),n.flags|=2048,Ol(9,kl.bind(null,n,l,r,t),void 0,null),r},useId:function(){var e=vl(),t=Ns.identifierPrefix;if(ai){var r=qa;t=":"+t+"R"+(r=(Qa&~(1<<32-lt(Qa)-1)).toString(32)+r),0<(r=fl++)&&(t+="H"+r.toString(32)),t+=":"}else t=":"+t+"r"+(r=hl++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Zl={readContext:Oi,useCallback:zl,useContext:Oi,useEffect:Ll,useImperativeHandle:Ul,useInsertionEffect:Ml,useLayoutEffect:Fl,useMemo:Hl,useReducer:wl,useRef:Rl,useState:function(){return wl(yl)},useDebugValue:Bl,useDeferredValue:function(e){return Wl(bl(),ol.memoizedState,e)},useTransition:function(){return[wl(yl)[0],bl().memoizedState]},useMutableSource:El,useSyncExternalStore:Sl,useId:$l,unstable_isNewReconciler:!1},eo={readContext:Oi,useCallback:zl,useContext:Oi,useEffect:Ll,useImperativeHandle:Ul,useInsertionEffect:Ml,useLayoutEffect:Fl,useMemo:Hl,useReducer:Tl,useRef:Rl,useState:function(){return Tl(yl)},useDebugValue:Bl,useDeferredValue:function(e){var t=bl();return null===ol?t.memoizedState=e:Wl(t,ol.memoizedState,e)},useTransition:function(){return[Tl(yl)[0],bl().memoizedState]},useMutableSource:El,useSyncExternalStore:Sl,useId:$l,unstable_isNewReconciler:!1};function to(e,t){if(e&&e.defaultProps){for(var r in t=F({},t),e=e.defaultProps)void 0===t[r]&&(t[r]=e[r]);return t}return t}function ro(e,t,r,n){r=null===(r=r(n,t=e.memoizedState))||void 0===r?t:F({},t,r),e.memoizedState=r,0===e.lanes&&(e.updateQueue.baseState=r)}var no={isMounted:function(e){return!!(e=e._reactInternals)&&He(e)===e},enqueueSetState:function(e,t,r){e=e._reactInternals;var n=ec(),a=tc(e),i=ji(n,a);i.payload=t,void 0!==r&&null!==r&&(i.callback=r),null!==(t=Ui(e,i,a))&&(rc(t,e,a,n),Bi(t,e,a))},enqueueReplaceState:function(e,t,r){e=e._reactInternals;var n=ec(),a=tc(e),i=ji(n,a);i.tag=1,i.payload=t,void 0!==r&&null!==r&&(i.callback=r),null!==(t=Ui(e,i,a))&&(rc(t,e,a,n),Bi(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var r=ec(),n=tc(e),a=ji(r,n);a.tag=2,void 0!==t&&null!==t&&(a.callback=t),null!==(t=Ui(e,a,n))&&(rc(t,e,n,r),Bi(t,e,n))}};function ao(e,t,r,n,a,i,l){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(n,i,l):!t.prototype||!t.prototype.isPureReactComponent||(!sn(r,n)||!sn(a,i))}function io(e,t,r){var n=!1,a=Ca,i=t.contextType;return"object"===typeof i&&null!==i?i=Oi(i):(a=Pa(t)?Oa:Aa.current,i=(n=null!==(n=t.contextTypes)&&void 0!==n)?Ra(e,a):Ca),t=new t(r,i),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=no,e.stateNode=t,t._reactInternals=e,n&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=i),t}function lo(e,t,r,n){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(r,n),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(r,n),t.state!==e&&no.enqueueReplaceState(t,t.state,null)}function oo(e,t,r,n){var a=e.stateNode;a.props=r,a.state=e.memoizedState,a.refs={},Mi(e);var i=t.contextType;"object"===typeof i&&null!==i?a.context=Oi(i):(i=Pa(t)?Oa:Aa.current,a.context=Ra(e,i)),a.state=e.memoizedState,"function"===typeof(i=t.getDerivedStateFromProps)&&(ro(e,t,i,r),a.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof a.getSnapshotBeforeUpdate||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||(t=a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),t!==a.state&&no.enqueueReplaceState(a,a.state,null),Hi(e,r,a,n),a.state=e.memoizedState),"function"===typeof a.componentDidMount&&(e.flags|=4194308)}function so(e,t){try{var r="",n=t;do{r+=z(n),n=n.return}while(n);var a=r}catch(i){a="\nError generating stack: "+i.message+"\n"+i.stack}return{value:e,source:t,stack:a,digest:null}}function co(e,t,r){return{value:e,source:null,stack:null!=r?r:null,digest:null!=t?t:null}}function uo(e,t){try{console.error(t.value)}catch(r){setTimeout((function(){throw r}))}}var fo="function"===typeof WeakMap?WeakMap:Map;function ho(e,t,r){(r=ji(-1,r)).tag=3,r.payload={element:null};var n=t.value;return r.callback=function(){Vs||(Vs=!0,$s=n),uo(0,t)},r}function po(e,t,r){(r=ji(-1,r)).tag=3;var n=e.type.getDerivedStateFromError;if("function"===typeof n){var a=t.value;r.payload=function(){return n(a)},r.callback=function(){uo(0,t)}}var i=e.stateNode;return null!==i&&"function"===typeof i.componentDidCatch&&(r.callback=function(){uo(0,t),"function"!==typeof n&&(null===Gs?Gs=new Set([this]):Gs.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),r}function mo(e,t,r){var n=e.pingCache;if(null===n){n=e.pingCache=new fo;var a=new Set;n.set(t,a)}else void 0===(a=n.get(t))&&(a=new Set,n.set(t,a));a.has(r)||(a.add(r),e=kc.bind(null,e,t,r),t.then(e,e))}function go(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function vo(e,t,r,n,a){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,r.flags|=131072,r.flags&=-52805,1===r.tag&&(null===r.alternate?r.tag=17:((t=ji(-1,1)).tag=2,Ui(r,t,1))),r.lanes|=1),e):(e.flags|=65536,e.lanes=a,e)}var bo=w.ReactCurrentOwner,yo=!1;function wo(e,t,r,n){t.child=null===e?Ti(t,null,r,n):wi(t,e.child,r,n)}function To(e,t,r,n,a){r=r.render;var i=t.ref;return Ni(t,a),n=ml(e,t,r,n,i,a),r=gl(),null===e||yo?(ai&&r&&ei(t),t.flags|=1,wo(e,t,n,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Vo(e,t,a))}function Eo(e,t,r,n,a){if(null===e){var i=r.type;return"function"!==typeof i||Pc(i)||void 0!==i.defaultProps||null!==r.compare||void 0!==r.defaultProps?((e=Dc(r.type,null,n,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=i,So(e,t,i,n,a))}if(i=e.child,0===(e.lanes&a)){var l=i.memoizedProps;if((r=null!==(r=r.compare)?r:sn)(l,n)&&e.ref===t.ref)return Vo(e,t,a)}return t.flags|=1,(e=Ic(i,n)).ref=t.ref,e.return=t,t.child=e}function So(e,t,r,n,a){if(null!==e){var i=e.memoizedProps;if(sn(i,n)&&e.ref===t.ref){if(yo=!1,t.pendingProps=n=i,0===(e.lanes&a))return t.lanes=e.lanes,Vo(e,t,a);0!==(131072&e.flags)&&(yo=!0)}}return _o(e,t,r,n,a)}function xo(e,t,r){var n=t.pendingProps,a=n.children,i=null!==e?e.memoizedState:null;if("hidden"===n.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},_a(Is,Ps),Ps|=r;else{if(0===(1073741824&r))return e=null!==i?i.baseLanes|r:r,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,_a(Is,Ps),Ps|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},n=null!==i?i.baseLanes:r,_a(Is,Ps),Ps|=n}else null!==i?(n=i.baseLanes|r,t.memoizedState=null):n=r,_a(Is,Ps),Ps|=n;return wo(e,t,a,r),t.child}function ko(e,t){var r=t.ref;(null===e&&null!==r||null!==e&&e.ref!==r)&&(t.flags|=512,t.flags|=2097152)}function _o(e,t,r,n,a){var i=Pa(r)?Oa:Aa.current;return i=Ra(t,i),Ni(t,a),r=ml(e,t,r,n,i,a),n=gl(),null===e||yo?(ai&&n&&ei(t),t.flags|=1,wo(e,t,r,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Vo(e,t,a))}function Co(e,t,r,n,a){if(Pa(r)){var i=!0;Ma(t)}else i=!1;if(Ni(t,a),null===t.stateNode)Wo(e,t),io(t,r,n),oo(t,r,n,a),n=!0;else if(null===e){var l=t.stateNode,o=t.memoizedProps;l.props=o;var s=l.context,c=r.contextType;"object"===typeof c&&null!==c?c=Oi(c):c=Ra(t,c=Pa(r)?Oa:Aa.current);var u=r.getDerivedStateFromProps,f="function"===typeof u||"function"===typeof l.getSnapshotBeforeUpdate;f||"function"!==typeof l.UNSAFE_componentWillReceiveProps&&"function"!==typeof l.componentWillReceiveProps||(o!==n||s!==c)&&lo(t,l,n,c),Li=!1;var h=t.memoizedState;l.state=h,Hi(t,n,l,a),s=t.memoizedState,o!==n||h!==s||Na.current||Li?("function"===typeof u&&(ro(t,r,u,n),s=t.memoizedState),(o=Li||ao(t,r,o,n,h,s,c))?(f||"function"!==typeof l.UNSAFE_componentWillMount&&"function"!==typeof l.componentWillMount||("function"===typeof l.componentWillMount&&l.componentWillMount(),"function"===typeof l.UNSAFE_componentWillMount&&l.UNSAFE_componentWillMount()),"function"===typeof l.componentDidMount&&(t.flags|=4194308)):("function"===typeof l.componentDidMount&&(t.flags|=4194308),t.memoizedProps=n,t.memoizedState=s),l.props=n,l.state=s,l.context=c,n=o):("function"===typeof l.componentDidMount&&(t.flags|=4194308),n=!1)}else{l=t.stateNode,Fi(e,t),o=t.memoizedProps,c=t.type===t.elementType?o:to(t.type,o),l.props=c,f=t.pendingProps,h=l.context,"object"===typeof(s=r.contextType)&&null!==s?s=Oi(s):s=Ra(t,s=Pa(r)?Oa:Aa.current);var d=r.getDerivedStateFromProps;(u="function"===typeof d||"function"===typeof l.getSnapshotBeforeUpdate)||"function"!==typeof l.UNSAFE_componentWillReceiveProps&&"function"!==typeof l.componentWillReceiveProps||(o!==f||h!==s)&&lo(t,l,n,s),Li=!1,h=t.memoizedState,l.state=h,Hi(t,n,l,a);var p=t.memoizedState;o!==f||h!==p||Na.current||Li?("function"===typeof d&&(ro(t,r,d,n),p=t.memoizedState),(c=Li||ao(t,r,c,n,h,p,s)||!1)?(u||"function"!==typeof l.UNSAFE_componentWillUpdate&&"function"!==typeof l.componentWillUpdate||("function"===typeof l.componentWillUpdate&&l.componentWillUpdate(n,p,s),"function"===typeof l.UNSAFE_componentWillUpdate&&l.UNSAFE_componentWillUpdate(n,p,s)),"function"===typeof l.componentDidUpdate&&(t.flags|=4),"function"===typeof l.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof l.componentDidUpdate||o===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),"function"!==typeof l.getSnapshotBeforeUpdate||o===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),t.memoizedProps=n,t.memoizedState=p),l.props=n,l.state=p,l.context=s,n=c):("function"!==typeof l.componentDidUpdate||o===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),"function"!==typeof l.getSnapshotBeforeUpdate||o===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),n=!1)}return Ao(e,t,r,n,i,a)}function Ao(e,t,r,n,a,i){ko(e,t);var l=0!==(128&t.flags);if(!n&&!l)return a&&Fa(t,r,!1),Vo(e,t,i);n=t.stateNode,bo.current=t;var o=l&&"function"!==typeof r.getDerivedStateFromError?null:n.render();return t.flags|=1,null!==e&&l?(t.child=wi(t,e.child,null,i),t.child=wi(t,null,o,i)):wo(e,t,o,i),t.memoizedState=n.state,a&&Fa(t,r,!0),t.child}function No(e){var t=e.stateNode;t.pendingContext?Da(0,t.pendingContext,t.pendingContext!==t.context):t.context&&Da(0,t.context,!1),Xi(e,t.containerInfo)}function Oo(e,t,r,n,a){return di(),pi(a),t.flags|=256,wo(e,t,r,n),t.child}var Ro,Po,Io,Do,Lo={dehydrated:null,treeContext:null,retryLane:0};function Mo(e){return{baseLanes:e,cachePool:null,transitions:null}}function Fo(e,t,r){var n,a=t.pendingProps,l=Zi.current,o=!1,s=0!==(128&t.flags);if((n=s)||(n=(null===e||null!==e.memoizedState)&&0!==(2&l)),n?(o=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(l|=1),_a(Zi,1&l),null===e)return ci(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(s=a.children,e=a.fallback,o?(a=t.mode,o=t.child,s={mode:"hidden",children:s},0===(1&a)&&null!==o?(o.childLanes=0,o.pendingProps=s):o=Mc(s,a,0,null),e=Lc(e,a,r,null),o.return=t,e.return=t,o.sibling=e,t.child=o,t.child.memoizedState=Mo(r),t.memoizedState=Lo,e):jo(t,s));if(null!==(l=e.memoizedState)&&null!==(n=l.dehydrated))return function(e,t,r,n,a,l,o){if(r)return 256&t.flags?(t.flags&=-257,Uo(e,t,o,n=co(Error(i(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(l=n.fallback,a=t.mode,n=Mc({mode:"visible",children:n.children},a,0,null),(l=Lc(l,a,o,null)).flags|=2,n.return=t,l.return=t,n.sibling=l,t.child=n,0!==(1&t.mode)&&wi(t,e.child,null,o),t.child.memoizedState=Mo(o),t.memoizedState=Lo,l);if(0===(1&t.mode))return Uo(e,t,o,null);if("$!"===a.data){if(n=a.nextSibling&&a.nextSibling.dataset)var s=n.dgst;return n=s,Uo(e,t,o,n=co(l=Error(i(419)),n,void 0))}if(s=0!==(o&e.childLanes),yo||s){if(null!==(n=Ns)){switch(o&-o){case 4:a=2;break;case 16:a=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:a=32;break;case 536870912:a=268435456;break;default:a=0}0!==(a=0!==(a&(n.suspendedLanes|o))?0:a)&&a!==l.retryLane&&(l.retryLane=a,Di(e,a),rc(n,e,a,-1))}return mc(),Uo(e,t,o,n=co(Error(i(421))))}return"$?"===a.data?(t.flags|=128,t.child=e.child,t=Cc.bind(null,e),a._reactRetry=t,null):(e=l.treeContext,ni=ca(a.nextSibling),ri=t,ai=!0,ii=null,null!==e&&(Ka[Ya++]=Qa,Ka[Ya++]=qa,Ka[Ya++]=Xa,Qa=e.id,qa=e.overflow,Xa=t),t=jo(t,n.children),t.flags|=4096,t)}(e,t,s,a,n,l,r);if(o){o=a.fallback,s=t.mode,n=(l=e.child).sibling;var c={mode:"hidden",children:a.children};return 0===(1&s)&&t.child!==l?((a=t.child).childLanes=0,a.pendingProps=c,t.deletions=null):(a=Ic(l,c)).subtreeFlags=14680064&l.subtreeFlags,null!==n?o=Ic(n,o):(o=Lc(o,s,r,null)).flags|=2,o.return=t,a.return=t,a.sibling=o,t.child=a,a=o,o=t.child,s=null===(s=e.child.memoizedState)?Mo(r):{baseLanes:s.baseLanes|r,cachePool:null,transitions:s.transitions},o.memoizedState=s,o.childLanes=e.childLanes&~r,t.memoizedState=Lo,a}return e=(o=e.child).sibling,a=Ic(o,{mode:"visible",children:a.children}),0===(1&t.mode)&&(a.lanes=r),a.return=t,a.sibling=null,null!==e&&(null===(r=t.deletions)?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=a,t.memoizedState=null,a}function jo(e,t){return(t=Mc({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Uo(e,t,r,n){return null!==n&&pi(n),wi(t,e.child,null,r),(e=jo(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Bo(e,t,r){e.lanes|=t;var n=e.alternate;null!==n&&(n.lanes|=t),Ai(e.return,t,r)}function zo(e,t,r,n,a){var i=e.memoizedState;null===i?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:n,tail:r,tailMode:a}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=n,i.tail=r,i.tailMode=a)}function Ho(e,t,r){var n=t.pendingProps,a=n.revealOrder,i=n.tail;if(wo(e,t,n.children,r),0!==(2&(n=Zi.current)))n=1&n|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Bo(e,r,t);else if(19===e.tag)Bo(e,r,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}n&=1}if(_a(Zi,n),0===(1&t.mode))t.memoizedState=null;else switch(a){case"forwards":for(r=t.child,a=null;null!==r;)null!==(e=r.alternate)&&null===el(e)&&(a=r),r=r.sibling;null===(r=a)?(a=t.child,t.child=null):(a=r.sibling,r.sibling=null),zo(t,!1,a,r,i);break;case"backwards":for(r=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===el(e)){t.child=a;break}e=a.sibling,a.sibling=r,r=a,a=e}zo(t,!0,r,null,i);break;case"together":zo(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Wo(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Vo(e,t,r){if(null!==e&&(t.dependencies=e.dependencies),Ms|=t.lanes,0===(r&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(i(153));if(null!==t.child){for(r=Ic(e=t.child,e.pendingProps),t.child=r,r.return=t;null!==e.sibling;)e=e.sibling,(r=r.sibling=Ic(e,e.pendingProps)).return=t;r.sibling=null}return t.child}function $o(e,t){if(!ai)switch(e.tailMode){case"hidden":t=e.tail;for(var r=null;null!==t;)null!==t.alternate&&(r=t),t=t.sibling;null===r?e.tail=null:r.sibling=null;break;case"collapsed":r=e.tail;for(var n=null;null!==r;)null!==r.alternate&&(n=r),r=r.sibling;null===n?t||null===e.tail?e.tail=null:e.tail.sibling=null:n.sibling=null}}function Go(e){var t=null!==e.alternate&&e.alternate.child===e.child,r=0,n=0;if(t)for(var a=e.child;null!==a;)r|=a.lanes|a.childLanes,n|=14680064&a.subtreeFlags,n|=14680064&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)r|=a.lanes|a.childLanes,n|=a.subtreeFlags,n|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=n,e.childLanes=r,t}function Ko(e,t,r){var n=t.pendingProps;switch(ti(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Go(t),null;case 1:case 17:return Pa(t.type)&&Ia(),Go(t),null;case 3:return n=t.stateNode,Qi(),ka(Na),ka(Aa),rl(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==e&&null!==e.child||(fi(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==ii&&(lc(ii),ii=null))),Po(e,t),Go(t),null;case 5:Ji(t);var a=Yi(Ki.current);if(r=t.type,null!==e&&null!=t.stateNode)Io(e,t,r,n,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!n){if(null===t.stateNode)throw Error(i(166));return Go(t),null}if(e=Yi($i.current),fi(t)){n=t.stateNode,r=t.type;var l=t.memoizedProps;switch(n[ha]=t,n[da]=l,e=0!==(1&t.mode),r){case"dialog":Un("cancel",n),Un("close",n);break;case"iframe":case"object":case"embed":Un("load",n);break;case"video":case"audio":for(a=0;a<Ln.length;a++)Un(Ln[a],n);break;case"source":Un("error",n);break;case"img":case"image":case"link":Un("error",n),Un("load",n);break;case"details":Un("toggle",n);break;case"input":Q(n,l),Un("invalid",n);break;case"select":n._wrapperState={wasMultiple:!!l.multiple},Un("invalid",n);break;case"textarea":ae(n,l),Un("invalid",n)}for(var s in be(r,l),a=null,l)if(l.hasOwnProperty(s)){var c=l[s];"children"===s?"string"===typeof c?n.textContent!==c&&(!0!==l.suppressHydrationWarning&&Jn(n.textContent,c,e),a=["children",c]):"number"===typeof c&&n.textContent!==""+c&&(!0!==l.suppressHydrationWarning&&Jn(n.textContent,c,e),a=["children",""+c]):o.hasOwnProperty(s)&&null!=c&&"onScroll"===s&&Un("scroll",n)}switch(r){case"input":G(n),Z(n,l,!0);break;case"textarea":G(n),le(n);break;case"select":case"option":break;default:"function"===typeof l.onClick&&(n.onclick=Zn)}n=a,t.updateQueue=n,null!==n&&(t.flags|=4)}else{s=9===a.nodeType?a:a.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=oe(r)),"http://www.w3.org/1999/xhtml"===e?"script"===r?((e=s.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof n.is?e=s.createElement(r,{is:n.is}):(e=s.createElement(r),"select"===r&&(s=e,n.multiple?s.multiple=!0:n.size&&(s.size=n.size))):e=s.createElementNS(e,r),e[ha]=t,e[da]=n,Ro(e,t,!1,!1),t.stateNode=e;e:{switch(s=ye(r,n),r){case"dialog":Un("cancel",e),Un("close",e),a=n;break;case"iframe":case"object":case"embed":Un("load",e),a=n;break;case"video":case"audio":for(a=0;a<Ln.length;a++)Un(Ln[a],e);a=n;break;case"source":Un("error",e),a=n;break;case"img":case"image":case"link":Un("error",e),Un("load",e),a=n;break;case"details":Un("toggle",e),a=n;break;case"input":Q(e,n),a=X(e,n),Un("invalid",e);break;case"option":default:a=n;break;case"select":e._wrapperState={wasMultiple:!!n.multiple},a=F({},n,{value:void 0}),Un("invalid",e);break;case"textarea":ae(e,n),a=ne(e,n),Un("invalid",e)}for(l in be(r,a),c=a)if(c.hasOwnProperty(l)){var u=c[l];"style"===l?ge(e,u):"dangerouslySetInnerHTML"===l?null!=(u=u?u.__html:void 0)&&fe(e,u):"children"===l?"string"===typeof u?("textarea"!==r||""!==u)&&he(e,u):"number"===typeof u&&he(e,""+u):"suppressContentEditableWarning"!==l&&"suppressHydrationWarning"!==l&&"autoFocus"!==l&&(o.hasOwnProperty(l)?null!=u&&"onScroll"===l&&Un("scroll",e):null!=u&&y(e,l,u,s))}switch(r){case"input":G(e),Z(e,n,!1);break;case"textarea":G(e),le(e);break;case"option":null!=n.value&&e.setAttribute("value",""+V(n.value));break;case"select":e.multiple=!!n.multiple,null!=(l=n.value)?re(e,!!n.multiple,l,!1):null!=n.defaultValue&&re(e,!!n.multiple,n.defaultValue,!0);break;default:"function"===typeof a.onClick&&(e.onclick=Zn)}switch(r){case"button":case"input":case"select":case"textarea":n=!!n.autoFocus;break e;case"img":n=!0;break e;default:n=!1}}n&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return Go(t),null;case 6:if(e&&null!=t.stateNode)Do(e,t,e.memoizedProps,n);else{if("string"!==typeof n&&null===t.stateNode)throw Error(i(166));if(r=Yi(Ki.current),Yi($i.current),fi(t)){if(n=t.stateNode,r=t.memoizedProps,n[ha]=t,(l=n.nodeValue!==r)&&null!==(e=ri))switch(e.tag){case 3:Jn(n.nodeValue,r,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Jn(n.nodeValue,r,0!==(1&e.mode))}l&&(t.flags|=4)}else(n=(9===r.nodeType?r:r.ownerDocument).createTextNode(n))[ha]=t,t.stateNode=n}return Go(t),null;case 13:if(ka(Zi),n=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(ai&&null!==ni&&0!==(1&t.mode)&&0===(128&t.flags))hi(),di(),t.flags|=98560,l=!1;else if(l=fi(t),null!==n&&null!==n.dehydrated){if(null===e){if(!l)throw Error(i(318));if(!(l=null!==(l=t.memoizedState)?l.dehydrated:null))throw Error(i(317));l[ha]=t}else di(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;Go(t),l=!1}else null!==ii&&(lc(ii),ii=null),l=!0;if(!l)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=r,t):((n=null!==n)!==(null!==e&&null!==e.memoizedState)&&n&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&Zi.current)?0===Ds&&(Ds=3):mc())),null!==t.updateQueue&&(t.flags|=4),Go(t),null);case 4:return Qi(),Po(e,t),null===e&&Hn(t.stateNode.containerInfo),Go(t),null;case 10:return Ci(t.type._context),Go(t),null;case 19:if(ka(Zi),null===(l=t.memoizedState))return Go(t),null;if(n=0!==(128&t.flags),null===(s=l.rendering))if(n)$o(l,!1);else{if(0!==Ds||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(s=el(e))){for(t.flags|=128,$o(l,!1),null!==(n=s.updateQueue)&&(t.updateQueue=n,t.flags|=4),t.subtreeFlags=0,n=r,r=t.child;null!==r;)e=n,(l=r).flags&=14680066,null===(s=l.alternate)?(l.childLanes=0,l.lanes=e,l.child=null,l.subtreeFlags=0,l.memoizedProps=null,l.memoizedState=null,l.updateQueue=null,l.dependencies=null,l.stateNode=null):(l.childLanes=s.childLanes,l.lanes=s.lanes,l.child=s.child,l.subtreeFlags=0,l.deletions=null,l.memoizedProps=s.memoizedProps,l.memoizedState=s.memoizedState,l.updateQueue=s.updateQueue,l.type=s.type,e=s.dependencies,l.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),r=r.sibling;return _a(Zi,1&Zi.current|2),t.child}e=e.sibling}null!==l.tail&&qe()>Hs&&(t.flags|=128,n=!0,$o(l,!1),t.lanes=4194304)}else{if(!n)if(null!==(e=el(s))){if(t.flags|=128,n=!0,null!==(r=e.updateQueue)&&(t.updateQueue=r,t.flags|=4),$o(l,!0),null===l.tail&&"hidden"===l.tailMode&&!s.alternate&&!ai)return Go(t),null}else 2*qe()-l.renderingStartTime>Hs&&1073741824!==r&&(t.flags|=128,n=!0,$o(l,!1),t.lanes=4194304);l.isBackwards?(s.sibling=t.child,t.child=s):(null!==(r=l.last)?r.sibling=s:t.child=s,l.last=s)}return null!==l.tail?(t=l.tail,l.rendering=t,l.tail=t.sibling,l.renderingStartTime=qe(),t.sibling=null,r=Zi.current,_a(Zi,n?1&r|2:1&r),t):(Go(t),null);case 22:case 23:return fc(),n=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==n&&(t.flags|=8192),n&&0!==(1&t.mode)?0!==(1073741824&Ps)&&(Go(t),6&t.subtreeFlags&&(t.flags|=8192)):Go(t),null;case 24:case 25:return null}throw Error(i(156,t.tag))}function Yo(e,t){switch(ti(t),t.tag){case 1:return Pa(t.type)&&Ia(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Qi(),ka(Na),ka(Aa),rl(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Ji(t),null;case 13:if(ka(Zi),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(i(340));di()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return ka(Zi),null;case 4:return Qi(),null;case 10:return Ci(t.type._context),null;case 22:case 23:return fc(),null;default:return null}}Ro=function(e,t){for(var r=t.child;null!==r;){if(5===r.tag||6===r.tag)e.appendChild(r.stateNode);else if(4!==r.tag&&null!==r.child){r.child.return=r,r=r.child;continue}if(r===t)break;for(;null===r.sibling;){if(null===r.return||r.return===t)return;r=r.return}r.sibling.return=r.return,r=r.sibling}},Po=function(){},Io=function(e,t,r,n){var a=e.memoizedProps;if(a!==n){e=t.stateNode,Yi($i.current);var i,l=null;switch(r){case"input":a=X(e,a),n=X(e,n),l=[];break;case"select":a=F({},a,{value:void 0}),n=F({},n,{value:void 0}),l=[];break;case"textarea":a=ne(e,a),n=ne(e,n),l=[];break;default:"function"!==typeof a.onClick&&"function"===typeof n.onClick&&(e.onclick=Zn)}for(u in be(r,n),r=null,a)if(!n.hasOwnProperty(u)&&a.hasOwnProperty(u)&&null!=a[u])if("style"===u){var s=a[u];for(i in s)s.hasOwnProperty(i)&&(r||(r={}),r[i]="")}else"dangerouslySetInnerHTML"!==u&&"children"!==u&&"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&"autoFocus"!==u&&(o.hasOwnProperty(u)?l||(l=[]):(l=l||[]).push(u,null));for(u in n){var c=n[u];if(s=null!=a?a[u]:void 0,n.hasOwnProperty(u)&&c!==s&&(null!=c||null!=s))if("style"===u)if(s){for(i in s)!s.hasOwnProperty(i)||c&&c.hasOwnProperty(i)||(r||(r={}),r[i]="");for(i in c)c.hasOwnProperty(i)&&s[i]!==c[i]&&(r||(r={}),r[i]=c[i])}else r||(l||(l=[]),l.push(u,r)),r=c;else"dangerouslySetInnerHTML"===u?(c=c?c.__html:void 0,s=s?s.__html:void 0,null!=c&&s!==c&&(l=l||[]).push(u,c)):"children"===u?"string"!==typeof c&&"number"!==typeof c||(l=l||[]).push(u,""+c):"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&(o.hasOwnProperty(u)?(null!=c&&"onScroll"===u&&Un("scroll",e),l||s===c||(l=[])):(l=l||[]).push(u,c))}r&&(l=l||[]).push("style",r);var u=l;(t.updateQueue=u)&&(t.flags|=4)}},Do=function(e,t,r,n){r!==n&&(t.flags|=4)};var Xo=!1,Qo=!1,qo="function"===typeof WeakSet?WeakSet:Set,Jo=null;function Zo(e,t){var r=e.ref;if(null!==r)if("function"===typeof r)try{r(null)}catch(n){xc(e,t,n)}else r.current=null}function es(e,t,r){try{r()}catch(n){xc(e,t,n)}}var ts=!1;function rs(e,t,r){var n=t.updateQueue;if(null!==(n=null!==n?n.lastEffect:null)){var a=n=n.next;do{if((a.tag&e)===e){var i=a.destroy;a.destroy=void 0,void 0!==i&&es(t,r,i)}a=a.next}while(a!==n)}}function ns(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var r=t=t.next;do{if((r.tag&e)===e){var n=r.create;r.destroy=n()}r=r.next}while(r!==t)}}function as(e){var t=e.ref;if(null!==t){var r=e.stateNode;e.tag,e=r,"function"===typeof t?t(e):t.current=e}}function is(e){var t=e.alternate;null!==t&&(e.alternate=null,is(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[ha],delete t[da],delete t[ma],delete t[ga],delete t[va])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function ls(e){return 5===e.tag||3===e.tag||4===e.tag}function os(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||ls(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function ss(e,t,r){var n=e.tag;if(5===n||6===n)e=e.stateNode,t?8===r.nodeType?r.parentNode.insertBefore(e,t):r.insertBefore(e,t):(8===r.nodeType?(t=r.parentNode).insertBefore(e,r):(t=r).appendChild(e),null!==(r=r._reactRootContainer)&&void 0!==r||null!==t.onclick||(t.onclick=Zn));else if(4!==n&&null!==(e=e.child))for(ss(e,t,r),e=e.sibling;null!==e;)ss(e,t,r),e=e.sibling}function cs(e,t,r){var n=e.tag;if(5===n||6===n)e=e.stateNode,t?r.insertBefore(e,t):r.appendChild(e);else if(4!==n&&null!==(e=e.child))for(cs(e,t,r),e=e.sibling;null!==e;)cs(e,t,r),e=e.sibling}var us=null,fs=!1;function hs(e,t,r){for(r=r.child;null!==r;)ds(e,t,r),r=r.sibling}function ds(e,t,r){if(it&&"function"===typeof it.onCommitFiberUnmount)try{it.onCommitFiberUnmount(at,r)}catch(o){}switch(r.tag){case 5:Qo||Zo(r,t);case 6:var n=us,a=fs;us=null,hs(e,t,r),fs=a,null!==(us=n)&&(fs?(e=us,r=r.stateNode,8===e.nodeType?e.parentNode.removeChild(r):e.removeChild(r)):us.removeChild(r.stateNode));break;case 18:null!==us&&(fs?(e=us,r=r.stateNode,8===e.nodeType?sa(e.parentNode,r):1===e.nodeType&&sa(e,r),Ht(e)):sa(us,r.stateNode));break;case 4:n=us,a=fs,us=r.stateNode.containerInfo,fs=!0,hs(e,t,r),us=n,fs=a;break;case 0:case 11:case 14:case 15:if(!Qo&&(null!==(n=r.updateQueue)&&null!==(n=n.lastEffect))){a=n=n.next;do{var i=a,l=i.destroy;i=i.tag,void 0!==l&&(0!==(2&i)||0!==(4&i))&&es(r,t,l),a=a.next}while(a!==n)}hs(e,t,r);break;case 1:if(!Qo&&(Zo(r,t),"function"===typeof(n=r.stateNode).componentWillUnmount))try{n.props=r.memoizedProps,n.state=r.memoizedState,n.componentWillUnmount()}catch(o){xc(r,t,o)}hs(e,t,r);break;case 21:hs(e,t,r);break;case 22:1&r.mode?(Qo=(n=Qo)||null!==r.memoizedState,hs(e,t,r),Qo=n):hs(e,t,r);break;default:hs(e,t,r)}}function ps(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var r=e.stateNode;null===r&&(r=e.stateNode=new qo),t.forEach((function(t){var n=Ac.bind(null,e,t);r.has(t)||(r.add(t),t.then(n,n))}))}}function ms(e,t){var r=t.deletions;if(null!==r)for(var n=0;n<r.length;n++){var a=r[n];try{var l=e,o=t,s=o;e:for(;null!==s;){switch(s.tag){case 5:us=s.stateNode,fs=!1;break e;case 3:case 4:us=s.stateNode.containerInfo,fs=!0;break e}s=s.return}if(null===us)throw Error(i(160));ds(l,o,a),us=null,fs=!1;var c=a.alternate;null!==c&&(c.return=null),a.return=null}catch(u){xc(a,t,u)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)gs(t,e),t=t.sibling}function gs(e,t){var r=e.alternate,n=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ms(t,e),vs(e),4&n){try{rs(3,e,e.return),ns(3,e)}catch(g){xc(e,e.return,g)}try{rs(5,e,e.return)}catch(g){xc(e,e.return,g)}}break;case 1:ms(t,e),vs(e),512&n&&null!==r&&Zo(r,r.return);break;case 5:if(ms(t,e),vs(e),512&n&&null!==r&&Zo(r,r.return),32&e.flags){var a=e.stateNode;try{he(a,"")}catch(g){xc(e,e.return,g)}}if(4&n&&null!=(a=e.stateNode)){var l=e.memoizedProps,o=null!==r?r.memoizedProps:l,s=e.type,c=e.updateQueue;if(e.updateQueue=null,null!==c)try{"input"===s&&"radio"===l.type&&null!=l.name&&q(a,l),ye(s,o);var u=ye(s,l);for(o=0;o<c.length;o+=2){var f=c[o],h=c[o+1];"style"===f?ge(a,h):"dangerouslySetInnerHTML"===f?fe(a,h):"children"===f?he(a,h):y(a,f,h,u)}switch(s){case"input":J(a,l);break;case"textarea":ie(a,l);break;case"select":var d=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!l.multiple;var p=l.value;null!=p?re(a,!!l.multiple,p,!1):d!==!!l.multiple&&(null!=l.defaultValue?re(a,!!l.multiple,l.defaultValue,!0):re(a,!!l.multiple,l.multiple?[]:"",!1))}a[da]=l}catch(g){xc(e,e.return,g)}}break;case 6:if(ms(t,e),vs(e),4&n){if(null===e.stateNode)throw Error(i(162));a=e.stateNode,l=e.memoizedProps;try{a.nodeValue=l}catch(g){xc(e,e.return,g)}}break;case 3:if(ms(t,e),vs(e),4&n&&null!==r&&r.memoizedState.isDehydrated)try{Ht(t.containerInfo)}catch(g){xc(e,e.return,g)}break;case 4:default:ms(t,e),vs(e);break;case 13:ms(t,e),vs(e),8192&(a=e.child).flags&&(l=null!==a.memoizedState,a.stateNode.isHidden=l,!l||null!==a.alternate&&null!==a.alternate.memoizedState||(zs=qe())),4&n&&ps(e);break;case 22:if(f=null!==r&&null!==r.memoizedState,1&e.mode?(Qo=(u=Qo)||f,ms(t,e),Qo=u):ms(t,e),vs(e),8192&n){if(u=null!==e.memoizedState,(e.stateNode.isHidden=u)&&!f&&0!==(1&e.mode))for(Jo=e,f=e.child;null!==f;){for(h=Jo=f;null!==Jo;){switch(p=(d=Jo).child,d.tag){case 0:case 11:case 14:case 15:rs(4,d,d.return);break;case 1:Zo(d,d.return);var m=d.stateNode;if("function"===typeof m.componentWillUnmount){n=d,r=d.return;try{t=n,m.props=t.memoizedProps,m.state=t.memoizedState,m.componentWillUnmount()}catch(g){xc(n,r,g)}}break;case 5:Zo(d,d.return);break;case 22:if(null!==d.memoizedState){Ts(h);continue}}null!==p?(p.return=d,Jo=p):Ts(h)}f=f.sibling}e:for(f=null,h=e;;){if(5===h.tag){if(null===f){f=h;try{a=h.stateNode,u?"function"===typeof(l=a.style).setProperty?l.setProperty("display","none","important"):l.display="none":(s=h.stateNode,o=void 0!==(c=h.memoizedProps.style)&&null!==c&&c.hasOwnProperty("display")?c.display:null,s.style.display=me("display",o))}catch(g){xc(e,e.return,g)}}}else if(6===h.tag){if(null===f)try{h.stateNode.nodeValue=u?"":h.memoizedProps}catch(g){xc(e,e.return,g)}}else if((22!==h.tag&&23!==h.tag||null===h.memoizedState||h===e)&&null!==h.child){h.child.return=h,h=h.child;continue}if(h===e)break e;for(;null===h.sibling;){if(null===h.return||h.return===e)break e;f===h&&(f=null),h=h.return}f===h&&(f=null),h.sibling.return=h.return,h=h.sibling}}break;case 19:ms(t,e),vs(e),4&n&&ps(e);case 21:}}function vs(e){var t=e.flags;if(2&t){try{e:{for(var r=e.return;null!==r;){if(ls(r)){var n=r;break e}r=r.return}throw Error(i(160))}switch(n.tag){case 5:var a=n.stateNode;32&n.flags&&(he(a,""),n.flags&=-33),cs(e,os(e),a);break;case 3:case 4:var l=n.stateNode.containerInfo;ss(e,os(e),l);break;default:throw Error(i(161))}}catch(o){xc(e,e.return,o)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function bs(e,t,r){Jo=e,ys(e,t,r)}function ys(e,t,r){for(var n=0!==(1&e.mode);null!==Jo;){var a=Jo,i=a.child;if(22===a.tag&&n){var l=null!==a.memoizedState||Xo;if(!l){var o=a.alternate,s=null!==o&&null!==o.memoizedState||Qo;o=Xo;var c=Qo;if(Xo=l,(Qo=s)&&!c)for(Jo=a;null!==Jo;)s=(l=Jo).child,22===l.tag&&null!==l.memoizedState?Es(a):null!==s?(s.return=l,Jo=s):Es(a);for(;null!==i;)Jo=i,ys(i,t,r),i=i.sibling;Jo=a,Xo=o,Qo=c}ws(e)}else 0!==(8772&a.subtreeFlags)&&null!==i?(i.return=a,Jo=i):ws(e)}}function ws(e){for(;null!==Jo;){var t=Jo;if(0!==(8772&t.flags)){var r=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:Qo||ns(5,t);break;case 1:var n=t.stateNode;if(4&t.flags&&!Qo)if(null===r)n.componentDidMount();else{var a=t.elementType===t.type?r.memoizedProps:to(t.type,r.memoizedProps);n.componentDidUpdate(a,r.memoizedState,n.__reactInternalSnapshotBeforeUpdate)}var l=t.updateQueue;null!==l&&Wi(t,l,n);break;case 3:var o=t.updateQueue;if(null!==o){if(r=null,null!==t.child)switch(t.child.tag){case 5:case 1:r=t.child.stateNode}Wi(t,o,r)}break;case 5:var s=t.stateNode;if(null===r&&4&t.flags){r=s;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&r.focus();break;case"img":c.src&&(r.src=c.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var u=t.alternate;if(null!==u){var f=u.memoizedState;if(null!==f){var h=f.dehydrated;null!==h&&Ht(h)}}}break;default:throw Error(i(163))}Qo||512&t.flags&&as(t)}catch(d){xc(t,t.return,d)}}if(t===e){Jo=null;break}if(null!==(r=t.sibling)){r.return=t.return,Jo=r;break}Jo=t.return}}function Ts(e){for(;null!==Jo;){var t=Jo;if(t===e){Jo=null;break}var r=t.sibling;if(null!==r){r.return=t.return,Jo=r;break}Jo=t.return}}function Es(e){for(;null!==Jo;){var t=Jo;try{switch(t.tag){case 0:case 11:case 15:var r=t.return;try{ns(4,t)}catch(s){xc(t,r,s)}break;case 1:var n=t.stateNode;if("function"===typeof n.componentDidMount){var a=t.return;try{n.componentDidMount()}catch(s){xc(t,a,s)}}var i=t.return;try{as(t)}catch(s){xc(t,i,s)}break;case 5:var l=t.return;try{as(t)}catch(s){xc(t,l,s)}}}catch(s){xc(t,t.return,s)}if(t===e){Jo=null;break}var o=t.sibling;if(null!==o){o.return=t.return,Jo=o;break}Jo=t.return}}var Ss,xs=Math.ceil,ks=w.ReactCurrentDispatcher,_s=w.ReactCurrentOwner,Cs=w.ReactCurrentBatchConfig,As=0,Ns=null,Os=null,Rs=0,Ps=0,Is=xa(0),Ds=0,Ls=null,Ms=0,Fs=0,js=0,Us=null,Bs=null,zs=0,Hs=1/0,Ws=null,Vs=!1,$s=null,Gs=null,Ks=!1,Ys=null,Xs=0,Qs=0,qs=null,Js=-1,Zs=0;function ec(){return 0!==(6&As)?qe():-1!==Js?Js:Js=qe()}function tc(e){return 0===(1&e.mode)?1:0!==(2&As)&&0!==Rs?Rs&-Rs:null!==mi.transition?(0===Zs&&(Zs=mt()),Zs):0!==(e=yt)?e:e=void 0===(e=window.event)?16:Qt(e.type)}function rc(e,t,r,n){if(50<Qs)throw Qs=0,qs=null,Error(i(185));vt(e,r,n),0!==(2&As)&&e===Ns||(e===Ns&&(0===(2&As)&&(Fs|=r),4===Ds&&oc(e,Rs)),nc(e,n),1===r&&0===As&&0===(1&t.mode)&&(Hs=qe()+500,Ua&&Ha()))}function nc(e,t){var r=e.callbackNode;!function(e,t){for(var r=e.suspendedLanes,n=e.pingedLanes,a=e.expirationTimes,i=e.pendingLanes;0<i;){var l=31-lt(i),o=1<<l,s=a[l];-1===s?0!==(o&r)&&0===(o&n)||(a[l]=dt(o,t)):s<=t&&(e.expiredLanes|=o),i&=~o}}(e,t);var n=ht(e,e===Ns?Rs:0);if(0===n)null!==r&&Ye(r),e.callbackNode=null,e.callbackPriority=0;else if(t=n&-n,e.callbackPriority!==t){if(null!=r&&Ye(r),1===t)0===e.tag?function(e){Ua=!0,za(e)}(sc.bind(null,e)):za(sc.bind(null,e)),la((function(){0===(6&As)&&Ha()})),r=null;else{switch(wt(n)){case 1:r=Ze;break;case 4:r=et;break;case 16:default:r=tt;break;case 536870912:r=nt}r=Nc(r,ac.bind(null,e))}e.callbackPriority=t,e.callbackNode=r}}function ac(e,t){if(Js=-1,Zs=0,0!==(6&As))throw Error(i(327));var r=e.callbackNode;if(Ec()&&e.callbackNode!==r)return null;var n=ht(e,e===Ns?Rs:0);if(0===n)return null;if(0!==(30&n)||0!==(n&e.expiredLanes)||t)t=gc(e,n);else{t=n;var a=As;As|=2;var l=pc();for(Ns===e&&Rs===t||(Ws=null,Hs=qe()+500,hc(e,t));;)try{bc();break}catch(s){dc(e,s)}_i(),ks.current=l,As=a,null!==Os?t=0:(Ns=null,Rs=0,t=Ds)}if(0!==t){if(2===t&&(0!==(a=pt(e))&&(n=a,t=ic(e,a))),1===t)throw r=Ls,hc(e,0),oc(e,n),nc(e,qe()),r;if(6===t)oc(e,n);else{if(a=e.current.alternate,0===(30&n)&&!function(e){for(var t=e;;){if(16384&t.flags){var r=t.updateQueue;if(null!==r&&null!==(r=r.stores))for(var n=0;n<r.length;n++){var a=r[n],i=a.getSnapshot;a=a.value;try{if(!on(i(),a))return!1}catch(o){return!1}}}if(r=t.child,16384&t.subtreeFlags&&null!==r)r.return=t,t=r;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(a)&&(2===(t=gc(e,n))&&(0!==(l=pt(e))&&(n=l,t=ic(e,l))),1===t))throw r=Ls,hc(e,0),oc(e,n),nc(e,qe()),r;switch(e.finishedWork=a,e.finishedLanes=n,t){case 0:case 1:throw Error(i(345));case 2:case 5:Tc(e,Bs,Ws);break;case 3:if(oc(e,n),(130023424&n)===n&&10<(t=zs+500-qe())){if(0!==ht(e,0))break;if(((a=e.suspendedLanes)&n)!==n){ec(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=na(Tc.bind(null,e,Bs,Ws),t);break}Tc(e,Bs,Ws);break;case 4:if(oc(e,n),(4194240&n)===n)break;for(t=e.eventTimes,a=-1;0<n;){var o=31-lt(n);l=1<<o,(o=t[o])>a&&(a=o),n&=~l}if(n=a,10<(n=(120>(n=qe()-n)?120:480>n?480:1080>n?1080:1920>n?1920:3e3>n?3e3:4320>n?4320:1960*xs(n/1960))-n)){e.timeoutHandle=na(Tc.bind(null,e,Bs,Ws),n);break}Tc(e,Bs,Ws);break;default:throw Error(i(329))}}}return nc(e,qe()),e.callbackNode===r?ac.bind(null,e):null}function ic(e,t){var r=Us;return e.current.memoizedState.isDehydrated&&(hc(e,t).flags|=256),2!==(e=gc(e,t))&&(t=Bs,Bs=r,null!==t&&lc(t)),e}function lc(e){null===Bs?Bs=e:Bs.push.apply(Bs,e)}function oc(e,t){for(t&=~js,t&=~Fs,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var r=31-lt(t),n=1<<r;e[r]=-1,t&=~n}}function sc(e){if(0!==(6&As))throw Error(i(327));Ec();var t=ht(e,0);if(0===(1&t))return nc(e,qe()),null;var r=gc(e,t);if(0!==e.tag&&2===r){var n=pt(e);0!==n&&(t=n,r=ic(e,n))}if(1===r)throw r=Ls,hc(e,0),oc(e,t),nc(e,qe()),r;if(6===r)throw Error(i(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Tc(e,Bs,Ws),nc(e,qe()),null}function cc(e,t){var r=As;As|=1;try{return e(t)}finally{0===(As=r)&&(Hs=qe()+500,Ua&&Ha())}}function uc(e){null!==Ys&&0===Ys.tag&&0===(6&As)&&Ec();var t=As;As|=1;var r=Cs.transition,n=yt;try{if(Cs.transition=null,yt=1,e)return e()}finally{yt=n,Cs.transition=r,0===(6&(As=t))&&Ha()}}function fc(){Ps=Is.current,ka(Is)}function hc(e,t){e.finishedWork=null,e.finishedLanes=0;var r=e.timeoutHandle;if(-1!==r&&(e.timeoutHandle=-1,aa(r)),null!==Os)for(r=Os.return;null!==r;){var n=r;switch(ti(n),n.tag){case 1:null!==(n=n.type.childContextTypes)&&void 0!==n&&Ia();break;case 3:Qi(),ka(Na),ka(Aa),rl();break;case 5:Ji(n);break;case 4:Qi();break;case 13:case 19:ka(Zi);break;case 10:Ci(n.type._context);break;case 22:case 23:fc()}r=r.return}if(Ns=e,Os=e=Ic(e.current,null),Rs=Ps=t,Ds=0,Ls=null,js=Fs=Ms=0,Bs=Us=null,null!==Ri){for(t=0;t<Ri.length;t++)if(null!==(n=(r=Ri[t]).interleaved)){r.interleaved=null;var a=n.next,i=r.pending;if(null!==i){var l=i.next;i.next=a,n.next=l}r.pending=n}Ri=null}return e}function dc(e,t){for(;;){var r=Os;try{if(_i(),nl.current=ql,cl){for(var n=ll.memoizedState;null!==n;){var a=n.queue;null!==a&&(a.pending=null),n=n.next}cl=!1}if(il=0,sl=ol=ll=null,ul=!1,fl=0,_s.current=null,null===r||null===r.return){Ds=1,Ls=t,Os=null;break}e:{var l=e,o=r.return,s=r,c=t;if(t=Rs,s.flags|=32768,null!==c&&"object"===typeof c&&"function"===typeof c.then){var u=c,f=s,h=f.tag;if(0===(1&f.mode)&&(0===h||11===h||15===h)){var d=f.alternate;d?(f.updateQueue=d.updateQueue,f.memoizedState=d.memoizedState,f.lanes=d.lanes):(f.updateQueue=null,f.memoizedState=null)}var p=go(o);if(null!==p){p.flags&=-257,vo(p,o,s,0,t),1&p.mode&&mo(l,u,t),c=u;var m=(t=p).updateQueue;if(null===m){var g=new Set;g.add(c),t.updateQueue=g}else m.add(c);break e}if(0===(1&t)){mo(l,u,t),mc();break e}c=Error(i(426))}else if(ai&&1&s.mode){var v=go(o);if(null!==v){0===(65536&v.flags)&&(v.flags|=256),vo(v,o,s,0,t),pi(so(c,s));break e}}l=c=so(c,s),4!==Ds&&(Ds=2),null===Us?Us=[l]:Us.push(l),l=o;do{switch(l.tag){case 3:l.flags|=65536,t&=-t,l.lanes|=t,zi(l,ho(0,c,t));break e;case 1:s=c;var b=l.type,y=l.stateNode;if(0===(128&l.flags)&&("function"===typeof b.getDerivedStateFromError||null!==y&&"function"===typeof y.componentDidCatch&&(null===Gs||!Gs.has(y)))){l.flags|=65536,t&=-t,l.lanes|=t,zi(l,po(l,s,t));break e}}l=l.return}while(null!==l)}wc(r)}catch(w){t=w,Os===r&&null!==r&&(Os=r=r.return);continue}break}}function pc(){var e=ks.current;return ks.current=ql,null===e?ql:e}function mc(){0!==Ds&&3!==Ds&&2!==Ds||(Ds=4),null===Ns||0===(268435455&Ms)&&0===(268435455&Fs)||oc(Ns,Rs)}function gc(e,t){var r=As;As|=2;var n=pc();for(Ns===e&&Rs===t||(Ws=null,hc(e,t));;)try{vc();break}catch(a){dc(e,a)}if(_i(),As=r,ks.current=n,null!==Os)throw Error(i(261));return Ns=null,Rs=0,Ds}function vc(){for(;null!==Os;)yc(Os)}function bc(){for(;null!==Os&&!Xe();)yc(Os)}function yc(e){var t=Ss(e.alternate,e,Ps);e.memoizedProps=e.pendingProps,null===t?wc(e):Os=t,_s.current=null}function wc(e){var t=e;do{var r=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(r=Ko(r,t,Ps)))return void(Os=r)}else{if(null!==(r=Yo(r,t)))return r.flags&=32767,void(Os=r);if(null===e)return Ds=6,void(Os=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Os=t);Os=t=e}while(null!==t);0===Ds&&(Ds=5)}function Tc(e,t,r){var n=yt,a=Cs.transition;try{Cs.transition=null,yt=1,function(e,t,r,n){do{Ec()}while(null!==Ys);if(0!==(6&As))throw Error(i(327));r=e.finishedWork;var a=e.finishedLanes;if(null===r)return null;if(e.finishedWork=null,e.finishedLanes=0,r===e.current)throw Error(i(177));e.callbackNode=null,e.callbackPriority=0;var l=r.lanes|r.childLanes;if(function(e,t){var r=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var n=e.eventTimes;for(e=e.expirationTimes;0<r;){var a=31-lt(r),i=1<<a;t[a]=0,n[a]=-1,e[a]=-1,r&=~i}}(e,l),e===Ns&&(Os=Ns=null,Rs=0),0===(2064&r.subtreeFlags)&&0===(2064&r.flags)||Ks||(Ks=!0,Nc(tt,(function(){return Ec(),null}))),l=0!==(15990&r.flags),0!==(15990&r.subtreeFlags)||l){l=Cs.transition,Cs.transition=null;var o=yt;yt=1;var s=As;As|=4,_s.current=null,function(e,t){if(ea=Vt,dn(e=hn())){if("selectionStart"in e)var r={start:e.selectionStart,end:e.selectionEnd};else e:{var n=(r=(r=e.ownerDocument)&&r.defaultView||window).getSelection&&r.getSelection();if(n&&0!==n.rangeCount){r=n.anchorNode;var a=n.anchorOffset,l=n.focusNode;n=n.focusOffset;try{r.nodeType,l.nodeType}catch(T){r=null;break e}var o=0,s=-1,c=-1,u=0,f=0,h=e,d=null;t:for(;;){for(var p;h!==r||0!==a&&3!==h.nodeType||(s=o+a),h!==l||0!==n&&3!==h.nodeType||(c=o+n),3===h.nodeType&&(o+=h.nodeValue.length),null!==(p=h.firstChild);)d=h,h=p;for(;;){if(h===e)break t;if(d===r&&++u===a&&(s=o),d===l&&++f===n&&(c=o),null!==(p=h.nextSibling))break;d=(h=d).parentNode}h=p}r=-1===s||-1===c?null:{start:s,end:c}}else r=null}r=r||{start:0,end:0}}else r=null;for(ta={focusedElem:e,selectionRange:r},Vt=!1,Jo=t;null!==Jo;)if(e=(t=Jo).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,Jo=e;else for(;null!==Jo;){t=Jo;try{var m=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==m){var g=m.memoizedProps,v=m.memoizedState,b=t.stateNode,y=b.getSnapshotBeforeUpdate(t.elementType===t.type?g:to(t.type,g),v);b.__reactInternalSnapshotBeforeUpdate=y}break;case 3:var w=t.stateNode.containerInfo;1===w.nodeType?w.textContent="":9===w.nodeType&&w.documentElement&&w.removeChild(w.documentElement);break;default:throw Error(i(163))}}catch(T){xc(t,t.return,T)}if(null!==(e=t.sibling)){e.return=t.return,Jo=e;break}Jo=t.return}m=ts,ts=!1}(e,r),gs(r,e),pn(ta),Vt=!!ea,ta=ea=null,e.current=r,bs(r,e,a),Qe(),As=s,yt=o,Cs.transition=l}else e.current=r;if(Ks&&(Ks=!1,Ys=e,Xs=a),l=e.pendingLanes,0===l&&(Gs=null),function(e){if(it&&"function"===typeof it.onCommitFiberRoot)try{it.onCommitFiberRoot(at,e,void 0,128===(128&e.current.flags))}catch(t){}}(r.stateNode),nc(e,qe()),null!==t)for(n=e.onRecoverableError,r=0;r<t.length;r++)a=t[r],n(a.value,{componentStack:a.stack,digest:a.digest});if(Vs)throw Vs=!1,e=$s,$s=null,e;0!==(1&Xs)&&0!==e.tag&&Ec(),l=e.pendingLanes,0!==(1&l)?e===qs?Qs++:(Qs=0,qs=e):Qs=0,Ha()}(e,t,r,n)}finally{Cs.transition=a,yt=n}return null}function Ec(){if(null!==Ys){var e=wt(Xs),t=Cs.transition,r=yt;try{if(Cs.transition=null,yt=16>e?16:e,null===Ys)var n=!1;else{if(e=Ys,Ys=null,Xs=0,0!==(6&As))throw Error(i(331));var a=As;for(As|=4,Jo=e.current;null!==Jo;){var l=Jo,o=l.child;if(0!==(16&Jo.flags)){var s=l.deletions;if(null!==s){for(var c=0;c<s.length;c++){var u=s[c];for(Jo=u;null!==Jo;){var f=Jo;switch(f.tag){case 0:case 11:case 15:rs(8,f,l)}var h=f.child;if(null!==h)h.return=f,Jo=h;else for(;null!==Jo;){var d=(f=Jo).sibling,p=f.return;if(is(f),f===u){Jo=null;break}if(null!==d){d.return=p,Jo=d;break}Jo=p}}}var m=l.alternate;if(null!==m){var g=m.child;if(null!==g){m.child=null;do{var v=g.sibling;g.sibling=null,g=v}while(null!==g)}}Jo=l}}if(0!==(2064&l.subtreeFlags)&&null!==o)o.return=l,Jo=o;else e:for(;null!==Jo;){if(0!==(2048&(l=Jo).flags))switch(l.tag){case 0:case 11:case 15:rs(9,l,l.return)}var b=l.sibling;if(null!==b){b.return=l.return,Jo=b;break e}Jo=l.return}}var y=e.current;for(Jo=y;null!==Jo;){var w=(o=Jo).child;if(0!==(2064&o.subtreeFlags)&&null!==w)w.return=o,Jo=w;else e:for(o=y;null!==Jo;){if(0!==(2048&(s=Jo).flags))try{switch(s.tag){case 0:case 11:case 15:ns(9,s)}}catch(E){xc(s,s.return,E)}if(s===o){Jo=null;break e}var T=s.sibling;if(null!==T){T.return=s.return,Jo=T;break e}Jo=s.return}}if(As=a,Ha(),it&&"function"===typeof it.onPostCommitFiberRoot)try{it.onPostCommitFiberRoot(at,e)}catch(E){}n=!0}return n}finally{yt=r,Cs.transition=t}}return!1}function Sc(e,t,r){e=Ui(e,t=ho(0,t=so(r,t),1),1),t=ec(),null!==e&&(vt(e,1,t),nc(e,t))}function xc(e,t,r){if(3===e.tag)Sc(e,e,r);else for(;null!==t;){if(3===t.tag){Sc(t,e,r);break}if(1===t.tag){var n=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof n.componentDidCatch&&(null===Gs||!Gs.has(n))){t=Ui(t,e=po(t,e=so(r,e),1),1),e=ec(),null!==t&&(vt(t,1,e),nc(t,e));break}}t=t.return}}function kc(e,t,r){var n=e.pingCache;null!==n&&n.delete(t),t=ec(),e.pingedLanes|=e.suspendedLanes&r,Ns===e&&(Rs&r)===r&&(4===Ds||3===Ds&&(130023424&Rs)===Rs&&500>qe()-zs?hc(e,0):js|=r),nc(e,t)}function _c(e,t){0===t&&(0===(1&e.mode)?t=1:(t=ut,0===(130023424&(ut<<=1))&&(ut=4194304)));var r=ec();null!==(e=Di(e,t))&&(vt(e,t,r),nc(e,r))}function Cc(e){var t=e.memoizedState,r=0;null!==t&&(r=t.retryLane),_c(e,r)}function Ac(e,t){var r=0;switch(e.tag){case 13:var n=e.stateNode,a=e.memoizedState;null!==a&&(r=a.retryLane);break;case 19:n=e.stateNode;break;default:throw Error(i(314))}null!==n&&n.delete(t),_c(e,r)}function Nc(e,t){return Ke(e,t)}function Oc(e,t,r,n){this.tag=e,this.key=r,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=n,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Rc(e,t,r,n){return new Oc(e,t,r,n)}function Pc(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Ic(e,t){var r=e.alternate;return null===r?((r=Rc(e.tag,t,e.key,e.mode)).elementType=e.elementType,r.type=e.type,r.stateNode=e.stateNode,r.alternate=e,e.alternate=r):(r.pendingProps=t,r.type=e.type,r.flags=0,r.subtreeFlags=0,r.deletions=null),r.flags=14680064&e.flags,r.childLanes=e.childLanes,r.lanes=e.lanes,r.child=e.child,r.memoizedProps=e.memoizedProps,r.memoizedState=e.memoizedState,r.updateQueue=e.updateQueue,t=e.dependencies,r.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},r.sibling=e.sibling,r.index=e.index,r.ref=e.ref,r}function Dc(e,t,r,n,a,l){var o=2;if(n=e,"function"===typeof e)Pc(e)&&(o=1);else if("string"===typeof e)o=5;else e:switch(e){case S:return Lc(r.children,a,l,t);case x:o=8,a|=8;break;case k:return(e=Rc(12,r,t,2|a)).elementType=k,e.lanes=l,e;case N:return(e=Rc(13,r,t,a)).elementType=N,e.lanes=l,e;case O:return(e=Rc(19,r,t,a)).elementType=O,e.lanes=l,e;case I:return Mc(r,a,l,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case _:o=10;break e;case C:o=9;break e;case A:o=11;break e;case R:o=14;break e;case P:o=16,n=null;break e}throw Error(i(130,null==e?e:typeof e,""))}return(t=Rc(o,r,t,a)).elementType=e,t.type=n,t.lanes=l,t}function Lc(e,t,r,n){return(e=Rc(7,e,n,t)).lanes=r,e}function Mc(e,t,r,n){return(e=Rc(22,e,n,t)).elementType=I,e.lanes=r,e.stateNode={isHidden:!1},e}function Fc(e,t,r){return(e=Rc(6,e,null,t)).lanes=r,e}function jc(e,t,r){return(t=Rc(4,null!==e.children?e.children:[],e.key,t)).lanes=r,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Uc(e,t,r,n,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=gt(0),this.expirationTimes=gt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gt(0),this.identifierPrefix=n,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function Bc(e,t,r,n,a,i,l,o,s){return e=new Uc(e,t,r,o,s),1===t?(t=1,!0===i&&(t|=8)):t=0,i=Rc(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:n,isDehydrated:r,cache:null,transitions:null,pendingSuspenseBoundaries:null},Mi(i),e}function zc(e){if(!e)return Ca;e:{if(He(e=e._reactInternals)!==e||1!==e.tag)throw Error(i(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Pa(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(i(171))}if(1===e.tag){var r=e.type;if(Pa(r))return La(e,r,t)}return t}function Hc(e,t,r,n,a,i,l,o,s){return(e=Bc(r,n,!0,e,0,i,0,o,s)).context=zc(null),r=e.current,(i=ji(n=ec(),a=tc(r))).callback=void 0!==t&&null!==t?t:null,Ui(r,i,a),e.current.lanes=a,vt(e,a,n),nc(e,n),e}function Wc(e,t,r,n){var a=t.current,i=ec(),l=tc(a);return r=zc(r),null===t.context?t.context=r:t.pendingContext=r,(t=ji(i,l)).payload={element:e},null!==(n=void 0===n?null:n)&&(t.callback=n),null!==(e=Ui(a,t,l))&&(rc(e,a,l,i),Bi(e,a,l)),l}function Vc(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function $c(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var r=e.retryLane;e.retryLane=0!==r&&r<t?r:t}}function Gc(e,t){$c(e,t),(e=e.alternate)&&$c(e,t)}Ss=function(e,t,r){if(null!==e)if(e.memoizedProps!==t.pendingProps||Na.current)yo=!0;else{if(0===(e.lanes&r)&&0===(128&t.flags))return yo=!1,function(e,t,r){switch(t.tag){case 3:No(t),di();break;case 5:qi(t);break;case 1:Pa(t.type)&&Ma(t);break;case 4:Xi(t,t.stateNode.containerInfo);break;case 10:var n=t.type._context,a=t.memoizedProps.value;_a(Ei,n._currentValue),n._currentValue=a;break;case 13:if(null!==(n=t.memoizedState))return null!==n.dehydrated?(_a(Zi,1&Zi.current),t.flags|=128,null):0!==(r&t.child.childLanes)?Fo(e,t,r):(_a(Zi,1&Zi.current),null!==(e=Vo(e,t,r))?e.sibling:null);_a(Zi,1&Zi.current);break;case 19:if(n=0!==(r&t.childLanes),0!==(128&e.flags)){if(n)return Ho(e,t,r);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),_a(Zi,Zi.current),n)break;return null;case 22:case 23:return t.lanes=0,xo(e,t,r)}return Vo(e,t,r)}(e,t,r);yo=0!==(131072&e.flags)}else yo=!1,ai&&0!==(1048576&t.flags)&&Za(t,Ga,t.index);switch(t.lanes=0,t.tag){case 2:var n=t.type;Wo(e,t),e=t.pendingProps;var a=Ra(t,Aa.current);Ni(t,r),a=ml(null,t,n,e,a,r);var l=gl();return t.flags|=1,"object"===typeof a&&null!==a&&"function"===typeof a.render&&void 0===a.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Pa(n)?(l=!0,Ma(t)):l=!1,t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,Mi(t),a.updater=no,t.stateNode=a,a._reactInternals=t,oo(t,n,e,r),t=Ao(null,t,n,!0,l,r)):(t.tag=0,ai&&l&&ei(t),wo(null,t,a,r),t=t.child),t;case 16:n=t.elementType;e:{switch(Wo(e,t),e=t.pendingProps,n=(a=n._init)(n._payload),t.type=n,a=t.tag=function(e){if("function"===typeof e)return Pc(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===A)return 11;if(e===R)return 14}return 2}(n),e=to(n,e),a){case 0:t=_o(null,t,n,e,r);break e;case 1:t=Co(null,t,n,e,r);break e;case 11:t=To(null,t,n,e,r);break e;case 14:t=Eo(null,t,n,to(n.type,e),r);break e}throw Error(i(306,n,""))}return t;case 0:return n=t.type,a=t.pendingProps,_o(e,t,n,a=t.elementType===n?a:to(n,a),r);case 1:return n=t.type,a=t.pendingProps,Co(e,t,n,a=t.elementType===n?a:to(n,a),r);case 3:e:{if(No(t),null===e)throw Error(i(387));n=t.pendingProps,a=(l=t.memoizedState).element,Fi(e,t),Hi(t,n,null,r);var o=t.memoizedState;if(n=o.element,l.isDehydrated){if(l={element:n,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=l,t.memoizedState=l,256&t.flags){t=Oo(e,t,n,r,a=so(Error(i(423)),t));break e}if(n!==a){t=Oo(e,t,n,r,a=so(Error(i(424)),t));break e}for(ni=ca(t.stateNode.containerInfo.firstChild),ri=t,ai=!0,ii=null,r=Ti(t,null,n,r),t.child=r;r;)r.flags=-3&r.flags|4096,r=r.sibling}else{if(di(),n===a){t=Vo(e,t,r);break e}wo(e,t,n,r)}t=t.child}return t;case 5:return qi(t),null===e&&ci(t),n=t.type,a=t.pendingProps,l=null!==e?e.memoizedProps:null,o=a.children,ra(n,a)?o=null:null!==l&&ra(n,l)&&(t.flags|=32),ko(e,t),wo(e,t,o,r),t.child;case 6:return null===e&&ci(t),null;case 13:return Fo(e,t,r);case 4:return Xi(t,t.stateNode.containerInfo),n=t.pendingProps,null===e?t.child=wi(t,null,n,r):wo(e,t,n,r),t.child;case 11:return n=t.type,a=t.pendingProps,To(e,t,n,a=t.elementType===n?a:to(n,a),r);case 7:return wo(e,t,t.pendingProps,r),t.child;case 8:case 12:return wo(e,t,t.pendingProps.children,r),t.child;case 10:e:{if(n=t.type._context,a=t.pendingProps,l=t.memoizedProps,o=a.value,_a(Ei,n._currentValue),n._currentValue=o,null!==l)if(on(l.value,o)){if(l.children===a.children&&!Na.current){t=Vo(e,t,r);break e}}else for(null!==(l=t.child)&&(l.return=t);null!==l;){var s=l.dependencies;if(null!==s){o=l.child;for(var c=s.firstContext;null!==c;){if(c.context===n){if(1===l.tag){(c=ji(-1,r&-r)).tag=2;var u=l.updateQueue;if(null!==u){var f=(u=u.shared).pending;null===f?c.next=c:(c.next=f.next,f.next=c),u.pending=c}}l.lanes|=r,null!==(c=l.alternate)&&(c.lanes|=r),Ai(l.return,r,t),s.lanes|=r;break}c=c.next}}else if(10===l.tag)o=l.type===t.type?null:l.child;else if(18===l.tag){if(null===(o=l.return))throw Error(i(341));o.lanes|=r,null!==(s=o.alternate)&&(s.lanes|=r),Ai(o,r,t),o=l.sibling}else o=l.child;if(null!==o)o.return=l;else for(o=l;null!==o;){if(o===t){o=null;break}if(null!==(l=o.sibling)){l.return=o.return,o=l;break}o=o.return}l=o}wo(e,t,a.children,r),t=t.child}return t;case 9:return a=t.type,n=t.pendingProps.children,Ni(t,r),n=n(a=Oi(a)),t.flags|=1,wo(e,t,n,r),t.child;case 14:return a=to(n=t.type,t.pendingProps),Eo(e,t,n,a=to(n.type,a),r);case 15:return So(e,t,t.type,t.pendingProps,r);case 17:return n=t.type,a=t.pendingProps,a=t.elementType===n?a:to(n,a),Wo(e,t),t.tag=1,Pa(n)?(e=!0,Ma(t)):e=!1,Ni(t,r),io(t,n,a),oo(t,n,a,r),Ao(null,t,n,!0,e,r);case 19:return Ho(e,t,r);case 22:return xo(e,t,r)}throw Error(i(156,t.tag))};var Kc="function"===typeof reportError?reportError:function(e){console.error(e)};function Yc(e){this._internalRoot=e}function Xc(e){this._internalRoot=e}function Qc(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function qc(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Jc(){}function Zc(e,t,r,n,a){var i=r._reactRootContainer;if(i){var l=i;if("function"===typeof a){var o=a;a=function(){var e=Vc(l);o.call(e)}}Wc(t,l,e,a)}else l=function(e,t,r,n,a){if(a){if("function"===typeof n){var i=n;n=function(){var e=Vc(l);i.call(e)}}var l=Hc(t,n,e,0,null,!1,0,"",Jc);return e._reactRootContainer=l,e[pa]=l.current,Hn(8===e.nodeType?e.parentNode:e),uc(),l}for(;a=e.lastChild;)e.removeChild(a);if("function"===typeof n){var o=n;n=function(){var e=Vc(s);o.call(e)}}var s=Bc(e,0,!1,null,0,!1,0,"",Jc);return e._reactRootContainer=s,e[pa]=s.current,Hn(8===e.nodeType?e.parentNode:e),uc((function(){Wc(t,s,r,n)})),s}(r,t,e,a,n);return Vc(l)}Xc.prototype.render=Yc.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(i(409));Wc(e,t,null,null)},Xc.prototype.unmount=Yc.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;uc((function(){Wc(null,e,null,null)})),t[pa]=null}},Xc.prototype.unstable_scheduleHydration=function(e){if(e){var t=xt();e={blockedOn:null,target:e,priority:t};for(var r=0;r<It.length&&0!==t&&t<It[r].priority;r++);It.splice(r,0,e),0===r&&Ft(e)}},Tt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var r=ft(t.pendingLanes);0!==r&&(bt(t,1|r),nc(t,qe()),0===(6&As)&&(Hs=qe()+500,Ha()))}break;case 13:uc((function(){var t=Di(e,1);if(null!==t){var r=ec();rc(t,e,1,r)}})),Gc(e,1)}},Et=function(e){if(13===e.tag){var t=Di(e,134217728);if(null!==t)rc(t,e,134217728,ec());Gc(e,134217728)}},St=function(e){if(13===e.tag){var t=tc(e),r=Di(e,t);if(null!==r)rc(r,e,t,ec());Gc(e,t)}},xt=function(){return yt},kt=function(e,t){var r=yt;try{return yt=e,t()}finally{yt=r}},Ee=function(e,t,r){switch(t){case"input":if(J(e,r),t=r.name,"radio"===r.type&&null!=t){for(r=e;r.parentNode;)r=r.parentNode;for(r=r.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<r.length;t++){var n=r[t];if(n!==e&&n.form===e.form){var a=Ta(n);if(!a)throw Error(i(90));K(n),J(n,a)}}}break;case"textarea":ie(e,r);break;case"select":null!=(t=r.value)&&re(e,!!r.multiple,t,!1)}},Ae=cc,Ne=uc;var eu={usingClientEntryPoint:!1,Events:[ya,wa,Ta,_e,Ce,cc]},tu={findFiberByHostInstance:ba,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},ru={bundleType:tu.bundleType,version:tu.version,rendererPackageName:tu.rendererPackageName,rendererConfig:tu.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:w.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=$e(e))?null:e.stateNode},findFiberByHostInstance:tu.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var nu=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!nu.isDisabled&&nu.supportsFiber)try{at=nu.inject(ru),it=nu}catch(ue){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=eu,t.createPortal=function(e,t){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Qc(t))throw Error(i(200));return function(e,t,r){var n=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:E,key:null==n?null:""+n,children:e,containerInfo:t,implementation:r}}(e,t,null,r)},t.createRoot=function(e,t){if(!Qc(e))throw Error(i(299));var r=!1,n="",a=Kc;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(r=!0),void 0!==t.identifierPrefix&&(n=t.identifierPrefix),void 0!==t.onRecoverableError&&(a=t.onRecoverableError)),t=Bc(e,1,!1,null,0,r,0,n,a),e[pa]=t.current,Hn(8===e.nodeType?e.parentNode:e),new Yc(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(i(188));throw e=Object.keys(e).join(","),Error(i(268,e))}return e=null===(e=$e(t))?null:e.stateNode},t.flushSync=function(e){return uc(e)},t.hydrate=function(e,t,r){if(!qc(t))throw Error(i(200));return Zc(null,e,t,!0,r)},t.hydrateRoot=function(e,t,r){if(!Qc(e))throw Error(i(405));var n=null!=r&&r.hydratedSources||null,a=!1,l="",o=Kc;if(null!==r&&void 0!==r&&(!0===r.unstable_strictMode&&(a=!0),void 0!==r.identifierPrefix&&(l=r.identifierPrefix),void 0!==r.onRecoverableError&&(o=r.onRecoverableError)),t=Hc(t,null,e,1,null!=r?r:null,a,0,l,o),e[pa]=t.current,Hn(e),n)for(e=0;e<n.length;e++)a=(a=(r=n[e])._getVersion)(r._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[r,a]:t.mutableSourceEagerHydrationData.push(r,a);return new Xc(t)},t.render=function(e,t,r){if(!qc(t))throw Error(i(200));return Zc(null,e,t,!1,r)},t.unmountComponentAtNode=function(e){if(!qc(e))throw Error(i(40));return!!e._reactRootContainer&&(uc((function(){Zc(null,null,e,!1,(function(){e._reactRootContainer=null,e[pa]=null}))})),!0)},t.unstable_batchedUpdates=cc,t.unstable_renderSubtreeIntoContainer=function(e,t,r,n){if(!qc(r))throw Error(i(200));if(null==e||void 0===e._reactInternals)throw Error(i(38));return Zc(e,t,r,!1,n)},t.version="18.3.1-next-f1338f8080-20240426"},352:(e,t,r)=>{var n=r(119);t.createRoot=n.createRoot,t.hydrateRoot=n.hydrateRoot},414:(e,t,r)=>{e.exports=r(654)},654:(e,t,r)=>{var n=r(950),a=Symbol.for("react.element"),i=Symbol.for("react.fragment"),l=Object.prototype.hasOwnProperty,o=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};function c(e,t,r){var n,i={},c=null,u=null;for(n in void 0!==r&&(c=""+r),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(u=t.ref),t)l.call(t,n)&&!s.hasOwnProperty(n)&&(i[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps)void 0===i[n]&&(i[n]=t[n]);return{$$typeof:a,type:e,key:c,ref:u,props:i,_owner:o.current}}t.Fragment=i,t.jsx=c,t.jsxs=c},761:(e,t)=>{function r(e,t){var r=e.length;e.push(t);e:for(;0<r;){var n=r-1>>>1,a=e[n];if(!(0<i(a,t)))break e;e[n]=t,e[r]=a,r=n}}function n(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],r=e.pop();if(r!==t){e[0]=r;e:for(var n=0,a=e.length,l=a>>>1;n<l;){var o=2*(n+1)-1,s=e[o],c=o+1,u=e[c];if(0>i(s,r))c<a&&0>i(u,s)?(e[n]=u,e[c]=r,n=c):(e[n]=s,e[o]=r,n=o);else{if(!(c<a&&0>i(u,r)))break e;e[n]=u,e[c]=r,n=c}}}return t}function i(e,t){var r=e.sortIndex-t.sortIndex;return 0!==r?r:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var l=performance;t.unstable_now=function(){return l.now()}}else{var o=Date,s=o.now();t.unstable_now=function(){return o.now()-s}}var c=[],u=[],f=1,h=null,d=3,p=!1,m=!1,g=!1,v="function"===typeof setTimeout?setTimeout:null,b="function"===typeof clearTimeout?clearTimeout:null,y="undefined"!==typeof setImmediate?setImmediate:null;function w(e){for(var t=n(u);null!==t;){if(null===t.callback)a(u);else{if(!(t.startTime<=e))break;a(u),t.sortIndex=t.expirationTime,r(c,t)}t=n(u)}}function T(e){if(g=!1,w(e),!m)if(null!==n(c))m=!0,I(E);else{var t=n(u);null!==t&&D(T,t.startTime-e)}}function E(e,r){m=!1,g&&(g=!1,b(_),_=-1),p=!0;var i=d;try{for(w(r),h=n(c);null!==h&&(!(h.expirationTime>r)||e&&!N());){var l=h.callback;if("function"===typeof l){h.callback=null,d=h.priorityLevel;var o=l(h.expirationTime<=r);r=t.unstable_now(),"function"===typeof o?h.callback=o:h===n(c)&&a(c),w(r)}else a(c);h=n(c)}if(null!==h)var s=!0;else{var f=n(u);null!==f&&D(T,f.startTime-r),s=!1}return s}finally{h=null,d=i,p=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var S,x=!1,k=null,_=-1,C=5,A=-1;function N(){return!(t.unstable_now()-A<C)}function O(){if(null!==k){var e=t.unstable_now();A=e;var r=!0;try{r=k(!0,e)}finally{r?S():(x=!1,k=null)}}else x=!1}if("function"===typeof y)S=function(){y(O)};else if("undefined"!==typeof MessageChannel){var R=new MessageChannel,P=R.port2;R.port1.onmessage=O,S=function(){P.postMessage(null)}}else S=function(){v(O,0)};function I(e){k=e,x||(x=!0,S())}function D(e,r){_=v((function(){e(t.unstable_now())}),r)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){m||p||(m=!0,I(E))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):C=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return d},t.unstable_getFirstCallbackNode=function(){return n(c)},t.unstable_next=function(e){switch(d){case 1:case 2:case 3:var t=3;break;default:t=d}var r=d;d=t;try{return e()}finally{d=r}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var r=d;d=e;try{return t()}finally{d=r}},t.unstable_scheduleCallback=function(e,a,i){var l=t.unstable_now();switch("object"===typeof i&&null!==i?i="number"===typeof(i=i.delay)&&0<i?l+i:l:i=l,e){case 1:var o=-1;break;case 2:o=250;break;case 5:o=1073741823;break;case 4:o=1e4;break;default:o=5e3}return e={id:f++,callback:a,priorityLevel:e,startTime:i,expirationTime:o=i+o,sortIndex:-1},i>l?(e.sortIndex=i,r(u,e),null===n(c)&&e===n(u)&&(g?(b(_),_=-1):g=!0,D(T,i-l))):(e.sortIndex=o,r(c,e),m||p||(m=!0,I(E))),e},t.unstable_shouldYield=N,t.unstable_wrapCallback=function(e){var t=d;return function(){var r=d;d=t;try{return e.apply(this,arguments)}finally{d=r}}}},950:(e,t,r)=>{e.exports=r(49)}},t={};function r(n){var a=t[n];if(void 0!==a)return a.exports;var i=t[n]={exports:{}};return e[n](i,i.exports,r),i.exports}r.m=e,r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.f={},r.e=e=>Promise.all(Object.keys(r.f).reduce(((t,n)=>(r.f[n](e,t),t)),[])),r.u=e=>"static/js/"+e+".c89db662.chunk.js",r.miniCssF=e=>{},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={},t="steampipe-compliance-dashboard:";r.l=(n,a,i,l)=>{if(e[n])e[n].push(a);else{var o,s;if(void 0!==i)for(var c=document.getElementsByTagName("script"),u=0;u<c.length;u++){var f=c[u];if(f.getAttribute("src")==n||f.getAttribute("data-webpack")==t+i){o=f;break}}o||(s=!0,(o=document.createElement("script")).charset="utf-8",o.timeout=120,r.nc&&o.setAttribute("nonce",r.nc),o.setAttribute("data-webpack",t+i),o.src=n),e[n]=[a];var h=(t,r)=>{o.onerror=o.onload=null,clearTimeout(d);var a=e[n];if(delete e[n],o.parentNode&&o.parentNode.removeChild(o),a&&a.forEach((e=>e(r))),t)return t(r)},d=setTimeout(h.bind(null,void 0,{type:"timeout",target:o}),12e4);o.onerror=h.bind(null,o.onerror),o.onload=h.bind(null,o.onload),s&&document.head.appendChild(o)}}})(),r.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.p="/",(()=>{var e={792:0};r.f.j=(t,n)=>{var a=r.o(e,t)?e[t]:void 0;if(0!==a)if(a)n.push(a[2]);else{var i=new Promise(((r,n)=>a=e[t]=[r,n]));n.push(a[2]=i);var l=r.p+r.u(t),o=new Error;r.l(l,(n=>{if(r.o(e,t)&&(0!==(a=e[t])&&(e[t]=void 0),a)){var i=n&&("load"===n.type?"missing":n.type),l=n&&n.target&&n.target.src;o.message="Loading chunk "+t+" failed.\n("+i+": "+l+")",o.name="ChunkLoadError",o.type=i,o.request=l,a[1](o)}}),"chunk-"+t,t)}};var t=(t,n)=>{var a,i,l=n[0],o=n[1],s=n[2],c=0;if(l.some((t=>0!==e[t]))){for(a in o)r.o(o,a)&&(r.m[a]=o[a]);if(s)s(r)}for(t&&t(n);c<l.length;c++)i=l[c],r.o(e,i)&&e[i]&&e[i][0](),e[i]=0},n=self.webpackChunksteampipe_compliance_dashboard=self.webpackChunksteampipe_compliance_dashboard||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})();var n=r(950),a=r(352),i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};var l=(e,t)=>{const r=(0,n.forwardRef)(((r,a)=>{let{color:l="currentColor",size:o=24,strokeWidth:s=2,absoluteStrokeWidth:c,children:u,...f}=r;return(0,n.createElement)("svg",{ref:a,...i,width:o,height:o,stroke:l,strokeWidth:c?24*Number(s)/Number(o):s,className:`lucide lucide-${h=e,h.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,...f},[...t.map((e=>{let[t,r]=e;return(0,n.createElement)(t,r)})),...(Array.isArray(u)?u:[u])||[]]);var h}));return r.displayName=`${e}`,r};const o=l("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["polyline",{points:"22 4 12 14.01 9 11.01",key:"6xbx8j"}]]),s=l("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),c=l("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),u=l("SkipForward",[["polygon",{points:"5 4 15 12 5 20 5 4",key:"16p6eg"}],["line",{x1:"19",x2:"19",y1:"5",y2:"19",key:"futhcm"}]]),f=l("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),h=l("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]),d=l("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),p=l("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),m=l("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]]),g=l("FolderGit2",[["path",{d:"M9 20H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H20a2 2 0 0 1 2 2v5",key:"1w6njk"}],["circle",{cx:"13",cy:"12",r:"2",key:"1j92g6"}],["path",{d:"M18 19c-2.8 0-5-2.2-5-5v8",key:"pkpw2h"}],["circle",{cx:"20",cy:"19",r:"2",key:"1obnsp"}]]),v=l("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),b=l("FileCheck",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["path",{d:"m9 15 2 2 4-4",key:"1grp1n"}]]),y=l("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),w=l("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),T=l("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]),E=l("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]),S=l("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]),x=l("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),k=l("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);var _=r(414);const C=e=>{let{status:t,className:r=""}=e;switch(null===t||void 0===t?void 0:t.toLowerCase()){case"ok":return(0,_.jsx)(o,{className:`${r} text-green-500`});case"alarm":return(0,_.jsx)(s,{className:`${r} text-red-500`});case"error":return(0,_.jsx)(s,{className:`${r} text-gray-500`});case"info":return(0,_.jsx)(c,{className:`${r} text-blue-500`});case"skip":return(0,_.jsx)(u,{className:`${r} text-gray-500`});default:return null}},A=e=>{let{control:t}=e;const[r,a]=n.useState(!1),i=t.tags?Object.entries(t.tags).map((e=>{let[t,r]=e;return{key:t,value:"boolean"===typeof r?r.toString():r}})):[];return(0,_.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700",children:[(0,_.jsxs)("button",{onClick:()=>a(!r),className:"w-full px-4 py-3 flex items-center justify-between text-left hover:bg-gray-50 dark:hover:bg-gray-700 rounded-t-lg",children:[(0,_.jsxs)("div",{className:"flex items-start gap-3",children:[r?(0,_.jsx)(v,{className:"w-5 h-5 text-gray-400 mt-1"}):(0,_.jsx)(x,{className:"w-5 h-5 text-gray-400 mt-1"}),(0,_.jsxs)("div",{children:[(0,_.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white",children:t.title}),(0,_.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Control ID: ",t.control_id]})]})]}),t.summary&&(0,_.jsx)("div",{className:"flex gap-2",children:Object.entries(t.summary).map((e=>{let[t,r]=e;return r>0&&(0,_.jsxs)("div",{className:"flex items-center gap-1 px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded-full",children:[(0,_.jsx)(C,{status:t,className:"w-4 h-4"}),(0,_.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:r})]},t)}))})]}),r&&(0,_.jsxs)("div",{className:"px-4 py-3 border-t border-gray-200 dark:border-gray-700",children:[t.description&&(0,_.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-300 mb-4",children:t.description}),i.length>0&&(0,_.jsxs)("div",{className:"mb-4",children:[(0,_.jsx)("h4",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Tags"}),(0,_.jsx)("div",{className:"flex flex-wrap gap-2",children:i.map((e=>{let{key:t,value:r}=e;return(0,_.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300",children:[t,": ",r]},t)}))})]})]})]})},N=e=>{var t;let{data:r,isOpen:n,onClose:a}=e;const i=e=>{let t=[];return e?(e.forEach((e=>{e.controls&&(t=[...t,...e.controls]),e.groups&&(t=[...t,...i(e.groups)])})),t):t},l=null!==r&&void 0!==r&&null!==(t=r.benchmark_results)&&void 0!==t&&t.groups?i(r.benchmark_results.groups):[];return n?(0,_.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4",children:(0,_.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[80vh] flex flex-col",children:[(0,_.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700",children:[(0,_.jsx)("h2",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"Benchmark Controls"}),(0,_.jsx)("button",{onClick:a,className:"p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700",children:(0,_.jsx)(k,{className:"w-6 h-6 text-gray-500 dark:text-gray-400"})})]}),(0,_.jsx)("div",{className:"overflow-y-auto flex-1 p-4",children:(0,_.jsx)("div",{className:"space-y-4",children:l.map((e=>(0,_.jsx)(A,{control:e},e.control_id)))})})]})}):null},O=l("Cloud",[["path",{d:"M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z",key:"p7xjir"}]]),R=l("CloudCog",[["path",{d:"M20 16.2A4.5 4.5 0 0 0 17.5 8h-1.8A7 7 0 1 0 4 14.9",key:"19hoja"}],["circle",{cx:"12",cy:"17",r:"3",key:"1spfwm"}],["path",{d:"M12 13v1",key:"176q98"}],["path",{d:"M12 20v1",key:"1wcdkc"}],["path",{d:"M16 17h-1",key:"y560le"}],["path",{d:"M9 17H8",key:"1lfe9z"}],["path",{d:"m15 14-.88.88",key:"12ytk1"}],["path",{d:"M9.88 19.12 9 20",key:"1kmb4r"}],["path",{d:"m15 20-.88-.88",key:"1ipjcf"}],["path",{d:"M9.88 14.88 9 14",key:"c4uok7"}]]),P=l("CloudLightning",[["path",{d:"M6 16.326A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 .5 8.973",key:"1cez44"}],["path",{d:"m13 12-3 5h4l-3 5",key:"1t22er"}]]);var I={version:"0.18.5"},D=1200,L=1252,M=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],F={0:1252,1:65001,2:65001,77:1e4,128:932,129:949,130:1361,134:936,136:950,161:1253,162:1254,163:1258,177:1255,178:1256,186:1257,204:1251,222:874,238:1250,255:1252,69:6969},j=function(e){-1!=M.indexOf(e)&&(L=F[0]=e)};var U=function(e){D=e,j(e)};function B(){U(1200),j(1252)}function z(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r+1)+(e.charCodeAt(2*r)<<8));return t.join("")}var H,W=function(e){return String.fromCharCode(e)},V=function(e){return String.fromCharCode(e)};var G="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function K(e){for(var t="",r=0,n=0,a=0,i=0,l=0,o=0,s=0,c=0;c<e.length;)i=(r=e.charCodeAt(c++))>>2,l=(3&r)<<4|(n=e.charCodeAt(c++))>>4,o=(15&n)<<2|(a=e.charCodeAt(c++))>>6,s=63&a,isNaN(n)?o=s=64:isNaN(a)&&(s=64),t+=G.charAt(i)+G.charAt(l)+G.charAt(o)+G.charAt(s);return t}function Y(e){var t="",r=0,n=0,a=0,i=0,l=0,o=0;e=e.replace(/[^\w\+\/\=]/g,"");for(var s=0;s<e.length;)r=G.indexOf(e.charAt(s++))<<2|(i=G.indexOf(e.charAt(s++)))>>4,t+=String.fromCharCode(r),n=(15&i)<<4|(l=G.indexOf(e.charAt(s++)))>>2,64!==l&&(t+=String.fromCharCode(n)),a=(3&l)<<6|(o=G.indexOf(e.charAt(s++))),64!==o&&(t+=String.fromCharCode(a));return t}var X=function(){return"undefined"!==typeof Buffer&&"undefined"!==typeof process&&"undefined"!==typeof process.versions&&!!process.versions.node}(),Q=function(){if("undefined"!==typeof Buffer){var e=!Buffer.from;if(!e)try{Buffer.from("foo","utf8")}catch(t){e=!0}return e?function(e,t){return t?new Buffer(e,t):new Buffer(e)}:Buffer.from.bind(Buffer)}return function(){}}();function q(e){return X?Buffer.alloc?Buffer.alloc(e):new Buffer(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):new Array(e)}function J(e){return X?Buffer.allocUnsafe?Buffer.allocUnsafe(e):new Buffer(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):new Array(e)}var Z=function(e){return X?Q(e,"binary"):e.split("").map((function(e){return 255&e.charCodeAt(0)}))};function ee(e){if("undefined"===typeof ArrayBuffer)return Z(e);for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),n=0;n!=e.length;++n)r[n]=255&e.charCodeAt(n);return t}function te(e){if(Array.isArray(e))return e.map((function(e){return String.fromCharCode(e)})).join("");for(var t=[],r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}var re=X?function(e){return Buffer.concat(e.map((function(e){return Buffer.isBuffer(e)?e:Q(e)})))}:function(e){if("undefined"!==typeof Uint8Array){var t=0,r=0;for(t=0;t<e.length;++t)r+=e[t].length;var n=new Uint8Array(r),a=0;for(t=0,r=0;t<e.length;r+=a,++t)if(a=e[t].length,e[t]instanceof Uint8Array)n.set(e[t],r);else{if("string"==typeof e[t])throw"wtf";n.set(new Uint8Array(e[t]),r)}return n}return[].concat.apply([],e.map((function(e){return Array.isArray(e)?e:[].slice.call(e)})))};var ne=/\u0000/g,ae=/[\u0001-\u0006]/g;function ie(e){for(var t="",r=e.length-1;r>=0;)t+=e.charAt(r--);return t}function le(e,t){var r=""+e;return r.length>=t?r:bt("0",t-r.length)+r}function oe(e,t){var r=""+e;return r.length>=t?r:bt(" ",t-r.length)+r}function se(e,t){var r=""+e;return r.length>=t?r:r+bt(" ",t-r.length)}var ce=Math.pow(2,32);function ue(e,t){return e>ce||e<-ce?function(e,t){var r=""+Math.round(e);return r.length>=t?r:bt("0",t-r.length)+r}(e,t):function(e,t){var r=""+e;return r.length>=t?r:bt("0",t-r.length)+r}(Math.round(e),t)}function fe(e,t){return t=t||0,e.length>=7+t&&103===(32|e.charCodeAt(t))&&101===(32|e.charCodeAt(t+1))&&110===(32|e.charCodeAt(t+2))&&101===(32|e.charCodeAt(t+3))&&114===(32|e.charCodeAt(t+4))&&97===(32|e.charCodeAt(t+5))&&108===(32|e.charCodeAt(t+6))}var he=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],de=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]];var pe={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"\u4e0a\u5348/\u4e0b\u5348 "hh"\u6642"mm"\u5206"ss"\u79d2 "'},me={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},ge={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function ve(e,t,r){for(var n=e<0?-1:1,a=e*n,i=0,l=1,o=0,s=1,c=0,u=0,f=Math.floor(a);c<t&&(o=(f=Math.floor(a))*l+i,u=f*c+s,!(a-f<5e-8));)a=1/(a-f),i=l,l=o,s=c,c=u;if(u>t&&(c>t?(u=s,o=i):(u=c,o=l)),!r)return[0,n*o,u];var h=Math.floor(n*o/u);return[h,n*o-h*u,u]}function be(e,t,r){if(e>2958465||e<0)return null;var n=0|e,a=Math.floor(86400*(e-n)),i=0,l=[],o={D:n,T:a,u:86400*(e-n)-a,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(Math.abs(o.u)<1e-6&&(o.u=0),t&&t.date1904&&(n+=1462),o.u>.9999&&(o.u=0,86400==++a&&(o.T=a=0,++n,++o.D)),60===n)l=r?[1317,10,29]:[1900,2,29],i=3;else if(0===n)l=r?[1317,8,29]:[1900,1,0],i=6;else{n>60&&--n;var s=new Date(1900,0,1);s.setDate(s.getDate()+n-1),l=[s.getFullYear(),s.getMonth()+1,s.getDate()],i=s.getDay(),n<60&&(i=(i+6)%7),r&&(i=function(e,t){t[0]-=581;var r=e.getDay();e<60&&(r=(r+6)%7);return r}(s,l))}return o.y=l[0],o.m=l[1],o.d=l[2],o.S=a%60,a=Math.floor(a/60),o.M=a%60,a=Math.floor(a/60),o.H=a,o.q=i,o}var ye=new Date(1899,11,31,0,0,0),we=ye.getTime(),Te=new Date(1900,2,1,0,0,0);function Ee(e,t){var r=e.getTime();return t?r-=1262304e5:e>=Te&&(r+=864e5),(r-(we+6e4*(e.getTimezoneOffset()-ye.getTimezoneOffset())))/864e5}function Se(e){return-1==e.indexOf(".")?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function xe(e){var t,r=Math.floor(Math.log(Math.abs(e))*Math.LOG10E);return t=r>=-4&&r<=-1?e.toPrecision(10+r):Math.abs(r)<=9?function(e){var t=e<0?12:11,r=Se(e.toFixed(12));return r.length<=t||(r=e.toPrecision(10)).length<=t?r:e.toExponential(5)}(e):10===r?e.toFixed(10).substr(0,12):function(e){var t=Se(e.toFixed(11));return t.length>(e<0?12:11)||"0"===t||"-0"===t?e.toPrecision(6):t}(e),Se(function(e){return-1==e.indexOf("E")?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2")}(t.toUpperCase()))}function ke(e,t){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(0|e)===e?e.toString(10):xe(e);case"undefined":return"";case"object":if(null==e)return"";if(e instanceof Date)return Ge(14,Ee(e,t&&t.date1904),t)}throw new Error("unsupported value in General format: "+e)}function _e(e,t,r,n){var a,i="",l=0,o=0,s=r.y,c=0;switch(e){case 98:s=r.y+543;case 121:switch(t.length){case 1:case 2:a=s%100,c=2;break;default:a=s%1e4,c=4}break;case 109:switch(t.length){case 1:case 2:a=r.m,c=t.length;break;case 3:return de[r.m-1][1];case 5:return de[r.m-1][0];default:return de[r.m-1][2]}break;case 100:switch(t.length){case 1:case 2:a=r.d,c=t.length;break;case 3:return he[r.q][0];default:return he[r.q][1]}break;case 104:switch(t.length){case 1:case 2:a=1+(r.H+11)%12,c=t.length;break;default:throw"bad hour format: "+t}break;case 72:switch(t.length){case 1:case 2:a=r.H,c=t.length;break;default:throw"bad hour format: "+t}break;case 77:switch(t.length){case 1:case 2:a=r.M,c=t.length;break;default:throw"bad minute format: "+t}break;case 115:if("s"!=t&&"ss"!=t&&".0"!=t&&".00"!=t&&".000"!=t)throw"bad second format: "+t;return 0!==r.u||"s"!=t&&"ss"!=t?(o=n>=2?3===n?1e3:100:1===n?10:1,(l=Math.round(o*(r.S+r.u)))>=60*o&&(l=0),"s"===t?0===l?"0":""+l/o:(i=le(l,2+n),"ss"===t?i.substr(0,2):"."+i.substr(2,t.length-1))):le(r.S,t.length);case 90:switch(t){case"[h]":case"[hh]":a=24*r.D+r.H;break;case"[m]":case"[mm]":a=60*(24*r.D+r.H)+r.M;break;case"[s]":case"[ss]":a=60*(60*(24*r.D+r.H)+r.M)+Math.round(r.S+r.u);break;default:throw"bad abstime format: "+t}c=3===t.length?1:2;break;case 101:a=s,c=1}return c>0?le(a,c):""}function Ce(e){if(e.length<=3)return e;for(var t=e.length%3,r=e.substr(0,t);t!=e.length;t+=3)r+=(r.length>0?",":"")+e.substr(t,3);return r}var Ae=/%/g;function Ne(e,t){var r,n=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(0==t)return"0.0E+0";if(t<0)return"-"+Ne(e,-t);var a=e.indexOf(".");-1===a&&(a=e.indexOf("E"));var i=Math.floor(Math.log(t)*Math.LOG10E)%a;if(i<0&&(i+=a),-1===(r=(t/Math.pow(10,i)).toPrecision(n+1+(a+i)%a)).indexOf("e")){var l=Math.floor(Math.log(t)*Math.LOG10E);for(-1===r.indexOf(".")?r=r.charAt(0)+"."+r.substr(1)+"E+"+(l-r.length+i):r+="E+"+(l-i);"0."===r.substr(0,2);)r=(r=r.charAt(0)+r.substr(2,a)+"."+r.substr(2+a)).replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,(function(e,t,r,n){return t+r+n.substr(0,(a+i)%a)+"."+n.substr(i)+"E"}))}else r=t.toExponential(n);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}var Oe=/# (\?+)( ?)\/( ?)(\d+)/;var Re=/^#*0*\.([0#]+)/,Pe=/\).*[0#]/,Ie=/\(###\) ###\\?-####/;function De(e){for(var t,r="",n=0;n!=e.length;++n)switch(t=e.charCodeAt(n)){case 35:break;case 63:r+=" ";break;case 48:r+="0";break;default:r+=String.fromCharCode(t)}return r}function Le(e,t){var r=Math.pow(10,t);return""+Math.round(e*r)/r}function Me(e,t){var r=e-Math.floor(e),n=Math.pow(10,t);return t<(""+Math.round(r*n)).length?0:Math.round(r*n)}function Fe(e,t,r){if(40===e.charCodeAt(0)&&!t.match(Pe)){var n=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?Fe("n",n,r):"("+Fe("n",n,-r)+")"}if(44===t.charCodeAt(t.length-1))return function(e,t,r){for(var n=t.length-1;44===t.charCodeAt(n-1);)--n;return Be(e,t.substr(0,n),r/Math.pow(10,3*(t.length-n)))}(e,t,r);if(-1!==t.indexOf("%"))return function(e,t,r){var n=t.replace(Ae,""),a=t.length-n.length;return Be(e,n,r*Math.pow(10,2*a))+bt("%",a)}(e,t,r);if(-1!==t.indexOf("E"))return Ne(t,r);if(36===t.charCodeAt(0))return"$"+Fe(e,t.substr(" "==t.charAt(1)?2:1),r);var a,i,l,o,s=Math.abs(r),c=r<0?"-":"";if(t.match(/^00+$/))return c+ue(s,t.length);if(t.match(/^[#?]+$/))return"0"===(a=ue(r,0))&&(a=""),a.length>t.length?a:De(t.substr(0,t.length-a.length))+a;if(i=t.match(Oe))return function(e,t,r){var n=parseInt(e[4],10),a=Math.round(t*n),i=Math.floor(a/n),l=a-i*n,o=n;return r+(0===i?"":""+i)+" "+(0===l?bt(" ",e[1].length+1+e[4].length):oe(l,e[1].length)+e[2]+"/"+e[3]+le(o,e[4].length))}(i,s,c);if(t.match(/^#+0+$/))return c+ue(s,t.length-t.indexOf("0"));if(i=t.match(Re))return a=Le(r,i[1].length).replace(/^([^\.]+)$/,"$1."+De(i[1])).replace(/\.$/,"."+De(i[1])).replace(/\.(\d*)$/,(function(e,t){return"."+t+bt("0",De(i[1]).length-t.length)})),-1!==t.indexOf("0.")?a:a.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),i=t.match(/^(0*)\.(#*)$/))return c+Le(s,i[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,i[1].length?"0.":".");if(i=t.match(/^#{1,3},##0(\.?)$/))return c+Ce(ue(s,0));if(i=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+Fe(e,t,-r):Ce(""+(Math.floor(r)+function(e,t){return t<(""+Math.round((e-Math.floor(e))*Math.pow(10,t))).length?1:0}(r,i[1].length)))+"."+le(Me(r,i[1].length),i[1].length);if(i=t.match(/^#,#*,#0/))return Fe(e,t.replace(/^#,#*,/,""),r);if(i=t.match(/^([0#]+)(\\?-([0#]+))+$/))return a=ie(Fe(e,t.replace(/[\\-]/g,""),r)),l=0,ie(ie(t.replace(/\\/g,"")).replace(/[0#]/g,(function(e){return l<a.length?a.charAt(l++):"0"===e?"0":""})));if(t.match(Ie))return"("+(a=Fe(e,"##########",r)).substr(0,3)+") "+a.substr(3,3)+"-"+a.substr(6);var u="";if(i=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return l=Math.min(i[4].length,7),o=ve(s,Math.pow(10,l)-1,!1),a=""+c," "==(u=Be("n",i[1],o[1])).charAt(u.length-1)&&(u=u.substr(0,u.length-1)+"0"),a+=u+i[2]+"/"+i[3],(u=se(o[2],l)).length<i[4].length&&(u=De(i[4].substr(i[4].length-u.length))+u),a+=u;if(i=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return l=Math.min(Math.max(i[1].length,i[4].length),7),c+((o=ve(s,Math.pow(10,l)-1,!0))[0]||(o[1]?"":"0"))+" "+(o[1]?oe(o[1],l)+i[2]+"/"+i[3]+se(o[2],l):bt(" ",2*l+1+i[2].length+i[3].length));if(i=t.match(/^[#0?]+$/))return a=ue(r,0),t.length<=a.length?a:De(t.substr(0,t.length-a.length))+a;if(i=t.match(/^([#0?]+)\.([#0]+)$/)){a=""+r.toFixed(Math.min(i[2].length,10)).replace(/([^0])0+$/,"$1"),l=a.indexOf(".");var f=t.indexOf(".")-l,h=t.length-a.length-f;return De(t.substr(0,f)+a+t.substr(t.length-h))}if(i=t.match(/^00,000\.([#0]*0)$/))return l=Me(r,i[1].length),r<0?"-"+Fe(e,t,-r):Ce(function(e){return e<2147483647&&e>-2147483648?""+(e>=0?0|e:e-1|0):""+Math.floor(e)}(r)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,(function(e){return"00,"+(e.length<3?le(0,3-e.length):"")+e}))+"."+le(l,i[1].length);switch(t){case"###,##0.00":return Fe(e,"#,##0.00",r);case"###,###":case"##,###":case"#,###":var d=Ce(ue(s,0));return"0"!==d?c+d:"";case"###,###.00":return Fe(e,"###,##0.00",r).replace(/^0\./,".");case"#,###.00":return Fe(e,"#,##0.00",r).replace(/^0\./,".")}throw new Error("unsupported format |"+t+"|")}function je(e,t){var r,n=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(0==t)return"0.0E+0";if(t<0)return"-"+je(e,-t);var a=e.indexOf(".");-1===a&&(a=e.indexOf("E"));var i=Math.floor(Math.log(t)*Math.LOG10E)%a;if(i<0&&(i+=a),!(r=(t/Math.pow(10,i)).toPrecision(n+1+(a+i)%a)).match(/[Ee]/)){var l=Math.floor(Math.log(t)*Math.LOG10E);-1===r.indexOf(".")?r=r.charAt(0)+"."+r.substr(1)+"E+"+(l-r.length+i):r+="E+"+(l-i),r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,(function(e,t,r,n){return t+r+n.substr(0,(a+i)%a)+"."+n.substr(i)+"E"}))}else r=t.toExponential(n);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}function Ue(e,t,r){if(40===e.charCodeAt(0)&&!t.match(Pe)){var n=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?Ue("n",n,r):"("+Ue("n",n,-r)+")"}if(44===t.charCodeAt(t.length-1))return function(e,t,r){for(var n=t.length-1;44===t.charCodeAt(n-1);)--n;return Be(e,t.substr(0,n),r/Math.pow(10,3*(t.length-n)))}(e,t,r);if(-1!==t.indexOf("%"))return function(e,t,r){var n=t.replace(Ae,""),a=t.length-n.length;return Be(e,n,r*Math.pow(10,2*a))+bt("%",a)}(e,t,r);if(-1!==t.indexOf("E"))return je(t,r);if(36===t.charCodeAt(0))return"$"+Ue(e,t.substr(" "==t.charAt(1)?2:1),r);var a,i,l,o,s=Math.abs(r),c=r<0?"-":"";if(t.match(/^00+$/))return c+le(s,t.length);if(t.match(/^[#?]+$/))return a=""+r,0===r&&(a=""),a.length>t.length?a:De(t.substr(0,t.length-a.length))+a;if(i=t.match(Oe))return function(e,t,r){return r+(0===t?"":""+t)+bt(" ",e[1].length+2+e[4].length)}(i,s,c);if(t.match(/^#+0+$/))return c+le(s,t.length-t.indexOf("0"));if(i=t.match(Re))return a=(a=(""+r).replace(/^([^\.]+)$/,"$1."+De(i[1])).replace(/\.$/,"."+De(i[1]))).replace(/\.(\d*)$/,(function(e,t){return"."+t+bt("0",De(i[1]).length-t.length)})),-1!==t.indexOf("0.")?a:a.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),i=t.match(/^(0*)\.(#*)$/))return c+(""+s).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,i[1].length?"0.":".");if(i=t.match(/^#{1,3},##0(\.?)$/))return c+Ce(""+s);if(i=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+Ue(e,t,-r):Ce(""+r)+"."+bt("0",i[1].length);if(i=t.match(/^#,#*,#0/))return Ue(e,t.replace(/^#,#*,/,""),r);if(i=t.match(/^([0#]+)(\\?-([0#]+))+$/))return a=ie(Ue(e,t.replace(/[\\-]/g,""),r)),l=0,ie(ie(t.replace(/\\/g,"")).replace(/[0#]/g,(function(e){return l<a.length?a.charAt(l++):"0"===e?"0":""})));if(t.match(Ie))return"("+(a=Ue(e,"##########",r)).substr(0,3)+") "+a.substr(3,3)+"-"+a.substr(6);var u="";if(i=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return l=Math.min(i[4].length,7),o=ve(s,Math.pow(10,l)-1,!1),a=""+c," "==(u=Be("n",i[1],o[1])).charAt(u.length-1)&&(u=u.substr(0,u.length-1)+"0"),a+=u+i[2]+"/"+i[3],(u=se(o[2],l)).length<i[4].length&&(u=De(i[4].substr(i[4].length-u.length))+u),a+=u;if(i=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return l=Math.min(Math.max(i[1].length,i[4].length),7),c+((o=ve(s,Math.pow(10,l)-1,!0))[0]||(o[1]?"":"0"))+" "+(o[1]?oe(o[1],l)+i[2]+"/"+i[3]+se(o[2],l):bt(" ",2*l+1+i[2].length+i[3].length));if(i=t.match(/^[#0?]+$/))return a=""+r,t.length<=a.length?a:De(t.substr(0,t.length-a.length))+a;if(i=t.match(/^([#0]+)\.([#0]+)$/)){a=""+r.toFixed(Math.min(i[2].length,10)).replace(/([^0])0+$/,"$1"),l=a.indexOf(".");var f=t.indexOf(".")-l,h=t.length-a.length-f;return De(t.substr(0,f)+a+t.substr(t.length-h))}if(i=t.match(/^00,000\.([#0]*0)$/))return r<0?"-"+Ue(e,t,-r):Ce(""+r).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,(function(e){return"00,"+(e.length<3?le(0,3-e.length):"")+e}))+"."+le(0,i[1].length);switch(t){case"###,###":case"##,###":case"#,###":var d=Ce(""+s);return"0"!==d?c+d:"";default:if(t.match(/\.[0#?]*$/))return Ue(e,t.slice(0,t.lastIndexOf(".")),r)+De(t.slice(t.lastIndexOf(".")))}throw new Error("unsupported format |"+t+"|")}function Be(e,t,r){return(0|r)===r?Ue(e,t,r):Fe(e,t,r)}var ze=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function He(e){for(var t=0,r="",n="";t<e.length;)switch(r=e.charAt(t)){case"G":fe(e,t)&&(t+=6),t++;break;case'"':for(;34!==e.charCodeAt(++t)&&t<e.length;);++t;break;case"\\":case"_":t+=2;break;case"@":++t;break;case"B":case"b":if("1"===e.charAt(t+1)||"2"===e.charAt(t+1))return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"\u4e0a":if("A/P"===e.substr(t,3).toUpperCase())return!0;if("AM/PM"===e.substr(t,5).toUpperCase())return!0;if("\u4e0a\u5348/\u4e0b\u5348"===e.substr(t,5).toUpperCase())return!0;++t;break;case"[":for(n=r;"]"!==e.charAt(t++)&&t<e.length;)n+=e.charAt(t);if(n.match(ze))return!0;break;case".":case"0":case"#":for(;t<e.length&&("0#?.,E+-%".indexOf(r=e.charAt(++t))>-1||"\\"==r&&"-"==e.charAt(t+1)&&"0#".indexOf(e.charAt(t+2))>-1););break;case"?":for(;e.charAt(++t)===r;);break;case"*":++t," "!=e.charAt(t)&&"*"!=e.charAt(t)||++t;break;case"(":case")":++t;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;t<e.length&&"0123456789".indexOf(e.charAt(++t))>-1;);break;default:++t}return!1}var We=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function Ve(e,t){if(null==t)return!1;var r=parseFloat(t[2]);switch(t[1]){case"=":if(e==r)return!0;break;case">":if(e>r)return!0;break;case"<":if(e<r)return!0;break;case"<>":if(e!=r)return!0;break;case">=":if(e>=r)return!0;break;case"<=":if(e<=r)return!0}return!1}function $e(e,t){var r=function(e){for(var t=[],r=!1,n=0,a=0;n<e.length;++n)switch(e.charCodeAt(n)){case 34:r=!r;break;case 95:case 42:case 92:++n;break;case 59:t[t.length]=e.substr(a,n-a),a=n+1}if(t[t.length]=e.substr(a),!0===r)throw new Error("Format |"+e+"| unterminated string ");return t}(e),n=r.length,a=r[n-1].indexOf("@");if(n<4&&a>-1&&--n,r.length>4)throw new Error("cannot find right format for |"+r.join("|")+"|");if("number"!==typeof t)return[4,4===r.length||a>-1?r[r.length-1]:"@"];switch(r.length){case 1:r=a>-1?["General","General","General",r[0]]:[r[0],r[0],r[0],"@"];break;case 2:r=a>-1?[r[0],r[0],r[0],r[1]]:[r[0],r[1],r[0],"@"];break;case 3:r=a>-1?[r[0],r[1],r[0],r[2]]:[r[0],r[1],r[2],"@"]}var i=t>0?r[0]:t<0?r[1]:r[2];if(-1===r[0].indexOf("[")&&-1===r[1].indexOf("["))return[n,i];if(null!=r[0].match(/\[[=<>]/)||null!=r[1].match(/\[[=<>]/)){var l=r[0].match(We),o=r[1].match(We);return Ve(t,l)?[n,r[0]]:Ve(t,o)?[n,r[1]]:[n,r[null!=l&&null!=o?2:1]]}return[n,i]}function Ge(e,t,r){null==r&&(r={});var n="";switch(typeof e){case"string":n="m/d/yy"==e&&r.dateNF?r.dateNF:e;break;case"number":null==(n=14==e&&r.dateNF?r.dateNF:(null!=r.table?r.table:pe)[e])&&(n=r.table&&r.table[me[e]]||pe[me[e]]),null==n&&(n=ge[e]||"General")}if(fe(n,0))return ke(t,r);t instanceof Date&&(t=Ee(t,r.date1904));var a=$e(n,t);if(fe(a[1]))return ke(t,r);if(!0===t)t="TRUE";else if(!1===t)t="FALSE";else if(""===t||null==t)return"";return function(e,t,r,n){for(var a,i,l,o=[],s="",c=0,u="",f="t",h="H";c<e.length;)switch(u=e.charAt(c)){case"G":if(!fe(e,c))throw new Error("unrecognized character "+u+" in "+e);o[o.length]={t:"G",v:"General"},c+=7;break;case'"':for(s="";34!==(l=e.charCodeAt(++c))&&c<e.length;)s+=String.fromCharCode(l);o[o.length]={t:"t",v:s},++c;break;case"\\":var d=e.charAt(++c),p="("===d||")"===d?d:"t";o[o.length]={t:p,v:d},++c;break;case"_":o[o.length]={t:"t",v:" "},c+=2;break;case"@":o[o.length]={t:"T",v:t},++c;break;case"B":case"b":if("1"===e.charAt(c+1)||"2"===e.charAt(c+1)){if(null==a&&null==(a=be(t,r,"2"===e.charAt(c+1))))return"";o[o.length]={t:"X",v:e.substr(c,2)},f=u,c+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":u=u.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(t<0)return"";if(null==a&&null==(a=be(t,r)))return"";for(s=u;++c<e.length&&e.charAt(c).toLowerCase()===u;)s+=u;"m"===u&&"h"===f.toLowerCase()&&(u="M"),"h"===u&&(u=h),o[o.length]={t:u,v:s},f=u;break;case"A":case"a":case"\u4e0a":var m={t:u,v:u};if(null==a&&(a=be(t,r)),"A/P"===e.substr(c,3).toUpperCase()?(null!=a&&(m.v=a.H>=12?"P":"A"),m.t="T",h="h",c+=3):"AM/PM"===e.substr(c,5).toUpperCase()?(null!=a&&(m.v=a.H>=12?"PM":"AM"),m.t="T",c+=5,h="h"):"\u4e0a\u5348/\u4e0b\u5348"===e.substr(c,5).toUpperCase()?(null!=a&&(m.v=a.H>=12?"\u4e0b\u5348":"\u4e0a\u5348"),m.t="T",c+=5,h="h"):(m.t="t",++c),null==a&&"T"===m.t)return"";o[o.length]=m,f=u;break;case"[":for(s=u;"]"!==e.charAt(c++)&&c<e.length;)s+=e.charAt(c);if("]"!==s.slice(-1))throw'unterminated "[" block: |'+s+"|";if(s.match(ze)){if(null==a&&null==(a=be(t,r)))return"";o[o.length]={t:"Z",v:s.toLowerCase()},f=s.charAt(1)}else s.indexOf("$")>-1&&(s=(s.match(/\$([^-\[\]]*)/)||[])[1]||"$",He(e)||(o[o.length]={t:"t",v:s}));break;case".":if(null!=a){for(s=u;++c<e.length&&"0"===(u=e.charAt(c));)s+=u;o[o.length]={t:"s",v:s};break}case"0":case"#":for(s=u;++c<e.length&&"0#?.,E+-%".indexOf(u=e.charAt(c))>-1;)s+=u;o[o.length]={t:"n",v:s};break;case"?":for(s=u;e.charAt(++c)===u;)s+=u;o[o.length]={t:u,v:s},f=u;break;case"*":++c," "!=e.charAt(c)&&"*"!=e.charAt(c)||++c;break;case"(":case")":o[o.length]={t:1===n?"t":u,v:u},++c;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(s=u;c<e.length&&"0123456789".indexOf(e.charAt(++c))>-1;)s+=e.charAt(c);o[o.length]={t:"D",v:s};break;case" ":o[o.length]={t:u,v:u},++c;break;case"$":o[o.length]={t:"t",v:"$"},++c;break;default:if(-1===",$-+/():!^&'~{}<>=\u20acacfijklopqrtuvwxzP".indexOf(u))throw new Error("unrecognized character "+u+" in "+e);o[o.length]={t:"t",v:u},++c}var g,v=0,b=0;for(c=o.length-1,f="t";c>=0;--c)switch(o[c].t){case"h":case"H":o[c].t=h,f="h",v<1&&(v=1);break;case"s":(g=o[c].v.match(/\.0+$/))&&(b=Math.max(b,g[0].length-1)),v<3&&(v=3);case"d":case"y":case"M":case"e":f=o[c].t;break;case"m":"s"===f&&(o[c].t="M",v<2&&(v=2));break;case"X":break;case"Z":v<1&&o[c].v.match(/[Hh]/)&&(v=1),v<2&&o[c].v.match(/[Mm]/)&&(v=2),v<3&&o[c].v.match(/[Ss]/)&&(v=3)}switch(v){case 0:break;case 1:a.u>=.5&&(a.u=0,++a.S),a.S>=60&&(a.S=0,++a.M),a.M>=60&&(a.M=0,++a.H);break;case 2:a.u>=.5&&(a.u=0,++a.S),a.S>=60&&(a.S=0,++a.M)}var y,w="";for(c=0;c<o.length;++c)switch(o[c].t){case"t":case"T":case" ":case"D":break;case"X":o[c].v="",o[c].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":o[c].v=_e(o[c].t.charCodeAt(0),o[c].v,a,b),o[c].t="t";break;case"n":case"?":for(y=c+1;null!=o[y]&&("?"===(u=o[y].t)||"D"===u||(" "===u||"t"===u)&&null!=o[y+1]&&("?"===o[y+1].t||"t"===o[y+1].t&&"/"===o[y+1].v)||"("===o[c].t&&(" "===u||"n"===u||")"===u)||"t"===u&&("/"===o[y].v||" "===o[y].v&&null!=o[y+1]&&"?"==o[y+1].t));)o[c].v+=o[y].v,o[y]={v:"",t:";"},++y;w+=o[c].v,c=y-1;break;case"G":o[c].t="t",o[c].v=ke(t,r)}var T,E,S="";if(w.length>0){40==w.charCodeAt(0)?(T=t<0&&45===w.charCodeAt(0)?-t:t,E=Be("n",w,T)):(E=Be("n",w,T=t<0&&n>1?-t:t),T<0&&o[0]&&"t"==o[0].t&&(E=E.substr(1),o[0].v="-"+o[0].v)),y=E.length-1;var x=o.length;for(c=0;c<o.length;++c)if(null!=o[c]&&"t"!=o[c].t&&o[c].v.indexOf(".")>-1){x=c;break}var k=o.length;if(x===o.length&&-1===E.indexOf("E")){for(c=o.length-1;c>=0;--c)null!=o[c]&&-1!=="n?".indexOf(o[c].t)&&(y>=o[c].v.length-1?(y-=o[c].v.length,o[c].v=E.substr(y+1,o[c].v.length)):y<0?o[c].v="":(o[c].v=E.substr(0,y+1),y=-1),o[c].t="t",k=c);y>=0&&k<o.length&&(o[k].v=E.substr(0,y+1)+o[k].v)}else if(x!==o.length&&-1===E.indexOf("E")){for(y=E.indexOf(".")-1,c=x;c>=0;--c)if(null!=o[c]&&-1!=="n?".indexOf(o[c].t)){for(i=o[c].v.indexOf(".")>-1&&c===x?o[c].v.indexOf(".")-1:o[c].v.length-1,S=o[c].v.substr(i+1);i>=0;--i)y>=0&&("0"===o[c].v.charAt(i)||"#"===o[c].v.charAt(i))&&(S=E.charAt(y--)+S);o[c].v=S,o[c].t="t",k=c}for(y>=0&&k<o.length&&(o[k].v=E.substr(0,y+1)+o[k].v),y=E.indexOf(".")+1,c=x;c<o.length;++c)if(null!=o[c]&&(-1!=="n?(".indexOf(o[c].t)||c===x)){for(i=o[c].v.indexOf(".")>-1&&c===x?o[c].v.indexOf(".")+1:0,S=o[c].v.substr(0,i);i<o[c].v.length;++i)y<E.length&&(S+=E.charAt(y++));o[c].v=S,o[c].t="t",k=c}}}for(c=0;c<o.length;++c)null!=o[c]&&"n?".indexOf(o[c].t)>-1&&(T=n>1&&t<0&&c>0&&"-"===o[c-1].v?-t:t,o[c].v=Be(o[c].t,o[c].v,T),o[c].t="t");var _="";for(c=0;c!==o.length;++c)null!=o[c]&&(_+=o[c].v);return _}(a[1],t,r,a[0])}function Ke(e,t){if("number"!=typeof t){t=+t||-1;for(var r=0;r<392;++r)if(void 0!=pe[r]){if(pe[r]==e){t=r;break}}else t<0&&(t=r);t<0&&(t=391)}return pe[t]=e,t}function Ye(e){for(var t=0;392!=t;++t)void 0!==e[t]&&Ke(e[t],t)}function Xe(){var e;e||(e={}),e[0]="General",e[1]="0",e[2]="0.00",e[3]="#,##0",e[4]="#,##0.00",e[9]="0%",e[10]="0.00%",e[11]="0.00E+00",e[12]="# ?/?",e[13]="# ??/??",e[14]="m/d/yy",e[15]="d-mmm-yy",e[16]="d-mmm",e[17]="mmm-yy",e[18]="h:mm AM/PM",e[19]="h:mm:ss AM/PM",e[20]="h:mm",e[21]="h:mm:ss",e[22]="m/d/yy h:mm",e[37]="#,##0 ;(#,##0)",e[38]="#,##0 ;[Red](#,##0)",e[39]="#,##0.00;(#,##0.00)",e[40]="#,##0.00;[Red](#,##0.00)",e[45]="mm:ss",e[46]="[h]:mm:ss",e[47]="mmss.0",e[48]="##0.0E+0",e[49]="@",e[56]='"\u4e0a\u5348/\u4e0b\u5348 "hh"\u6642"mm"\u5206"ss"\u79d2 "',pe=e}var Qe=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g;var qe=function(){var e={};e.version="1.2.0";var t=function(){for(var e=0,t=new Array(256),r=0;256!=r;++r)e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=r)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1,t[r]=e;return"undefined"!==typeof Int32Array?new Int32Array(t):t}();var r=function(e){var t=0,r=0,n=0,a="undefined"!==typeof Int32Array?new Int32Array(4096):new Array(4096);for(n=0;256!=n;++n)a[n]=e[n];for(n=0;256!=n;++n)for(r=e[n],t=256+n;t<4096;t+=256)r=a[t]=r>>>8^e[255&r];var i=[];for(n=1;16!=n;++n)i[n-1]="undefined"!==typeof Int32Array?a.subarray(256*n,256*n+256):a.slice(256*n,256*n+256);return i}(t),n=r[0],a=r[1],i=r[2],l=r[3],o=r[4],s=r[5],c=r[6],u=r[7],f=r[8],h=r[9],d=r[10],p=r[11],m=r[12],g=r[13],v=r[14];return e.table=t,e.bstr=function(e,r){for(var n=~r,a=0,i=e.length;a<i;)n=n>>>8^t[255&(n^e.charCodeAt(a++))];return~n},e.buf=function(e,r){for(var b=~r,y=e.length-15,w=0;w<y;)b=v[e[w++]^255&b]^g[e[w++]^b>>8&255]^m[e[w++]^b>>16&255]^p[e[w++]^b>>>24]^d[e[w++]]^h[e[w++]]^f[e[w++]]^u[e[w++]]^c[e[w++]]^s[e[w++]]^o[e[w++]]^l[e[w++]]^i[e[w++]]^a[e[w++]]^n[e[w++]]^t[e[w++]];for(y+=15;w<y;)b=b>>>8^t[255&(b^e[w++])];return~b},e.str=function(e,r){for(var n=~r,a=0,i=e.length,l=0,o=0;a<i;)(l=e.charCodeAt(a++))<128?n=n>>>8^t[255&(n^l)]:l<2048?n=(n=n>>>8^t[255&(n^(192|l>>6&31))])>>>8^t[255&(n^(128|63&l))]:l>=55296&&l<57344?(l=64+(1023&l),o=1023&e.charCodeAt(a++),n=(n=(n=(n=n>>>8^t[255&(n^(240|l>>8&7))])>>>8^t[255&(n^(128|l>>2&63))])>>>8^t[255&(n^(128|o>>6&15|(3&l)<<4))])>>>8^t[255&(n^(128|63&o))]):n=(n=(n=n>>>8^t[255&(n^(224|l>>12&15))])>>>8^t[255&(n^(128|l>>6&63))])>>>8^t[255&(n^(128|63&l))];return~n},e}(),Je=function(){var e,t={};function r(e){if("/"==e.charAt(e.length-1))return-1===e.slice(0,-1).indexOf("/")?e:r(e.slice(0,-1));var t=e.lastIndexOf("/");return-1===t?e:e.slice(0,t+1)}function n(e){if("/"==e.charAt(e.length-1))return n(e.slice(0,-1));var t=e.lastIndexOf("/");return-1===t?e:e.slice(t+1)}function a(e,t){"string"===typeof t&&(t=new Date(t));var r=t.getHours();r=(r=r<<6|t.getMinutes())<<5|t.getSeconds()>>>1,e.write_shift(2,r);var n=t.getFullYear()-1980;n=(n=n<<4|t.getMonth()+1)<<5|t.getDate(),e.write_shift(2,n)}function i(e){Kr(e,0);for(var t={},r=0;e.l<=e.length-4;){var n=e.read_shift(2),a=e.read_shift(2),i=e.l+a,l={};if(21589===n)1&(r=e.read_shift(1))&&(l.mtime=e.read_shift(4)),a>5&&(2&r&&(l.atime=e.read_shift(4)),4&r&&(l.ctime=e.read_shift(4))),l.mtime&&(l.mt=new Date(1e3*l.mtime));e.l=i,t[n]=l}return t}function l(){return e||(e={})}function o(e,t){if(80==e[0]&&75==e[1])return de(e,t);if(109==(32|e[0])&&105==(32|e[1]))return function(e,t){if("mime-version:"!=E(e.slice(0,13)).toLowerCase())throw new Error("Unsupported MAD header");var r=t&&t.root||"",n=(X&&Buffer.isBuffer(e)?e.toString("binary"):E(e)).split("\r\n"),a=0,i="";for(a=0;a<n.length;++a)if(i=n[a],/^Content-Location:/i.test(i)&&(i=i.slice(i.indexOf("file")),r||(r=i.slice(0,i.lastIndexOf("/")+1)),i.slice(0,r.length)!=r))for(;r.length>0&&(r=(r=r.slice(0,r.length-1)).slice(0,r.lastIndexOf("/")+1),i.slice(0,r.length)!=r););var l=(n[1]||"").match(/boundary="(.*?)"/);if(!l)throw new Error("MAD cannot find boundary");var o="--"+(l[1]||""),s=[],c=[],u={FileIndex:s,FullPaths:c};h(u);var f,d=0;for(a=0;a<n.length;++a){var p=n[a];p!==o&&p!==o+"--"||(d++&&ye(u,n.slice(f,a),r),f=a)}return u}(e,t);if(e.length<512)throw new Error("CFB file size "+e.length+" < 512");var r,n,a,i,l,o,d=512,p=[],m=e.slice(0,512);Kr(m,0);var g=function(e){if(80==e[e.l]&&75==e[e.l+1])return[0,0];e.chk(b,"Header Signature: "),e.l+=16;var t=e.read_shift(2,"u");return[e.read_shift(2,"u"),t]}(m);switch(r=g[0]){case 3:d=512;break;case 4:d=4096;break;case 0:if(0==g[1])return de(e,t);default:throw new Error("Major Version: Expected 3 or 4 saw "+r)}512!==d&&Kr(m=e.slice(0,d),28);var y=e.slice(0,d);!function(e,t){var r=9;switch(e.l+=2,r=e.read_shift(2)){case 9:if(3!=t)throw new Error("Sector Shift: Expected 9 saw "+r);break;case 12:if(4!=t)throw new Error("Sector Shift: Expected 12 saw "+r);break;default:throw new Error("Sector Shift: Expected 9 or 12 saw "+r)}e.chk("0600","Mini Sector Shift: "),e.chk("000000000000","Reserved: ")}(m,r);var w=m.read_shift(4,"i");if(3===r&&0!==w)throw new Error("# Directory Sectors: Expected 0 saw "+w);m.l+=4,i=m.read_shift(4,"i"),m.l+=4,m.chk("00100000","Mini Stream Cutoff Size: "),l=m.read_shift(4,"i"),n=m.read_shift(4,"i"),o=m.read_shift(4,"i"),a=m.read_shift(4,"i");for(var T=-1,S=0;S<109&&!((T=m.read_shift(4,"i"))<0);++S)p[S]=T;var x=function(e,t){for(var r=Math.ceil(e.length/t)-1,n=[],a=1;a<r;++a)n[a-1]=e.slice(a*t,(a+1)*t);return n[r-1]=e.slice(r*t),n}(e,d);c(o,a,x,d,p);var k=function(e,t,r,n){var a=e.length,i=[],l=[],o=[],s=[],c=n-1,u=0,f=0,h=0,d=0;for(u=0;u<a;++u)if(o=[],(h=u+t)>=a&&(h-=a),!l[h]){s=[];var p=[];for(f=h;f>=0;){p[f]=!0,l[f]=!0,o[o.length]=f,s.push(e[f]);var m=r[Math.floor(4*f/n)];if(n<4+(d=4*f&c))throw new Error("FAT boundary crossed: "+f+" 4 "+n);if(!e[m])break;if(p[f=Ur(e[m],d)])break}i[h]={nodes:o,data:pr([s])}}return i}(x,i,p,d);k[i].name="!Directory",n>0&&l!==v&&(k[l].name="!MiniFAT"),k[p[0]].name="!FAT",k.fat_addrs=p,k.ssz=d;var _=[],C=[],A=[];!function(e,t,r,n,a,i,l,o){for(var c,h=0,d=n.length?2:0,p=t[e].data,m=0,g=0;m<p.length;m+=128){var b=p.slice(m,m+128);Kr(b,64),g=b.read_shift(2),c=gr(b,0,g-d),n.push(c);var y={name:c,type:b.read_shift(1),color:b.read_shift(1),L:b.read_shift(4,"i"),R:b.read_shift(4,"i"),C:b.read_shift(4,"i"),clsid:b.read_shift(16),state:b.read_shift(4,"i"),start:0,size:0};0!==b.read_shift(2)+b.read_shift(2)+b.read_shift(2)+b.read_shift(2)&&(y.ct=f(b,b.l-8)),0!==b.read_shift(2)+b.read_shift(2)+b.read_shift(2)+b.read_shift(2)&&(y.mt=f(b,b.l-8)),y.start=b.read_shift(4,"i"),y.size=b.read_shift(4,"i"),y.size<0&&y.start<0&&(y.size=y.type=0,y.start=v,y.name=""),5===y.type?(h=y.start,a>0&&h!==v&&(t[h].name="!StreamData")):y.size>=4096?(y.storage="fat",void 0===t[y.start]&&(t[y.start]=u(r,y.start,t.fat_addrs,t.ssz)),t[y.start].name=y.name,y.content=t[y.start].data.slice(0,y.size)):(y.storage="minifat",y.size<0?y.size=0:h!==v&&y.start!==v&&t[h]&&(y.content=s(y,t[h].data,(t[o]||{}).data))),y.content&&Kr(y.content,0),i[c]=y,l.push(y)}}(i,k,x,_,n,{},C,l),function(e,t,r){for(var n=0,a=0,i=0,l=0,o=0,s=r.length,c=[],u=[];n<s;++n)c[n]=u[n]=n,t[n]=r[n];for(;o<u.length;++o)a=e[n=u[o]].L,i=e[n].R,l=e[n].C,c[n]===n&&(-1!==a&&c[a]!==a&&(c[n]=c[a]),-1!==i&&c[i]!==i&&(c[n]=c[i])),-1!==l&&(c[l]=n),-1!==a&&n!=c[n]&&(c[a]=c[n],u.lastIndexOf(a)<o&&u.push(a)),-1!==i&&n!=c[n]&&(c[i]=c[n],u.lastIndexOf(i)<o&&u.push(i));for(n=1;n<s;++n)c[n]===n&&(-1!==i&&c[i]!==i?c[n]=c[i]:-1!==a&&c[a]!==a&&(c[n]=c[a]));for(n=1;n<s;++n)if(0!==e[n].type){if((o=n)!=c[o])do{o=c[o],t[n]=t[o]+"/"+t[n]}while(0!==o&&-1!==c[o]&&o!=c[o]);c[n]=-1}for(t[0]+="/",n=1;n<s;++n)2!==e[n].type&&(t[n]+="/")}(C,A,_),_.shift();var N={FileIndex:C,FullPaths:A};return t&&t.raw&&(N.raw={header:y,sectors:x}),N}function s(e,t,r){for(var n=e.start,a=e.size,i=[],l=n;r&&a>0&&l>=0;)i.push(t.slice(l*g,l*g+g)),a-=g,l=Ur(r,4*l);return 0===i.length?Xr(0):re(i).slice(0,e.size)}function c(e,t,r,n,a){var i=v;if(e===v){if(0!==t)throw new Error("DIFAT chain shorter than expected")}else if(-1!==e){var l=r[e],o=(n>>>2)-1;if(!l)return;for(var s=0;s<o&&(i=Ur(l,4*s))!==v;++s)a.push(i);c(Ur(l,n-4),t-1,r,n,a)}}function u(e,t,r,n,a){var i=[],l=[];a||(a=[]);var o=n-1,s=0,c=0;for(s=t;s>=0;){a[s]=!0,i[i.length]=s,l.push(e[s]);var u=r[Math.floor(4*s/n)];if(n<4+(c=4*s&o))throw new Error("FAT boundary crossed: "+s+" 4 "+n);if(!e[u])break;s=Ur(e[u],c)}return{nodes:i,data:pr([l])}}function f(e,t){return new Date(1e3*(jr(e,t+4)/1e7*Math.pow(2,32)+jr(e,t)/1e7-11644473600))}function h(e,t){var r=t||{},n=r.root||"Root Entry";if(e.FullPaths||(e.FullPaths=[]),e.FileIndex||(e.FileIndex=[]),e.FullPaths.length!==e.FileIndex.length)throw new Error("inconsistent CFB structure");0===e.FullPaths.length&&(e.FullPaths[0]=n+"/",e.FileIndex[0]={name:n,type:5}),r.CLSID&&(e.FileIndex[0].clsid=r.CLSID),function(e){var t="\x01Sh33tJ5";if(Je.find(e,"/"+t))return;var r=Xr(4);r[0]=55,r[1]=r[3]=50,r[2]=54,e.FileIndex.push({name:t,type:2,content:r,size:4,L:69,R:69,C:69}),e.FullPaths.push(e.FullPaths[0]+t),d(e)}(e)}function d(e,t){h(e);for(var a=!1,i=!1,l=e.FullPaths.length-1;l>=0;--l){var o=e.FileIndex[l];switch(o.type){case 0:i?a=!0:(e.FileIndex.pop(),e.FullPaths.pop());break;case 1:case 2:case 5:i=!0,isNaN(o.R*o.L*o.C)&&(a=!0),o.R>-1&&o.L>-1&&o.R==o.L&&(a=!0);break;default:a=!0}}if(a||t){var s=new Date(1987,1,19),c=0,u=Object.create?Object.create(null):{},f=[];for(l=0;l<e.FullPaths.length;++l)u[e.FullPaths[l]]=!0,0!==e.FileIndex[l].type&&f.push([e.FullPaths[l],e.FileIndex[l]]);for(l=0;l<f.length;++l){var d=r(f[l][0]);(i=u[d])||(f.push([d,{name:n(d).replace("/",""),type:1,clsid:w,ct:s,mt:s,content:null}]),u[d]=!0)}for(f.sort((function(e,t){return function(e,t){for(var r=e.split("/"),n=t.split("/"),a=0,i=0,l=Math.min(r.length,n.length);a<l;++a){if(i=r[a].length-n[a].length)return i;if(r[a]!=n[a])return r[a]<n[a]?-1:1}return r.length-n.length}(e[0],t[0])})),e.FullPaths=[],e.FileIndex=[],l=0;l<f.length;++l)e.FullPaths[l]=f[l][0],e.FileIndex[l]=f[l][1];for(l=0;l<f.length;++l){var p=e.FileIndex[l],m=e.FullPaths[l];if(p.name=n(m).replace("/",""),p.L=p.R=p.C=-(p.color=1),p.size=p.content?p.content.length:0,p.start=0,p.clsid=p.clsid||w,0===l)p.C=f.length>1?1:-1,p.size=0,p.type=5;else if("/"==m.slice(-1)){for(c=l+1;c<f.length&&r(e.FullPaths[c])!=m;++c);for(p.C=c>=f.length?-1:c,c=l+1;c<f.length&&r(e.FullPaths[c])!=r(m);++c);p.R=c>=f.length?-1:c,p.type=1}else r(e.FullPaths[l+1]||"")==r(m)&&(p.R=l+1),p.type=2}}}function p(e,t){var r=t||{};if("mad"==r.fileType)return function(e,t){for(var r=t||{},n=r.boundary||"SheetJS",a=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+(n="------="+n).slice(2)+'"',"","",""],i=e.FullPaths[0],l=i,o=e.FileIndex[0],s=1;s<e.FullPaths.length;++s)if(l=e.FullPaths[s].slice(i.length),(o=e.FileIndex[s]).size&&o.content&&"\x01Sh33tJ5"!=l){l=l.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,(function(e){return"_x"+e.charCodeAt(0).toString(16)+"_"})).replace(/[\u0080-\uFFFF]/g,(function(e){return"_u"+e.charCodeAt(0).toString(16)+"_"}));for(var c=o.content,u=X&&Buffer.isBuffer(c)?c.toString("binary"):E(c),f=0,h=Math.min(1024,u.length),d=0,p=0;p<=h;++p)(d=u.charCodeAt(p))>=32&&d<128&&++f;var m=f>=4*h/5;a.push(n),a.push("Content-Location: "+(r.root||"file:///C:/SheetJS/")+l),a.push("Content-Transfer-Encoding: "+(m?"quoted-printable":"base64")),a.push("Content-Type: "+ge(o,l)),a.push(""),a.push(m?be(u):ve(u))}return a.push(n+"--\r\n"),a.join("\r\n")}(e,r);if(d(e),"zip"===r.fileType)return function(e,t){var r=t||{},n=[],i=[],l=Xr(1),o=r.compression?8:0,s=0;0;var c=0,u=0,f=0,h=0,d=e.FullPaths[0],p=d,m=e.FileIndex[0],g=[],v=0;for(c=1;c<e.FullPaths.length;++c)if(p=e.FullPaths[c].slice(d.length),(m=e.FileIndex[c]).size&&m.content&&"\x01Sh33tJ5"!=p){var b=f,y=Xr(p.length);for(u=0;u<p.length;++u)y.write_shift(1,127&p.charCodeAt(u));y=y.slice(0,y.l),g[h]=qe.buf(m.content,0);var w=m.content;8==o&&(w=S(w)),(l=Xr(30)).write_shift(4,67324752),l.write_shift(2,20),l.write_shift(2,s),l.write_shift(2,o),m.mt?a(l,m.mt):l.write_shift(4,0),l.write_shift(-4,8&s?0:g[h]),l.write_shift(4,8&s?0:w.length),l.write_shift(4,8&s?0:m.content.length),l.write_shift(2,y.length),l.write_shift(2,0),f+=l.length,n.push(l),f+=y.length,n.push(y),f+=w.length,n.push(w),8&s&&((l=Xr(12)).write_shift(-4,g[h]),l.write_shift(4,w.length),l.write_shift(4,m.content.length),f+=l.l,n.push(l)),(l=Xr(46)).write_shift(4,33639248),l.write_shift(2,0),l.write_shift(2,20),l.write_shift(2,s),l.write_shift(2,o),l.write_shift(4,0),l.write_shift(-4,g[h]),l.write_shift(4,w.length),l.write_shift(4,m.content.length),l.write_shift(2,y.length),l.write_shift(2,0),l.write_shift(2,0),l.write_shift(2,0),l.write_shift(2,0),l.write_shift(4,0),l.write_shift(4,b),v+=l.l,i.push(l),v+=y.length,i.push(y),++h}return l=Xr(22),l.write_shift(4,101010256),l.write_shift(2,0),l.write_shift(2,0),l.write_shift(2,h),l.write_shift(2,h),l.write_shift(4,v),l.write_shift(4,f),l.write_shift(2,0),re([re(n),re(i),l])}(e,r);var n=function(e){for(var t=0,r=0,n=0;n<e.FileIndex.length;++n){var a=e.FileIndex[n];if(a.content){var i=a.content.length;i>0&&(i<4096?t+=i+63>>6:r+=i+511>>9)}}for(var l=e.FullPaths.length+3>>2,o=t+127>>7,s=(t+7>>3)+r+l+o,c=s+127>>7,u=c<=109?0:Math.ceil((c-109)/127);s+c+u+127>>7>c;)u=++c<=109?0:Math.ceil((c-109)/127);var f=[1,u,c,o,l,r,t,0];return e.FileIndex[0].size=t<<6,f[7]=(e.FileIndex[0].start=f[0]+f[1]+f[2]+f[3]+f[4]+f[5])+(f[6]+7>>3),f}(e),i=Xr(n[7]<<9),l=0,o=0;for(l=0;l<8;++l)i.write_shift(1,y[l]);for(l=0;l<8;++l)i.write_shift(2,0);for(i.write_shift(2,62),i.write_shift(2,3),i.write_shift(2,65534),i.write_shift(2,9),i.write_shift(2,6),l=0;l<3;++l)i.write_shift(2,0);for(i.write_shift(4,0),i.write_shift(4,n[2]),i.write_shift(4,n[0]+n[1]+n[2]+n[3]-1),i.write_shift(4,0),i.write_shift(4,4096),i.write_shift(4,n[3]?n[0]+n[1]+n[2]-1:v),i.write_shift(4,n[3]),i.write_shift(-4,n[1]?n[0]-1:v),i.write_shift(4,n[1]),l=0;l<109;++l)i.write_shift(-4,l<n[2]?n[1]+l:-1);if(n[1])for(o=0;o<n[1];++o){for(;l<236+127*o;++l)i.write_shift(-4,l<n[2]?n[1]+l:-1);i.write_shift(-4,o===n[1]-1?v:o+1)}var s=function(e){for(o+=e;l<o-1;++l)i.write_shift(-4,l+1);e&&(++l,i.write_shift(-4,v))};for(o=l=0,o+=n[1];l<o;++l)i.write_shift(-4,T.DIFSECT);for(o+=n[2];l<o;++l)i.write_shift(-4,T.FATSECT);s(n[3]),s(n[4]);for(var c=0,u=0,f=e.FileIndex[0];c<e.FileIndex.length;++c)(f=e.FileIndex[c]).content&&((u=f.content.length)<4096||(f.start=o,s(u+511>>9)));for(s(n[6]+7>>3);511&i.l;)i.write_shift(-4,T.ENDOFCHAIN);for(o=l=0,c=0;c<e.FileIndex.length;++c)(f=e.FileIndex[c]).content&&(!(u=f.content.length)||u>=4096||(f.start=o,s(u+63>>6)));for(;511&i.l;)i.write_shift(-4,T.ENDOFCHAIN);for(l=0;l<n[4]<<2;++l){var h=e.FullPaths[l];if(h&&0!==h.length){f=e.FileIndex[l],0===l&&(f.start=f.size?f.start-1:v);var p=0===l&&r.root||f.name;if(u=2*(p.length+1),i.write_shift(64,p,"utf16le"),i.write_shift(2,u),i.write_shift(1,f.type),i.write_shift(1,f.color),i.write_shift(-4,f.L),i.write_shift(-4,f.R),i.write_shift(-4,f.C),f.clsid)i.write_shift(16,f.clsid,"hex");else for(c=0;c<4;++c)i.write_shift(4,0);i.write_shift(4,f.state||0),i.write_shift(4,0),i.write_shift(4,0),i.write_shift(4,0),i.write_shift(4,0),i.write_shift(4,f.start),i.write_shift(4,f.size),i.write_shift(4,0)}else{for(c=0;c<17;++c)i.write_shift(4,0);for(c=0;c<3;++c)i.write_shift(4,-1);for(c=0;c<12;++c)i.write_shift(4,0)}}for(l=1;l<e.FileIndex.length;++l)if((f=e.FileIndex[l]).size>=4096)if(i.l=f.start+1<<9,X&&Buffer.isBuffer(f.content))f.content.copy(i,i.l,0,f.size),i.l+=f.size+511&-512;else{for(c=0;c<f.size;++c)i.write_shift(1,f.content[c]);for(;511&c;++c)i.write_shift(1,0)}for(l=1;l<e.FileIndex.length;++l)if((f=e.FileIndex[l]).size>0&&f.size<4096)if(X&&Buffer.isBuffer(f.content))f.content.copy(i,i.l,0,f.size),i.l+=f.size+63&-64;else{for(c=0;c<f.size;++c)i.write_shift(1,f.content[c]);for(;63&c;++c)i.write_shift(1,0)}if(X)i.l=i.length;else for(;i.l<i.length;)i.write_shift(1,0);return i}t.version="1.2.1";var m,g=64,v=-2,b="d0cf11e0a1b11ae1",y=[208,207,17,224,161,177,26,225],w="00000000000000000000000000000000",T={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:v,FREESECT:-1,HEADER_SIGNATURE:b,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:w,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function E(e){for(var t=new Array(e.length),r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}function S(e){return m?m.deflateRawSync(e):te(e)}var x=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],k=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],_=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577];function C(e){var t=139536&(e<<1|e<<11)|558144&(e<<5|e<<15);return 255&(t>>16|t>>8|t)}for(var A="undefined"!==typeof Uint8Array,N=A?new Uint8Array(256):[],O=0;O<256;++O)N[O]=C(O);function R(e,t){var r=N[255&e];return t<=8?r>>>8-t:(r=r<<8|N[e>>8&255],t<=16?r>>>16-t:(r=r<<8|N[e>>16&255])>>>24-t)}function P(e,t){var r=7&t,n=t>>>3;return(e[n]|(r<=6?0:e[n+1]<<8))>>>r&3}function I(e,t){var r=7&t,n=t>>>3;return(e[n]|(r<=5?0:e[n+1]<<8))>>>r&7}function D(e,t){var r=7&t,n=t>>>3;return(e[n]|(r<=3?0:e[n+1]<<8))>>>r&31}function L(e,t){var r=7&t,n=t>>>3;return(e[n]|(r<=1?0:e[n+1]<<8))>>>r&127}function M(e,t,r){var n=7&t,a=t>>>3,i=(1<<r)-1,l=e[a]>>>n;return r<8-n?l&i:(l|=e[a+1]<<8-n,r<16-n?l&i:(l|=e[a+2]<<16-n,r<24-n?l&i:(l|=e[a+3]<<24-n)&i))}function F(e,t,r){var n=7&t,a=t>>>3;return n<=5?e[a]|=(7&r)<<n:(e[a]|=r<<n&255,e[a+1]=(7&r)>>8-n),t+3}function j(e,t,r){return r=(1&r)<<(7&t),e[t>>>3]|=r,t+1}function U(e,t,r){var n=t>>>3;return r<<=7&t,e[n]|=255&r,r>>>=8,e[n+1]=r,t+8}function B(e,t,r){var n=t>>>3;return r<<=7&t,e[n]|=255&r,r>>>=8,e[n+1]=255&r,e[n+2]=r>>>8,t+16}function z(e,t){var r=e.length,n=2*r>t?2*r:t+5,a=0;if(r>=t)return e;if(X){var i=J(n);if(e.copy)e.copy(i);else for(;a<e.length;++a)i[a]=e[a];return i}if(A){var l=new Uint8Array(n);if(l.set)l.set(e);else for(;a<r;++a)l[a]=e[a];return l}return e.length=n,e}function H(e){for(var t=new Array(e),r=0;r<e;++r)t[r]=0;return t}function W(e,t,r){var n=1,a=0,i=0,l=0,o=0,s=e.length,c=A?new Uint16Array(32):H(32);for(i=0;i<32;++i)c[i]=0;for(i=s;i<r;++i)e[i]=0;s=e.length;var u=A?new Uint16Array(s):H(s);for(i=0;i<s;++i)c[a=e[i]]++,n<a&&(n=a),u[i]=0;for(c[0]=0,i=1;i<=n;++i)c[i+16]=o=o+c[i-1]<<1;for(i=0;i<s;++i)0!=(o=e[i])&&(u[i]=c[o+16]++);var f=0;for(i=0;i<s;++i)if(0!=(f=e[i]))for(o=R(u[i],n)>>n-f,l=(1<<n+4-f)-1;l>=0;--l)t[o|l<<f]=15&f|i<<4;return n}var V=A?new Uint16Array(512):H(512),$=A?new Uint16Array(32):H(32);if(!A){for(var G=0;G<512;++G)V[G]=0;for(G=0;G<32;++G)$[G]=0}!function(){for(var e=[],t=0;t<32;t++)e.push(5);W(e,$,32);var r=[];for(t=0;t<=143;t++)r.push(8);for(;t<=255;t++)r.push(9);for(;t<=279;t++)r.push(7);for(;t<=287;t++)r.push(8);W(r,V,288)}();var ee=function(){for(var e=A?new Uint8Array(32768):[],t=0,r=0;t<_.length-1;++t)for(;r<_[t+1];++r)e[r]=t;for(;r<32768;++r)e[r]=29;var n=A?new Uint8Array(259):[];for(t=0,r=0;t<k.length-1;++t)for(;r<k[t+1];++r)n[r]=t;return function(t,r){return t.length<8?function(e,t){for(var r=0;r<e.length;){var n=Math.min(65535,e.length-r),a=r+n==e.length;for(t.write_shift(1,+a),t.write_shift(2,n),t.write_shift(2,65535&~n);n-- >0;)t[t.l++]=e[r++]}return t.l}(t,r):function(t,r){for(var a=0,i=0,l=A?new Uint16Array(32768):[];i<t.length;){var o=Math.min(65535,t.length-i);if(o<10){for(7&(a=F(r,a,+!(i+o!=t.length)))&&(a+=8-(7&a)),r.l=a/8|0,r.write_shift(2,o),r.write_shift(2,65535&~o);o-- >0;)r[r.l++]=t[i++];a=8*r.l}else{a=F(r,a,+!(i+o!=t.length)+2);for(var s=0;o-- >0;){var c=t[i],u=-1,f=0;if((u=l[s=32767&(s<<5^c)])&&((u|=-32768&i)>i&&(u-=32768),u<i))for(;t[u+f]==t[i+f]&&f<250;)++f;if(f>2){(c=n[f])<=22?a=U(r,a,N[c+1]>>1)-1:(U(r,a,3),U(r,a+=5,N[c-23]>>5),a+=3);var h=c<8?0:c-4>>2;h>0&&(B(r,a,f-k[c]),a+=h),c=e[i-u],a=U(r,a,N[c]>>3),a-=3;var d=c<4?0:c-2>>1;d>0&&(B(r,a,i-u-_[c]),a+=d);for(var p=0;p<f;++p)l[s]=32767&i,s=32767&(s<<5^t[i]),++i;o-=f-1}else c<=143?c+=48:a=j(r,a,1),a=U(r,a,N[c]),l[s]=32767&i,++i}a=U(r,a,0)-1}}return r.l=(a+7)/8|0,r.l}(t,r)}}();function te(e){var t=Xr(50+Math.floor(1.1*e.length)),r=ee(e,t);return t.slice(0,r)}var ie=A?new Uint16Array(32768):H(32768),le=A?new Uint16Array(32768):H(32768),oe=A?new Uint16Array(128):H(128),se=1,ce=1;function ue(e,t){var r=D(e,t)+257,n=D(e,t+=5)+1,a=function(e,t){var r=7&t,n=t>>>3;return(e[n]|(r<=4?0:e[n+1]<<8))>>>r&15}(e,t+=5)+4;t+=4;for(var i=0,l=A?new Uint8Array(19):H(19),o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],s=1,c=A?new Uint8Array(8):H(8),u=A?new Uint8Array(8):H(8),f=l.length,h=0;h<a;++h)l[x[h]]=i=I(e,t),s<i&&(s=i),c[i]++,t+=3;var d=0;for(c[0]=0,h=1;h<=s;++h)u[h]=d=d+c[h-1]<<1;for(h=0;h<f;++h)0!=(d=l[h])&&(o[h]=u[d]++);var p=0;for(h=0;h<f;++h)if(0!=(p=l[h])){d=N[o[h]]>>8-p;for(var m=(1<<7-p)-1;m>=0;--m)oe[d|m<<p]=7&p|h<<3}var g=[];for(s=1;g.length<r+n;)switch(t+=7&(d=oe[L(e,t)]),d>>>=3){case 16:for(i=3+P(e,t),t+=2,d=g[g.length-1];i-- >0;)g.push(d);break;case 17:for(i=3+I(e,t),t+=3;i-- >0;)g.push(0);break;case 18:for(i=11+L(e,t),t+=7;i-- >0;)g.push(0);break;default:g.push(d),s<d&&(s=d)}var v=g.slice(0,r),b=g.slice(r);for(h=r;h<286;++h)v[h]=0;for(h=n;h<30;++h)b[h]=0;return se=W(v,ie,286),ce=W(b,le,30),t}function fe(e,t){var r=function(e,t){if(3==e[0]&&!(3&e[1]))return[q(t),2];for(var r=0,n=0,a=J(t||1<<18),i=0,l=a.length>>>0,o=0,s=0;0==(1&n);)if(n=I(e,r),r+=3,n>>>1!=0)for(n>>1==1?(o=9,s=5):(r=ue(e,r),o=se,s=ce);;){!t&&l<i+32767&&(l=(a=z(a,i+32767)).length);var c=M(e,r,o),u=n>>>1==1?V[c]:ie[c];if(r+=15&u,0===((u>>>=4)>>>8&255))a[i++]=u;else{if(256==u)break;var f=(u-=257)<8?0:u-4>>2;f>5&&(f=0);var h=i+k[u];f>0&&(h+=M(e,r,f),r+=f),c=M(e,r,s),r+=15&(u=n>>>1==1?$[c]:le[c]);var d=(u>>>=4)<4?0:u-2>>1,p=_[u];for(d>0&&(p+=M(e,r,d),r+=d),!t&&l<h&&(l=(a=z(a,h+100)).length);i<h;)a[i]=a[i-p],++i}}else{7&r&&(r+=8-(7&r));var m=e[r>>>3]|e[1+(r>>>3)]<<8;if(r+=32,m>0)for(!t&&l<i+m&&(l=(a=z(a,i+m)).length);m-- >0;)a[i++]=e[r>>>3],r+=8}return t?[a,r+7>>>3]:[a.slice(0,i),r+7>>>3]}(e.slice(e.l||0),t);return e.l+=r[1],r[0]}function he(e,t){if(!e)throw new Error(t);"undefined"!==typeof console&&console.error(t)}function de(e,t){var r=e;Kr(r,0);var n={FileIndex:[],FullPaths:[]};h(n,{root:t.root});for(var a=r.length-4;(80!=r[a]||75!=r[a+1]||5!=r[a+2]||6!=r[a+3])&&a>=0;)--a;r.l=a+4,r.l+=4;var l=r.read_shift(2);r.l+=6;var o=r.read_shift(4);for(r.l=o,a=0;a<l;++a){r.l+=20;var s=r.read_shift(4),c=r.read_shift(4),u=r.read_shift(2),f=r.read_shift(2),d=r.read_shift(2);r.l+=8;var p=r.read_shift(4),m=i(r.slice(r.l+u,r.l+u+f));r.l+=u+f+d;var g=r.l;r.l=p+4,pe(r,s,c,n,m),r.l=g}return n}function pe(e,t,r,n,a){e.l+=2;var l=e.read_shift(2),o=e.read_shift(2),s=function(e){var t=65535&e.read_shift(2),r=65535&e.read_shift(2),n=new Date,a=31&r,i=15&(r>>>=5);r>>>=4,n.setMilliseconds(0),n.setFullYear(r+1980),n.setMonth(i-1),n.setDate(a);var l=31&t,o=63&(t>>>=5);return t>>>=6,n.setHours(t),n.setMinutes(o),n.setSeconds(l<<1),n}(e);if(8257&l)throw new Error("Unsupported ZIP encryption");e.read_shift(4);for(var c=e.read_shift(4),u=e.read_shift(4),f=e.read_shift(2),h=e.read_shift(2),d="",p=0;p<f;++p)d+=String.fromCharCode(e[e.l++]);if(h){var g=i(e.slice(e.l,e.l+h));(g[21589]||{}).mt&&(s=g[21589].mt),((a||{})[21589]||{}).mt&&(s=a[21589].mt)}e.l+=h;var v=e.slice(e.l,e.l+c);switch(o){case 8:v=function(e,t){if(!m)return fe(e,t);var r=new(0,m.InflateRaw),n=r._processChunk(e.slice(e.l),r._finishFlushFlag);return e.l+=r.bytesRead,n}(e,u);break;case 0:break;default:throw new Error("Unsupported ZIP Compression method "+o)}var b=!1;8&l&&(134695760==e.read_shift(4)&&(e.read_shift(4),b=!0),c=e.read_shift(4),u=e.read_shift(4)),c!=t&&he(b,"Bad compressed size: "+t+" != "+c),u!=r&&he(b,"Bad uncompressed size: "+r+" != "+u),we(n,d,v,{unsafe:!0,mt:s})}var me={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function ge(e,t){if(e.ctype)return e.ctype;var r=e.name||"",n=r.match(/\.([^\.]+)$/);return n&&me[n[1]]||t&&(n=(r=t).match(/[\.\\]([^\.\\])+$/))&&me[n[1]]?me[n[1]]:"application/octet-stream"}function ve(e){for(var t=K(e),r=[],n=0;n<t.length;n+=76)r.push(t.slice(n,n+76));return r.join("\r\n")+"\r\n"}function be(e){var t=e.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,(function(e){var t=e.charCodeAt(0).toString(16).toUpperCase();return"="+(1==t.length?"0"+t:t)}));"\n"==(t=t.replace(/ $/gm,"=20").replace(/\t$/gm,"=09")).charAt(0)&&(t="=0D"+t.slice(1));for(var r=[],n=(t=t.replace(/\r(?!\n)/gm,"=0D").replace(/\n\n/gm,"\n=0A").replace(/([^\r\n])\n/gm,"$1=0A")).split("\r\n"),a=0;a<n.length;++a){var i=n[a];if(0!=i.length)for(var l=0;l<i.length;){var o=76,s=i.slice(l,l+o);"="==s.charAt(o-1)?o--:"="==s.charAt(o-2)?o-=2:"="==s.charAt(o-3)&&(o-=3),s=i.slice(l,l+o),(l+=o)<i.length&&(s+="="),r.push(s)}else r.push("")}return r.join("\r\n")}function ye(e,t,r){for(var n,a="",i="",l="",o=0;o<10;++o){var s=t[o];if(!s||s.match(/^\s*$/))break;var c=s.match(/^(.*?):\s*([^\s].*)$/);if(c)switch(c[1].toLowerCase()){case"content-location":a=c[2].trim();break;case"content-type":l=c[2].trim();break;case"content-transfer-encoding":i=c[2].trim()}}switch(++o,i.toLowerCase()){case"base64":n=Z(Y(t.slice(o).join("")));break;case"quoted-printable":n=function(e){for(var t=[],r=0;r<e.length;++r){for(var n=e[r];r<=e.length&&"="==n.charAt(n.length-1);)n=n.slice(0,n.length-1)+e[++r];t.push(n)}for(var a=0;a<t.length;++a)t[a]=t[a].replace(/[=][0-9A-Fa-f]{2}/g,(function(e){return String.fromCharCode(parseInt(e.slice(1),16))}));return Z(t.join("\r\n"))}(t.slice(o));break;default:throw new Error("Unsupported Content-Transfer-Encoding "+i)}var u=we(e,a.slice(r.length),n,{unsafe:!0});l&&(u.ctype=l)}function we(e,t,r,a){var i=a&&a.unsafe;i||h(e);var l=!i&&Je.find(e,t);if(!l){var o=e.FullPaths[0];t.slice(0,o.length)==o?o=t:("/"!=o.slice(-1)&&(o+="/"),o=(o+t).replace("//","/")),l={name:n(t),type:2},e.FileIndex.push(l),e.FullPaths.push(o),i||Je.utils.cfb_gc(e)}return l.content=r,l.size=r?r.length:0,a&&(a.CLSID&&(l.clsid=a.CLSID),a.mt&&(l.mt=a.mt),a.ct&&(l.ct=a.ct)),l}return t.find=function(e,t){var r=e.FullPaths.map((function(e){return e.toUpperCase()})),n=r.map((function(e){var t=e.split("/");return t[t.length-("/"==e.slice(-1)?2:1)]})),a=!1;47===t.charCodeAt(0)?(a=!0,t=r[0].slice(0,-1)+t):a=-1!==t.indexOf("/");var i=t.toUpperCase(),l=!0===a?r.indexOf(i):n.indexOf(i);if(-1!==l)return e.FileIndex[l];var o=!i.match(ae);for(i=i.replace(ne,""),o&&(i=i.replace(ae,"!")),l=0;l<r.length;++l){if((o?r[l].replace(ae,"!"):r[l]).replace(ne,"")==i)return e.FileIndex[l];if((o?n[l].replace(ae,"!"):n[l]).replace(ne,"")==i)return e.FileIndex[l]}return null},t.read=function(t,r){var n=r&&r.type;switch(n||X&&Buffer.isBuffer(t)&&(n="buffer"),n||"base64"){case"file":return function(t,r){return l(),o(e.readFileSync(t),r)}(t,r);case"base64":return o(Z(Y(t)),r);case"binary":return o(Z(t),r)}return o(t,r)},t.parse=o,t.write=function(t,r){var n=p(t,r);switch(r&&r.type||"buffer"){case"file":return l(),e.writeFileSync(r.filename,n),n;case"binary":return"string"==typeof n?n:E(n);case"base64":return K("string"==typeof n?n:E(n));case"buffer":if(X)return Buffer.isBuffer(n)?n:Q(n);case"array":return"string"==typeof n?Z(n):n}return n},t.writeFile=function(t,r,n){l();var a=p(t,n);e.writeFileSync(r,a)},t.utils={cfb_new:function(e){var t={};return h(t,e),t},cfb_add:we,cfb_del:function(e,t){h(e);var r=Je.find(e,t);if(r)for(var n=0;n<e.FileIndex.length;++n)if(e.FileIndex[n]==r)return e.FileIndex.splice(n,1),e.FullPaths.splice(n,1),!0;return!1},cfb_mov:function(e,t,r){h(e);var a=Je.find(e,t);if(a)for(var i=0;i<e.FileIndex.length;++i)if(e.FileIndex[i]==a)return e.FileIndex[i].name=n(r),e.FullPaths[i]=r,!0;return!1},cfb_gc:function(e){d(e,!0)},ReadShift:zr,CheckField:Gr,prep_blob:Kr,bconcat:re,use_zlib:function(e){try{var t=new(0,e.InflateRaw);if(t._processChunk(new Uint8Array([3,0]),t._finishFlushFlag),!t.bytesRead)throw new Error("zlib does not expose bytesRead");m=e}catch(r){console.error("cannot use native zlib: "+(r.message||r))}},_deflateRaw:te,_inflateRaw:fe,consts:T},t}();let Ze;function et(e){return"string"===typeof e?ee(e):Array.isArray(e)?function(e){if("undefined"===typeof Uint8Array)throw new Error("Unsupported");return new Uint8Array(e)}(e):e}function tt(e,t,r){if("undefined"!==typeof Ze&&Ze.writeFileSync)return r?Ze.writeFileSync(e,t,r):Ze.writeFileSync(e,t);if("undefined"!==typeof Deno){if(r&&"string"==typeof t)switch(r){case"utf8":t=new TextEncoder(r).encode(t);break;case"binary":t=ee(t);break;default:throw new Error("Unsupported encoding "+r)}return Deno.writeFileSync(e,t)}var n="utf8"==r?Ut(t):t;if("undefined"!==typeof IE_SaveFile)return IE_SaveFile(n,e);if("undefined"!==typeof Blob){var a=new Blob([et(n)],{type:"application/octet-stream"});if("undefined"!==typeof navigator&&navigator.msSaveBlob)return navigator.msSaveBlob(a,e);if("undefined"!==typeof saveAs)return saveAs(a,e);if("undefined"!==typeof URL&&"undefined"!==typeof document&&document.createElement&&URL.createObjectURL){var i=URL.createObjectURL(a);if("object"===typeof chrome&&"function"==typeof(chrome.downloads||{}).download)return URL.revokeObjectURL&&"undefined"!==typeof setTimeout&&setTimeout((function(){URL.revokeObjectURL(i)}),6e4),chrome.downloads.download({url:i,filename:e,saveAs:!0});var l=document.createElement("a");if(null!=l.download)return l.download=e,l.href=i,document.body.appendChild(l),l.click(),document.body.removeChild(l),URL.revokeObjectURL&&"undefined"!==typeof setTimeout&&setTimeout((function(){URL.revokeObjectURL(i)}),6e4),i}}if("undefined"!==typeof $&&"undefined"!==typeof File&&"undefined"!==typeof Folder)try{var o=File(e);return o.open("w"),o.encoding="binary",Array.isArray(t)&&(t=te(t)),o.write(t),o.close(),t}catch(s){if(!s.message||!s.message.match(/onstruct/))throw s}throw new Error("cannot save file "+e)}function rt(e){for(var t=Object.keys(e),r=[],n=0;n<t.length;++n)Object.prototype.hasOwnProperty.call(e,t[n])&&r.push(t[n]);return r}function nt(e,t){for(var r=[],n=rt(e),a=0;a!==n.length;++a)null==r[e[n[a]][t]]&&(r[e[n[a]][t]]=n[a]);return r}function at(e){for(var t=[],r=rt(e),n=0;n!==r.length;++n)t[e[r[n]]]=r[n];return t}function it(e){for(var t=[],r=rt(e),n=0;n!==r.length;++n)t[e[r[n]]]=parseInt(r[n],10);return t}var lt=new Date(1899,11,30,0,0,0);function ot(e,t){var r=e.getTime();return t&&(r-=1263168e5),(r-(lt.getTime()+6e4*(e.getTimezoneOffset()-lt.getTimezoneOffset())))/864e5}var st=new Date,ct=lt.getTime()+6e4*(st.getTimezoneOffset()-lt.getTimezoneOffset()),ut=st.getTimezoneOffset();function ft(e){var t=new Date;return t.setTime(24*e*60*60*1e3+ct),t.getTimezoneOffset()!==ut&&t.setTime(t.getTime()+6e4*(t.getTimezoneOffset()-ut)),t}var ht=new Date("2017-02-19T19:06:09.000Z"),dt=isNaN(ht.getFullYear())?new Date("2/19/17"):ht,pt=2017==dt.getFullYear();function mt(e,t){var r=new Date(e);if(pt)return t>0?r.setTime(r.getTime()+60*r.getTimezoneOffset()*1e3):t<0&&r.setTime(r.getTime()-60*r.getTimezoneOffset()*1e3),r;if(e instanceof Date)return e;if(1917==dt.getFullYear()&&!isNaN(r.getFullYear())){var n=r.getFullYear();return e.indexOf(""+n)>-1||r.setFullYear(r.getFullYear()+100),r}var a=e.match(/\d+/g)||["2017","2","19","0","0","0"],i=new Date(+a[0],+a[1]-1,+a[2],+a[3]||0,+a[4]||0,+a[5]||0);return e.indexOf("Z")>-1&&(i=new Date(i.getTime()-60*i.getTimezoneOffset()*1e3)),i}function gt(e,t){if(X&&Buffer.isBuffer(e)){if(t){if(255==e[0]&&254==e[1])return Ut(e.slice(2).toString("utf16le"));if(254==e[1]&&255==e[2])return Ut(z(e.slice(2).toString("binary")))}return e.toString("binary")}if("undefined"!==typeof TextDecoder)try{if(t){if(255==e[0]&&254==e[1])return Ut(new TextDecoder("utf-16le").decode(e.slice(2)));if(254==e[0]&&255==e[1])return Ut(new TextDecoder("utf-16be").decode(e.slice(2)))}var r={"\u20ac":"\x80","\u201a":"\x82","\u0192":"\x83","\u201e":"\x84","\u2026":"\x85","\u2020":"\x86","\u2021":"\x87","\u02c6":"\x88","\u2030":"\x89","\u0160":"\x8a","\u2039":"\x8b","\u0152":"\x8c","\u017d":"\x8e","\u2018":"\x91","\u2019":"\x92","\u201c":"\x93","\u201d":"\x94","\u2022":"\x95","\u2013":"\x96","\u2014":"\x97","\u02dc":"\x98","\u2122":"\x99","\u0161":"\x9a","\u203a":"\x9b","\u0153":"\x9c","\u017e":"\x9e","\u0178":"\x9f"};return Array.isArray(e)&&(e=new Uint8Array(e)),new TextDecoder("latin1").decode(e).replace(/[\u20ac\u201a\u0192\u201e\u2026\u2020\u2021\u02c6\u2030\u0160\u2039\u0152\u017d\u2018\u2019\u201c\u201d\u2022\u2013\u2014\u02dc\u2122\u0161\u203a\u0153\u017e\u0178]/g,(function(e){return r[e]||e}))}catch(i){}for(var n=[],a=0;a!=e.length;++a)n.push(String.fromCharCode(e[a]));return n.join("")}function vt(e){if("undefined"!=typeof JSON&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if("object"!=typeof e||null==e)return e;if(e instanceof Date)return new Date(e.getTime());var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=vt(e[r]));return t}function bt(e,t){for(var r="";r.length<t;)r+=e;return r}function yt(e){var t=Number(e);if(!isNaN(t))return isFinite(t)?t:NaN;if(!/\d/.test(e))return t;var r=1,n=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,(function(){return r*=100,""}));return isNaN(t=Number(n))?(n=n.replace(/[(](.*)[)]/,(function(e,t){return r=-r,t})),isNaN(t=Number(n))?t:t/r):t/r}var wt=["january","february","march","april","may","june","july","august","september","october","november","december"];function Tt(e){var t=new Date(e),r=new Date(NaN),n=t.getYear(),a=t.getMonth(),i=t.getDate();if(isNaN(i))return r;var l=e.toLowerCase();if(l.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if((l=l.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,"")).length>3&&-1==wt.indexOf(l))return r}else if(l.match(/[a-z]/))return r;return n<0||n>8099?r:(a>0||i>1)&&101!=n?t:e.match(/[^-0-9:,\/\\]/)?r:t}function Et(e,t,r){if(e.FullPaths){var n;if("string"==typeof r)return n=X?Q(r):function(e){for(var t=[],r=0,n=e.length+250,a=q(e.length+255),i=0;i<e.length;++i){var l=e.charCodeAt(i);if(l<128)a[r++]=l;else if(l<2048)a[r++]=192|l>>6&31,a[r++]=128|63&l;else if(l>=55296&&l<57344){l=64+(1023&l);var o=1023&e.charCodeAt(++i);a[r++]=240|l>>8&7,a[r++]=128|l>>2&63,a[r++]=128|o>>6&15|(3&l)<<4,a[r++]=128|63&o}else a[r++]=224|l>>12&15,a[r++]=128|l>>6&63,a[r++]=128|63&l;r>n&&(t.push(a.slice(0,r)),r=0,a=q(65535),n=65530)}return t.push(a.slice(0,r)),re(t)}(r),Je.utils.cfb_add(e,t,n);Je.utils.cfb_add(e,t,r)}else e.file(t,r)}function St(){return Je.utils.cfb_new()}var xt='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r\n';var kt={"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"},_t=at(kt),Ct=/[&<>'"]/g,At=/[\u0000-\u0008\u000b-\u001f]/g;function Nt(e){return(e+"").replace(Ct,(function(e){return _t[e]})).replace(At,(function(e){return"_x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+"_"}))}function Ot(e){return Nt(e).replace(/ /g,"_x0020_")}var Rt=/[\u0000-\u001f]/g;function Pt(e){return(e+"").replace(Ct,(function(e){return _t[e]})).replace(/\n/g,"<br/>").replace(Rt,(function(e){return"&#x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+";"}))}function It(e){switch(e){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}function Dt(e){for(var t="",r=0,n=0,a=0,i=0,l=0,o=0;r<e.length;)(n=e.charCodeAt(r++))<128?t+=String.fromCharCode(n):(a=e.charCodeAt(r++),n>191&&n<224?(l=(31&n)<<6,l|=63&a,t+=String.fromCharCode(l)):(i=e.charCodeAt(r++),n<240?t+=String.fromCharCode((15&n)<<12|(63&a)<<6|63&i):(o=((7&n)<<18|(63&a)<<12|(63&i)<<6|63&(l=e.charCodeAt(r++)))-65536,t+=String.fromCharCode(55296+(o>>>10&1023)),t+=String.fromCharCode(56320+(1023&o)))));return t}function Lt(e){var t,r,n,a=q(2*e.length),i=1,l=0,o=0;for(r=0;r<e.length;r+=i)i=1,(n=e.charCodeAt(r))<128?t=n:n<224?(t=64*(31&n)+(63&e.charCodeAt(r+1)),i=2):n<240?(t=4096*(15&n)+64*(63&e.charCodeAt(r+1))+(63&e.charCodeAt(r+2)),i=3):(i=4,t=262144*(7&n)+4096*(63&e.charCodeAt(r+1))+64*(63&e.charCodeAt(r+2))+(63&e.charCodeAt(r+3)),o=55296+((t-=65536)>>>10&1023),t=56320+(1023&t)),0!==o&&(a[l++]=255&o,a[l++]=o>>>8,o=0),a[l++]=t%256,a[l++]=t>>>8;return a.slice(0,l).toString("ucs2")}function Mt(e){return Q(e,"binary").toString("utf8")}var Ft="foo bar baz\xe2\x98\x83\xf0\x9f\x8d\xa3",jt=X&&(Mt(Ft)==Dt(Ft)&&Mt||Lt(Ft)==Dt(Ft)&&Lt)||Dt,Ut=X?function(e){return Q(e,"utf8").toString("binary")}:function(e){for(var t=[],r=0,n=0,a=0;r<e.length;)switch(n=e.charCodeAt(r++),!0){case n<128:t.push(String.fromCharCode(n));break;case n<2048:t.push(String.fromCharCode(192+(n>>6))),t.push(String.fromCharCode(128+(63&n)));break;case n>=55296&&n<57344:n-=55296,a=e.charCodeAt(r++)-56320+(n<<10),t.push(String.fromCharCode(240+(a>>18&7))),t.push(String.fromCharCode(144+(a>>12&63))),t.push(String.fromCharCode(128+(a>>6&63))),t.push(String.fromCharCode(128+(63&a)));break;default:t.push(String.fromCharCode(224+(n>>12))),t.push(String.fromCharCode(128+(n>>6&63))),t.push(String.fromCharCode(128+(63&n)))}return t.join("")},Bt=function(){var e=[["nbsp"," "],["middot","\xb7"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map((function(e){return[new RegExp("&"+e[0]+";","ig"),e[1]]}));return function(t){for(var r=t.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/>\s+/g,">").replace(/\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,"\n").replace(/<[^>]*>/g,""),n=0;n<e.length;++n)r=r.replace(e[n][0],e[n][1]);return r}}();var zt=/(^\s|\s$|\n)/;function Ht(e,t){return"<"+e+(t.match(zt)?' xml:space="preserve"':"")+">"+t+"</"+e+">"}function Wt(e){return rt(e).map((function(t){return" "+t+'="'+e[t]+'"'})).join("")}function Vt(e,t,r){return"<"+e+(null!=r?Wt(r):"")+(null!=t?(t.match(zt)?' xml:space="preserve"':"")+">"+t+"</"+e:"/")+">"}function $t(e,t){try{return e.toISOString().replace(/\.\d*/,"")}catch(r){if(t)throw r}return""}var Gt="http://schemas.openxmlformats.org/package/2006/metadata/core-properties",Kt="http://schemas.openxmlformats.org/officeDocument/2006/custom-properties",Yt="http://schemas.openxmlformats.org/officeDocument/2006/extended-properties",Xt="http://schemas.openxmlformats.org/package/2006/content-types",Qt="http://schemas.openxmlformats.org/package/2006/relationships",qt="http://schemas.microsoft.com/office/spreadsheetml/2018/threadedcomments",Jt="http://purl.org/dc/elements/1.1/",Zt="http://purl.org/dc/terms/",er="http://purl.org/dc/dcmitype/",tr="http://schemas.openxmlformats.org/officeDocument/2006/relationships",rr="http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes",nr="http://www.w3.org/2001/XMLSchema-instance",ar="http://www.w3.org/2001/XMLSchema",ir=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"],lr="urn:schemas-microsoft-com:office:office",or="urn:schemas-microsoft-com:office:excel",sr="urn:schemas-microsoft-com:office:spreadsheet",cr="uuid:C2F41010-65B3-11d1-A29F-00AA00C14882",ur="http://macVmlSchemaUri",fr="urn:schemas-microsoft-com:vml",hr="http://www.w3.org/TR/REC-html40";var dr=function(e){for(var t=[],r=0;r<e[0].length;++r)if(e[0][r])for(var n=0,a=e[0][r].length;n<a;n+=10240)t.push.apply(t,e[0][r].slice(n,n+10240));return t},pr=X?function(e){return e[0].length>0&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0].map((function(e){return Buffer.isBuffer(e)?e:Q(e)}))):dr(e)}:dr,mr=function(e,t,r){for(var n=[],a=t;a<r;a+=2)n.push(String.fromCharCode(Mr(e,a)));return n.join("").replace(ne,"")},gr=X?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf16le",t,r).replace(ne,""):mr(e,t,r)}:mr,vr=function(e,t,r){for(var n=[],a=t;a<t+r;++a)n.push(("0"+e[a].toString(16)).slice(-2));return n.join("")},br=X?function(e,t,r){return Buffer.isBuffer(e)?e.toString("hex",t,t+r):vr(e,t,r)}:vr,yr=function(e,t,r){for(var n=[],a=t;a<r;a++)n.push(String.fromCharCode(Lr(e,a)));return n.join("")},wr=X?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf8",t,r):yr(e,t,r)}:yr,Tr=function(e,t){var r=jr(e,t);return r>0?wr(e,t+4,t+4+r-1):""},Er=Tr,Sr=function(e,t){var r=jr(e,t);return r>0?wr(e,t+4,t+4+r-1):""},xr=Sr,kr=function(e,t){var r=2*jr(e,t);return r>0?wr(e,t+4,t+4+r-1):""},_r=kr,Cr=function(e,t){var r=jr(e,t);return r>0?gr(e,t+4,t+4+r):""},Ar=Cr,Nr=function(e,t){var r=jr(e,t);return r>0?wr(e,t+4,t+4+r):""},Or=Nr,Rr=function(e,t){return function(e,t){for(var r=1-2*(e[t+7]>>>7),n=((127&e[t+7])<<4)+(e[t+6]>>>4&15),a=15&e[t+6],i=5;i>=0;--i)a=256*a+e[t+i];return 2047==n?0==a?r*(1/0):NaN:(0==n?n=-1022:(n-=1023,a+=Math.pow(2,52)),r*Math.pow(2,n-52)*a)}(e,t)},Pr=Rr,Ir=function(e){return Array.isArray(e)||"undefined"!==typeof Uint8Array&&e instanceof Uint8Array};function Dr(){gr=function(e,t,r){return H.utils.decode(1200,e.slice(t,r)).replace(ne,"")},wr=function(e,t,r){return H.utils.decode(65001,e.slice(t,r))},Er=function(e,t){var r=jr(e,t);return r>0?H.utils.decode(L,e.slice(t+4,t+4+r-1)):""},xr=function(e,t){var r=jr(e,t);return r>0?H.utils.decode(D,e.slice(t+4,t+4+r-1)):""},_r=function(e,t){var r=2*jr(e,t);return r>0?H.utils.decode(1200,e.slice(t+4,t+4+r-1)):""},Ar=function(e,t){var r=jr(e,t);return r>0?H.utils.decode(1200,e.slice(t+4,t+4+r)):""},Or=function(e,t){var r=jr(e,t);return r>0?H.utils.decode(65001,e.slice(t+4,t+4+r)):""}}X&&(Er=function(e,t){if(!Buffer.isBuffer(e))return Tr(e,t);var r=e.readUInt32LE(t);return r>0?e.toString("utf8",t+4,t+4+r-1):""},xr=function(e,t){if(!Buffer.isBuffer(e))return Sr(e,t);var r=e.readUInt32LE(t);return r>0?e.toString("utf8",t+4,t+4+r-1):""},_r=function(e,t){if(!Buffer.isBuffer(e))return kr(e,t);var r=2*e.readUInt32LE(t);return e.toString("utf16le",t+4,t+4+r-1)},Ar=function(e,t){if(!Buffer.isBuffer(e))return Cr(e,t);var r=e.readUInt32LE(t);return e.toString("utf16le",t+4,t+4+r)},Or=function(e,t){if(!Buffer.isBuffer(e))return Nr(e,t);var r=e.readUInt32LE(t);return e.toString("utf8",t+4,t+4+r)},Pr=function(e,t){return Buffer.isBuffer(e)?e.readDoubleLE(t):Rr(e,t)},Ir=function(e){return Buffer.isBuffer(e)||Array.isArray(e)||"undefined"!==typeof Uint8Array&&e instanceof Uint8Array}),"undefined"!==typeof H&&Dr();var Lr=function(e,t){return e[t]},Mr=function(e,t){return 256*e[t+1]+e[t]},Fr=function(e,t){var r=256*e[t+1]+e[t];return r<32768?r:-1*(65535-r+1)},jr=function(e,t){return e[t+3]*(1<<24)+(e[t+2]<<16)+(e[t+1]<<8)+e[t]},Ur=function(e,t){return e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]},Br=function(e,t){return e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3]};function zr(e,t){var r,n,a,i,l,o,s="",c=[];switch(t){case"dbcs":if(o=this.l,X&&Buffer.isBuffer(this))s=this.slice(this.l,this.l+2*e).toString("utf16le");else for(l=0;l<e;++l)s+=String.fromCharCode(Mr(this,o)),o+=2;e*=2;break;case"utf8":s=wr(this,this.l,this.l+e);break;case"utf16le":e*=2,s=gr(this,this.l,this.l+e);break;case"wstr":if("undefined"===typeof H)return zr.call(this,e,"dbcs");s=H.utils.decode(D,this.slice(this.l,this.l+2*e)),e*=2;break;case"lpstr-ansi":s=Er(this,this.l),e=4+jr(this,this.l);break;case"lpstr-cp":s=xr(this,this.l),e=4+jr(this,this.l);break;case"lpwstr":s=_r(this,this.l),e=4+2*jr(this,this.l);break;case"lpp4":e=4+jr(this,this.l),s=Ar(this,this.l),2&e&&(e+=2);break;case"8lpp4":e=4+jr(this,this.l),s=Or(this,this.l),3&e&&(e+=4-(3&e));break;case"cstr":for(e=0,s="";0!==(a=Lr(this,this.l+e++));)c.push(W(a));s=c.join("");break;case"_wstr":for(e=0,s="";0!==(a=Mr(this,this.l+e));)c.push(W(a)),e+=2;e+=2,s=c.join("");break;case"dbcs-cont":for(s="",o=this.l,l=0;l<e;++l){if(this.lens&&-1!==this.lens.indexOf(o))return a=Lr(this,o),this.l=o+1,i=zr.call(this,e-l,a?"dbcs-cont":"sbcs-cont"),c.join("")+i;c.push(W(Mr(this,o))),o+=2}s=c.join(""),e*=2;break;case"cpstr":if("undefined"!==typeof H){s=H.utils.decode(D,this.slice(this.l,this.l+e));break}case"sbcs-cont":for(s="",o=this.l,l=0;l!=e;++l){if(this.lens&&-1!==this.lens.indexOf(o))return a=Lr(this,o),this.l=o+1,i=zr.call(this,e-l,a?"dbcs-cont":"sbcs-cont"),c.join("")+i;c.push(W(Lr(this,o))),o+=1}s=c.join("");break;default:switch(e){case 1:return r=Lr(this,this.l),this.l++,r;case 2:return r=("i"===t?Fr:Mr)(this,this.l),this.l+=2,r;case 4:case-4:return"i"===t||0===(128&this[this.l+3])?(r=(e>0?Ur:Br)(this,this.l),this.l+=4,r):(n=jr(this,this.l),this.l+=4,n);case 8:case-8:if("f"===t)return n=8==e?Pr(this,this.l):Pr([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,n;e=8;case 16:s=br(this,this.l,e)}}return this.l+=e,s}var Hr=function(e,t,r){e[r]=255&t,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24&255},Wr=function(e,t,r){e[r]=255&t,e[r+1]=t>>8&255,e[r+2]=t>>16&255,e[r+3]=t>>24&255},Vr=function(e,t,r){e[r]=255&t,e[r+1]=t>>>8&255};function $r(e,t,r){var n=0,a=0;if("dbcs"===r){for(a=0;a!=t.length;++a)Vr(this,t.charCodeAt(a),this.l+2*a);n=2*t.length}else if("sbcs"===r){if("undefined"!==typeof H&&874==L)for(a=0;a!=t.length;++a){var i=H.utils.encode(L,t.charAt(a));this[this.l+a]=i[0]}else for(t=t.replace(/[^\x00-\x7F]/g,"_"),a=0;a!=t.length;++a)this[this.l+a]=255&t.charCodeAt(a);n=t.length}else{if("hex"===r){for(;a<e;++a)this[this.l++]=parseInt(t.slice(2*a,2*a+2),16)||0;return this}if("utf16le"===r){var l=Math.min(this.l+e,this.length);for(a=0;a<Math.min(t.length,e);++a){var o=t.charCodeAt(a);this[this.l++]=255&o,this[this.l++]=o>>8}for(;this.l<l;)this[this.l++]=0;return this}switch(e){case 1:n=1,this[this.l]=255&t;break;case 2:n=2,this[this.l]=255&t,t>>>=8,this[this.l+1]=255&t;break;case 3:n=3,this[this.l]=255&t,t>>>=8,this[this.l+1]=255&t,t>>>=8,this[this.l+2]=255&t;break;case 4:n=4,Hr(this,t,this.l);break;case 8:if(n=8,"f"===r){!function(e,t,r){var n=(t<0||1/t==-1/0?1:0)<<7,a=0,i=0,l=n?-t:t;isFinite(l)?0==l?a=i=0:(a=Math.floor(Math.log(l)/Math.LN2),i=l*Math.pow(2,52-a),a<=-1023&&(!isFinite(i)||i<Math.pow(2,52))?a=-1022:(i-=Math.pow(2,52),a+=1023)):(a=2047,i=isNaN(t)?26985:0);for(var o=0;o<=5;++o,i/=256)e[r+o]=255&i;e[r+6]=(15&a)<<4|15&i,e[r+7]=a>>4|n}(this,t,this.l);break}case 16:break;case-4:n=4,Wr(this,t,this.l)}}return this.l+=n,this}function Gr(e,t){var r=br(this,this.l,e.length>>1);if(r!==e)throw new Error(t+"Expected "+e+" saw "+r);this.l+=e.length>>1}function Kr(e,t){e.l=t,e.read_shift=zr,e.chk=Gr,e.write_shift=$r}function Yr(e,t){e.l+=t}function Xr(e){var t=q(e);return Kr(t,0),t}function Qr(){var e=[],t=X?256:2048,r=function(e){var t=Xr(e);return Kr(t,0),t},n=r(t),a=function(){n&&(n.length>n.l&&((n=n.slice(0,n.l)).l=n.length),n.length>0&&e.push(n),n=null)},i=function(e){return n&&e<n.length-n.l?n:(a(),n=r(Math.max(e+1,t)))};return{next:i,push:function(e){a(),null==(n=e).l&&(n.l=n.length),i(t)},end:function(){return a(),re(e)},_bufs:e}}function qr(e,t,r,n){var a,i=+t;if(!isNaN(i)){n||(n=co[i].p||(r||[]).length||0),a=1+(i>=128?1:0)+1,n>=128&&++a,n>=16384&&++a,n>=2097152&&++a;var l=e.next(a);i<=127?l.write_shift(1,i):(l.write_shift(1,128+(127&i)),l.write_shift(1,i>>7));for(var o=0;4!=o;++o){if(!(n>=128)){l.write_shift(1,n);break}l.write_shift(1,128+(127&n)),n>>=7}n>0&&Ir(r)&&e.push(r)}}function Jr(e,t,r){var n=vt(e);if(t.s?(n.cRel&&(n.c+=t.s.c),n.rRel&&(n.r+=t.s.r)):(n.cRel&&(n.c+=t.c),n.rRel&&(n.r+=t.r)),!r||r.biff<12){for(;n.c>=256;)n.c-=256;for(;n.r>=65536;)n.r-=65536}return n}function Zr(e,t,r){var n=vt(e);return n.s=Jr(n.s,t.s,r),n.e=Jr(n.e,t.s,r),n}function en(e,t){if(e.cRel&&e.c<0)for(e=vt(e);e.c<0;)e.c+=t>8?16384:256;if(e.rRel&&e.r<0)for(e=vt(e);e.r<0;)e.r+=t>8?1048576:t>5?65536:16384;var r=sn(e);return e.cRel||null==e.cRel||(r=r.replace(/^([A-Z])/,"$$$1")),e.rRel||null==e.rRel||(r=function(e){return e.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")}(r)),r}function tn(e,t){return 0!=e.s.r||e.s.rRel||e.e.r!=(t.biff>=12?1048575:t.biff>=8?65536:16384)||e.e.rRel?0!=e.s.c||e.s.cRel||e.e.c!=(t.biff>=12?16383:255)||e.e.cRel?en(e.s,t.biff)+":"+en(e.e,t.biff):(e.s.rRel?"":"$")+nn(e.s.r)+":"+(e.e.rRel?"":"$")+nn(e.e.r):(e.s.cRel?"":"$")+ln(e.s.c)+":"+(e.e.cRel?"":"$")+ln(e.e.c)}function rn(e){return parseInt(e.replace(/\$(\d+)$/,"$1"),10)-1}function nn(e){return""+(e+1)}function an(e){for(var t=e.replace(/^\$([A-Z])/,"$1"),r=0,n=0;n!==t.length;++n)r=26*r+t.charCodeAt(n)-64;return r-1}function ln(e){if(e<0)throw new Error("invalid column "+e);var t="";for(++e;e;e=Math.floor((e-1)/26))t=String.fromCharCode((e-1)%26+65)+t;return t}function on(e){for(var t=0,r=0,n=0;n<e.length;++n){var a=e.charCodeAt(n);a>=48&&a<=57?t=10*t+(a-48):a>=65&&a<=90&&(r=26*r+(a-64))}return{c:r-1,r:t-1}}function sn(e){for(var t=e.c+1,r="";t;t=(t-1)/26|0)r=String.fromCharCode((t-1)%26+65)+r;return r+(e.r+1)}function cn(e){var t=e.indexOf(":");return-1==t?{s:on(e),e:on(e)}:{s:on(e.slice(0,t)),e:on(e.slice(t+1))}}function un(e,t){return"undefined"===typeof t||"number"===typeof t?un(e.s,e.e):("string"!==typeof e&&(e=sn(e)),"string"!==typeof t&&(t=sn(t)),e==t?e:e+":"+t)}function fn(e){var t={s:{c:0,r:0},e:{c:0,r:0}},r=0,n=0,a=0,i=e.length;for(r=0;n<i&&!((a=e.charCodeAt(n)-64)<1||a>26);++n)r=26*r+a;for(t.s.c=--r,r=0;n<i&&!((a=e.charCodeAt(n)-48)<0||a>9);++n)r=10*r+a;if(t.s.r=--r,n===i||10!=a)return t.e.c=t.s.c,t.e.r=t.s.r,t;for(++n,r=0;n!=i&&!((a=e.charCodeAt(n)-64)<1||a>26);++n)r=26*r+a;for(t.e.c=--r,r=0;n!=i&&!((a=e.charCodeAt(n)-48)<0||a>9);++n)r=10*r+a;return t.e.r=--r,t}function hn(e,t){var r="d"==e.t&&t instanceof Date;if(null!=e.z)try{return e.w=Ge(e.z,r?ot(t):t)}catch(n){}try{return e.w=Ge((e.XF||{}).numFmtId||(r?14:0),r?ot(t):t)}catch(n){return""+t}}function dn(e,t,r){return null==e||null==e.t||"z"==e.t?"":void 0!==e.w?e.w:("d"==e.t&&!e.z&&r&&r.dateNF&&(e.z=r.dateNF),"e"==e.t?Yn[e.v]||e.v:hn(e,void 0==t?e.v:t))}function pn(e,t){var r=t&&t.sheet?t.sheet:"Sheet1",n={};return n[r]=e,{SheetNames:[r],Sheets:n}}function mn(e,t,r){var n=r||{},a=e?Array.isArray(e):n.dense;var i=e||(a?[]:{}),l=0,o=0;if(i&&null!=n.origin){if("number"==typeof n.origin)l=n.origin;else{var s="string"==typeof n.origin?on(n.origin):n.origin;l=s.r,o=s.c}i["!ref"]||(i["!ref"]="A1:A1")}var c={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(i["!ref"]){var u=fn(i["!ref"]);c.s.c=u.s.c,c.s.r=u.s.r,c.e.c=Math.max(c.e.c,u.e.c),c.e.r=Math.max(c.e.r,u.e.r),-1==l&&(c.e.r=l=u.e.r+1)}for(var f=0;f!=t.length;++f)if(t[f]){if(!Array.isArray(t[f]))throw new Error("aoa_to_sheet expects an array of arrays");for(var h=0;h!=t[f].length;++h)if("undefined"!==typeof t[f][h]){var d={v:t[f][h]},p=l+f,m=o+h;if(c.s.r>p&&(c.s.r=p),c.s.c>m&&(c.s.c=m),c.e.r<p&&(c.e.r=p),c.e.c<m&&(c.e.c=m),!t[f][h]||"object"!==typeof t[f][h]||Array.isArray(t[f][h])||t[f][h]instanceof Date)if(Array.isArray(d.v)&&(d.f=t[f][h][1],d.v=d.v[0]),null===d.v)if(d.f)d.t="n";else if(n.nullError)d.t="e",d.v=0;else{if(!n.sheetStubs)continue;d.t="z"}else"number"===typeof d.v?d.t="n":"boolean"===typeof d.v?d.t="b":d.v instanceof Date?(d.z=n.dateNF||pe[14],n.cellDates?(d.t="d",d.w=Ge(d.z,ot(d.v))):(d.t="n",d.v=ot(d.v),d.w=Ge(d.z,d.v))):d.t="s";else d=t[f][h];if(a)i[p]||(i[p]=[]),i[p][m]&&i[p][m].z&&(d.z=i[p][m].z),i[p][m]=d;else{var g=sn({c:m,r:p});i[g]&&i[g].z&&(d.z=i[g].z),i[g]=d}}}return c.s.c<1e7&&(i["!ref"]=un(c)),i}function gn(e,t){return mn(null,e,t)}function vn(e,t){return t||(t=Xr(4)),t.write_shift(4,e),t}function bn(e){var t=e.read_shift(4);return 0===t?"":e.read_shift(t,"dbcs")}function yn(e,t){var r=!1;return null==t&&(r=!0,t=Xr(4+2*e.length)),t.write_shift(4,e.length),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}function wn(e){return{ich:e.read_shift(2),ifnt:e.read_shift(2)}}function Tn(e,t){var r=e.l,n=e.read_shift(1),a=bn(e),i=[],l={t:a,h:a};if(0!==(1&n)){for(var o=e.read_shift(4),s=0;s!=o;++s)i.push(wn(e));l.r=i}else l.r=[{ich:0,ifnt:0}];return e.l=r+t,l}var En=Tn;function Sn(e,t){var r=!1;return null==t&&(r=!0,t=Xr(23+4*e.t.length)),t.write_shift(1,1),yn(e.t,t),t.write_shift(4,1),function(e,t){t||(t=Xr(4)),t.write_shift(2,e.ich||0),t.write_shift(2,e.ifnt||0)}({ich:0,ifnt:0},t),r?t.slice(0,t.l):t}function xn(e){var t=e.read_shift(4),r=e.read_shift(2);return r+=e.read_shift(1)<<16,e.l++,{c:t,iStyleRef:r}}function kn(e,t){return null==t&&(t=Xr(8)),t.write_shift(-4,e.c),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}function _n(e){var t=e.read_shift(2);return t+=e.read_shift(1)<<16,e.l++,{c:-1,iStyleRef:t}}function Cn(e,t){return null==t&&(t=Xr(4)),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}var An=bn,Nn=yn;function On(e){var t=e.read_shift(4);return 0===t||4294967295===t?"":e.read_shift(t,"dbcs")}function Rn(e,t){var r=!1;return null==t&&(r=!0,t=Xr(127)),t.write_shift(4,e.length>0?e.length:4294967295),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}var Pn=bn,In=On,Dn=Rn;function Ln(e){var t=e.slice(e.l,e.l+4),r=1&t[0],n=2&t[0];e.l+=4;var a=0===n?Pr([0,0,0,0,252&t[0],t[1],t[2],t[3]],0):Ur(t,0)>>2;return r?a/100:a}function Mn(e,t){null==t&&(t=Xr(4));var r=0,n=0,a=100*e;if(e==(0|e)&&e>=-(1<<29)&&e<1<<29?n=1:a==(0|a)&&a>=-(1<<29)&&a<1<<29&&(n=1,r=1),!n)throw new Error("unsupported RkNumber "+e);t.write_shift(-4,((r?a:e)<<2)+(r+2))}function Fn(e){var t={s:{},e:{}};return t.s.r=e.read_shift(4),t.e.r=e.read_shift(4),t.s.c=e.read_shift(4),t.e.c=e.read_shift(4),t}var jn=Fn,Un=function(e,t){return t||(t=Xr(16)),t.write_shift(4,e.s.r),t.write_shift(4,e.e.r),t.write_shift(4,e.s.c),t.write_shift(4,e.e.c),t};function Bn(e){if(e.length-e.l<8)throw"XLS Xnum Buffer underflow";return e.read_shift(8,"f")}function zn(e,t){return(t||Xr(8)).write_shift(8,e,"f")}function Hn(e,t){if(t||(t=Xr(8)),!e||e.auto)return t.write_shift(4,0),t.write_shift(4,0),t;null!=e.index?(t.write_shift(1,2),t.write_shift(1,e.index)):null!=e.theme?(t.write_shift(1,6),t.write_shift(1,e.theme)):(t.write_shift(1,5),t.write_shift(1,0));var r=e.tint||0;if(r>0?r*=32767:r<0&&(r*=32768),t.write_shift(2,r),e.rgb&&null==e.theme){var n=e.rgb||"FFFFFF";"number"==typeof n&&(n=("000000"+n.toString(16)).slice(-6)),t.write_shift(1,parseInt(n.slice(0,2),16)),t.write_shift(1,parseInt(n.slice(2,4),16)),t.write_shift(1,parseInt(n.slice(4,6),16)),t.write_shift(1,255)}else t.write_shift(2,0),t.write_shift(1,0),t.write_shift(1,0);return t}var Wn=80,Vn={1:{n:"CodePage",t:2},2:{n:"Category",t:Wn},3:{n:"PresentationFormat",t:Wn},4:{n:"ByteCount",t:3},5:{n:"LineCount",t:3},6:{n:"ParagraphCount",t:3},7:{n:"SlideCount",t:3},8:{n:"NoteCount",t:3},9:{n:"HiddenCount",t:3},10:{n:"MultimediaClipCount",t:3},11:{n:"ScaleCrop",t:11},12:{n:"HeadingPairs",t:4108},13:{n:"TitlesOfParts",t:4126},14:{n:"Manager",t:Wn},15:{n:"Company",t:Wn},16:{n:"LinksUpToDate",t:11},17:{n:"CharacterCount",t:3},19:{n:"SharedDoc",t:11},22:{n:"HyperlinksChanged",t:11},23:{n:"AppVersion",t:3,p:"version"},24:{n:"DigSig",t:65},26:{n:"ContentType",t:Wn},27:{n:"ContentStatus",t:Wn},28:{n:"Language",t:Wn},29:{n:"Version",t:Wn},255:{},2147483648:{n:"Locale",t:19},2147483651:{n:"Behavior",t:19},1919054434:{}},$n={1:{n:"CodePage",t:2},2:{n:"Title",t:Wn},3:{n:"Subject",t:Wn},4:{n:"Author",t:Wn},5:{n:"Keywords",t:Wn},6:{n:"Comments",t:Wn},7:{n:"Template",t:Wn},8:{n:"LastAuthor",t:Wn},9:{n:"RevNumber",t:Wn},10:{n:"EditTime",t:64},11:{n:"LastPrinted",t:64},12:{n:"CreatedDate",t:64},13:{n:"ModifiedDate",t:64},14:{n:"PageCount",t:3},15:{n:"WordCount",t:3},16:{n:"CharCount",t:3},17:{n:"Thumbnail",t:71},18:{n:"Application",t:Wn},19:{n:"DocSecurity",t:3},255:{},2147483648:{n:"Locale",t:19},2147483651:{n:"Behavior",t:19},1919054434:{}};function Gn(e){return e.map((function(e){return[e>>16&255,e>>8&255,255&e]}))}var Kn=vt(Gn([0,16777215,16711680,65280,255,16776960,16711935,65535,0,16777215,16711680,65280,255,16776960,16711935,65535,8388608,32768,128,8421376,8388736,32896,12632256,8421504,10066431,10040166,16777164,13434879,6684774,16744576,26316,13421823,128,16711935,16776960,65535,8388736,8388608,32896,255,52479,13434879,13434828,16777113,10079487,16751052,13408767,16764057,3368703,3394764,10079232,16763904,16750848,16737792,6710937,9868950,13158,3381606,13056,3355392,10040064,10040166,3355545,3355443,16777215,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0])),Yn={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},Xn={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"},Qn={workbooks:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml",xlsm:"application/vnd.ms-excel.sheet.macroEnabled.main+xml",xlsb:"application/vnd.ms-excel.sheet.binary.macroEnabled.main",xlam:"application/vnd.ms-excel.addin.macroEnabled.main+xml",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml"},strs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml",xlsb:"application/vnd.ms-excel.sharedStrings"},comments:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml",xlsb:"application/vnd.ms-excel.comments"},sheets:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml",xlsb:"application/vnd.ms-excel.worksheet"},charts:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml",xlsb:"application/vnd.ms-excel.chartsheet"},dialogs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml",xlsb:"application/vnd.ms-excel.dialogsheet"},macros:{xlsx:"application/vnd.ms-excel.macrosheet+xml",xlsb:"application/vnd.ms-excel.macrosheet"},metadata:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml",xlsb:"application/vnd.ms-excel.sheetMetadata"},styles:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml",xlsb:"application/vnd.ms-excel.styles"}};function qn(e,t){var r,n=function(e){for(var t=[],r=rt(e),n=0;n!==r.length;++n)null==t[e[r[n]]]&&(t[e[r[n]]]=[]),t[e[r[n]]].push(r[n]);return t}(Xn),a=[];a[a.length]=xt,a[a.length]=Vt("Types",null,{xmlns:Xt,"xmlns:xsd":ar,"xmlns:xsi":nr}),a=a.concat([["xml","application/xml"],["bin","application/vnd.ms-excel.sheet.binary.macroEnabled.main"],["vml","application/vnd.openxmlformats-officedocument.vmlDrawing"],["data","application/vnd.openxmlformats-officedocument.model+data"],["bmp","image/bmp"],["png","image/png"],["gif","image/gif"],["emf","image/x-emf"],["wmf","image/x-wmf"],["jpg","image/jpeg"],["jpeg","image/jpeg"],["tif","image/tiff"],["tiff","image/tiff"],["pdf","application/pdf"],["rels","application/vnd.openxmlformats-package.relationships+xml"]].map((function(e){return Vt("Default",null,{Extension:e[0],ContentType:e[1]})})));var i=function(n){e[n]&&e[n].length>0&&(r=e[n][0],a[a.length]=Vt("Override",null,{PartName:("/"==r[0]?"":"/")+r,ContentType:Qn[n][t.bookType]||Qn[n].xlsx}))},l=function(r){(e[r]||[]).forEach((function(e){a[a.length]=Vt("Override",null,{PartName:("/"==e[0]?"":"/")+e,ContentType:Qn[r][t.bookType]||Qn[r].xlsx})}))},o=function(t){(e[t]||[]).forEach((function(e){a[a.length]=Vt("Override",null,{PartName:("/"==e[0]?"":"/")+e,ContentType:n[t][0]})}))};return i("workbooks"),l("sheets"),l("charts"),o("themes"),["strs","styles"].forEach(i),["coreprops","extprops","custprops"].forEach(o),o("vba"),o("comments"),o("threadedcomments"),o("drawings"),l("metadata"),o("people"),a.length>2&&(a[a.length]="</Types>",a[1]=a[1].replace("/>",">")),a.join("")}var Jn={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",SHEET:"http://sheetjs.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",XLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLink",CXML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXml",CXMLP:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXmlProps",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",CHART:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chart",CHARTEX:"http://schemas.microsoft.com/office/2014/relationships/chartEx",CS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chartsheet",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/dialogsheet",MS:"http://schemas.microsoft.com/office/2006/relationships/xlMacrosheet",IMG:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image",DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function Zn(e){var t=e.lastIndexOf("/");return e.slice(0,t+1)+"_rels/"+e.slice(t+1)+".rels"}function ea(e){var t=[xt,Vt("Relationships",null,{xmlns:Qt})];return rt(e["!id"]).forEach((function(r){t[t.length]=Vt("Relationship",null,e["!id"][r])})),t.length>2&&(t[t.length]="</Relationships>",t[1]=t[1].replace("/>",">")),t.join("")}function ta(e,t,r,n,a,i){if(a||(a={}),e["!id"]||(e["!id"]={}),e["!idx"]||(e["!idx"]=1),t<0)for(t=e["!idx"];e["!id"]["rId"+t];++t);if(e["!idx"]=t+1,a.Id="rId"+t,a.Type=n,a.Target=r,i?a.TargetMode=i:[Jn.HLINK,Jn.XPATH,Jn.XMISS].indexOf(a.Type)>-1&&(a.TargetMode="External"),e["!id"][a.Id])throw new Error("Cannot rewrite rId "+t);return e["!id"][a.Id]=a,e[("/"+a.Target).replace("//","/")]=a,t}function ra(e,t,r){return['  <rdf:Description rdf:about="'+e+'">\n','    <rdf:type rdf:resource="http://docs.oasis-open.org/ns/office/1.2/meta/'+(r||"odf")+"#"+t+'"/>\n',"  </rdf:Description>\n"].join("")}function na(){return'<office:document-meta xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:xlink="http://www.w3.org/1999/xlink" office:version="1.2"><office:meta><meta:generator>SheetJS '+I.version+"</meta:generator></office:meta></office:document-meta>"}var aa=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]];function ia(e,t,r,n,a){null==a[e]&&null!=t&&""!==t&&(a[e]=t,t=Nt(t),n[n.length]=r?Vt(e,t,r):Ht(e,t))}function la(e,t){var r=t||{},n=[xt,Vt("cp:coreProperties",null,{"xmlns:cp":Gt,"xmlns:dc":Jt,"xmlns:dcterms":Zt,"xmlns:dcmitype":er,"xmlns:xsi":nr})],a={};if(!e&&!r.Props)return n.join("");e&&(null!=e.CreatedDate&&ia("dcterms:created","string"===typeof e.CreatedDate?e.CreatedDate:$t(e.CreatedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},n,a),null!=e.ModifiedDate&&ia("dcterms:modified","string"===typeof e.ModifiedDate?e.ModifiedDate:$t(e.ModifiedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},n,a));for(var i=0;i!=aa.length;++i){var l=aa[i],o=r.Props&&null!=r.Props[l[1]]?r.Props[l[1]]:e?e[l[1]]:null;!0===o?o="1":!1===o?o="0":"number"==typeof o&&(o=String(o)),null!=o&&ia(l[0],o,null,n,a)}return n.length>2&&(n[n.length]="</cp:coreProperties>",n[1]=n[1].replace("/>",">")),n.join("")}var oa=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]],sa=["Worksheets","SheetNames","NamedRanges","DefinedNames","Chartsheets","ChartNames"];function ca(e){var t=[],r=Vt;return e||(e={}),e.Application="SheetJS",t[t.length]=xt,t[t.length]=Vt("Properties",null,{xmlns:Yt,"xmlns:vt":rr}),oa.forEach((function(n){if(void 0!==e[n[1]]){var a;switch(n[2]){case"string":a=Nt(String(e[n[1]]));break;case"bool":a=e[n[1]]?"true":"false"}void 0!==a&&(t[t.length]=r(n[0],a))}})),t[t.length]=r("HeadingPairs",r("vt:vector",r("vt:variant","<vt:lpstr>Worksheets</vt:lpstr>")+r("vt:variant",r("vt:i4",String(e.Worksheets))),{size:2,baseType:"variant"})),t[t.length]=r("TitlesOfParts",r("vt:vector",e.SheetNames.map((function(e){return"<vt:lpstr>"+Nt(e)+"</vt:lpstr>"})).join(""),{size:e.Worksheets,baseType:"lpstr"})),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}function ua(e){var t=[xt,Vt("Properties",null,{xmlns:Kt,"xmlns:vt":rr})];if(!e)return t.join("");var r=1;return rt(e).forEach((function(n){++r,t[t.length]=Vt("property",function(e,t){switch(typeof e){case"string":var r=Vt("vt:lpwstr",Nt(e));return t&&(r=r.replace(/&quot;/g,"_x0022_")),r;case"number":return Vt((0|e)==e?"vt:i4":"vt:r8",Nt(String(e)));case"boolean":return Vt("vt:bool",e?"true":"false")}if(e instanceof Date)return Vt("vt:filetime",$t(e));throw new Error("Unable to serialize "+e)}(e[n],!0),{fmtid:"{D5CDD505-2E9C-101B-9397-08002B2CF9AE}",pid:r,name:Nt(n)})})),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}var fa={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"};function ha(e,t){var r=Xr(4),n=Xr(4);switch(r.write_shift(4,80==e?31:e),e){case 3:n.write_shift(-4,t);break;case 5:(n=Xr(8)).write_shift(8,t,"f");break;case 11:n.write_shift(4,t?1:0);break;case 64:n=function(e){var t=("string"==typeof e?new Date(Date.parse(e)):e).getTime()/1e3+11644473600,r=t%Math.pow(2,32),n=(t-r)/Math.pow(2,32);n*=1e7;var a=(r*=1e7)/Math.pow(2,32)|0;a>0&&(r%=Math.pow(2,32),n+=a);var i=Xr(8);return i.write_shift(4,r),i.write_shift(4,n),i}(t);break;case 31:case 80:for((n=Xr(4+2*(t.length+1)+(t.length%2?0:2))).write_shift(4,t.length+1),n.write_shift(0,t,"dbcs");n.l!=n.length;)n.write_shift(1,0);break;default:throw new Error("TypedPropertyValue unrecognized type "+e+" "+t)}return re([r,n])}var da=["CodePage","Thumbnail","_PID_LINKBASE","_PID_HLINKS","SystemIdentifier","FMTID"];function pa(e){switch(typeof e){case"boolean":return 11;case"number":return(0|e)==e?3:5;case"string":return 31;case"object":if(e instanceof Date)return 64}return-1}function ma(e,t,r){var n=Xr(8),a=[],i=[],l=8,o=0,s=Xr(8),c=Xr(8);if(s.write_shift(4,2),s.write_shift(4,1200),c.write_shift(4,1),i.push(s),a.push(c),l+=8+s.length,!t){(c=Xr(8)).write_shift(4,0),a.unshift(c);var u=[Xr(4)];for(u[0].write_shift(4,e.length),o=0;o<e.length;++o){var f=e[o][0];for((s=Xr(8+2*(f.length+1)+(f.length%2?0:2))).write_shift(4,o+2),s.write_shift(4,f.length+1),s.write_shift(0,f,"dbcs");s.l!=s.length;)s.write_shift(1,0);u.push(s)}s=re(u),i.unshift(s),l+=8+s.length}for(o=0;o<e.length;++o)if((!t||t[e[o][0]])&&!(da.indexOf(e[o][0])>-1||sa.indexOf(e[o][0])>-1)&&null!=e[o][1]){var h=e[o][1],d=0;if(t){var p=r[d=+t[e[o][0]]];if("version"==p.p&&"string"==typeof h){var m=h.split(".");h=(+m[0]<<16)+(+m[1]||0)}s=ha(p.t,h)}else{var g=pa(h);-1==g&&(g=31,h=String(h)),s=ha(g,h)}i.push(s),(c=Xr(8)).write_shift(4,t?d:2+o),a.push(c),l+=8+s.length}var v=8*(i.length+1);for(o=0;o<i.length;++o)a[o].write_shift(4,v),v+=i[o].length;return n.write_shift(4,l),n.write_shift(4,i.length),re([n].concat(a).concat(i))}function ga(e,t,r,n,a,i){var l=Xr(a?68:48),o=[l];l.write_shift(2,65534),l.write_shift(2,0),l.write_shift(4,842412599),l.write_shift(16,Je.utils.consts.HEADER_CLSID,"hex"),l.write_shift(4,a?2:1),l.write_shift(16,t,"hex"),l.write_shift(4,a?68:48);var s=ma(e,r,n);if(o.push(s),a){var c=ma(a,null,null);l.write_shift(16,i,"hex"),l.write_shift(4,68+s.length),o.push(c)}return re(o)}function va(e,t){return 1===e.read_shift(t)}function ba(e,t){return t||(t=Xr(2)),t.write_shift(2,+!!e),t}function ya(e){return e.read_shift(2,"u")}function wa(e,t){return t||(t=Xr(2)),t.write_shift(2,e),t}function Ta(e,t,r){return r||(r=Xr(2)),r.write_shift(1,"e"==t?+e:+!!e),r.write_shift(1,"e"==t?1:0),r}function Ea(e,t,r){var n=e.read_shift(r&&r.biff>=12?2:1),a="sbcs-cont",i=D;(r&&r.biff>=8&&(D=1200),r&&8!=r.biff)?12==r.biff&&(a="wstr"):e.read_shift(1)&&(a="dbcs-cont");r.biff>=2&&r.biff<=5&&(a="cpstr");var l=n?e.read_shift(n,a):"";return D=i,l}function Sa(e){var t=e.t||"",r=Xr(3);r.write_shift(2,t.length),r.write_shift(1,1);var n=Xr(2*t.length);return n.write_shift(2*t.length,t,"utf16le"),re([r,n])}function xa(e,t,r){if(r){if(r.biff>=2&&r.biff<=5)return e.read_shift(t,"cpstr");if(r.biff>=12)return e.read_shift(t,"dbcs-cont")}return 0===e.read_shift(1)?e.read_shift(t,"sbcs-cont"):e.read_shift(t,"dbcs-cont")}function ka(e,t,r){var n=e.read_shift(r&&2==r.biff?1:2);return 0===n?(e.l++,""):xa(e,n,r)}function _a(e,t,r){if(r.biff>5)return ka(e,0,r);var n=e.read_shift(1);return 0===n?(e.l++,""):e.read_shift(n,r.biff<=4||!e.lens?"cpstr":"sbcs-cont")}function Ca(e,t,r){return r||(r=Xr(3+2*e.length)),r.write_shift(2,e.length),r.write_shift(1,1),r.write_shift(31,e,"utf16le"),r}function Aa(e,t){t||(t=Xr(6+2*e.length)),t.write_shift(4,1+e.length);for(var r=0;r<e.length;++r)t.write_shift(2,e.charCodeAt(r));return t.write_shift(2,0),t}function Na(e){var t=Xr(512),r=0,n=e.Target;"file://"==n.slice(0,7)&&(n=n.slice(7));var a=n.indexOf("#"),i=a>-1?31:23;switch(n.charAt(0)){case"#":i=28;break;case".":i&=-3}t.write_shift(4,2),t.write_shift(4,i);var l=[8,6815827,6619237,4849780,83];for(r=0;r<l.length;++r)t.write_shift(4,l[r]);if(28==i)Aa(n=n.slice(1),t);else if(2&i){for(l="e0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),r=0;r<l.length;++r)t.write_shift(1,parseInt(l[r],16));var o=a>-1?n.slice(0,a):n;for(t.write_shift(4,2*(o.length+1)),r=0;r<o.length;++r)t.write_shift(2,o.charCodeAt(r));t.write_shift(2,0),8&i&&Aa(a>-1?n.slice(a+1):"",t)}else{for(l="03 03 00 00 00 00 00 00 c0 00 00 00 00 00 00 46".split(" "),r=0;r<l.length;++r)t.write_shift(1,parseInt(l[r],16));for(var s=0;"../"==n.slice(3*s,3*s+3)||"..\\"==n.slice(3*s,3*s+3);)++s;for(t.write_shift(2,s),t.write_shift(4,n.length-3*s+1),r=0;r<n.length-3*s;++r)t.write_shift(1,255&n.charCodeAt(r+3*s));for(t.write_shift(1,0),t.write_shift(2,65535),t.write_shift(2,57005),r=0;r<6;++r)t.write_shift(4,0)}return t.slice(0,t.l)}function Oa(e,t,r,n){return n||(n=Xr(6)),n.write_shift(2,e),n.write_shift(2,t),n.write_shift(2,r||0),n}function Ra(e,t,r){var n=r.biff>8?4:2;return[e.read_shift(n),e.read_shift(n,"i"),e.read_shift(n,"i")]}function Pa(e){var t=e.read_shift(2),r=e.read_shift(2);return{s:{c:e.read_shift(2),r:t},e:{c:e.read_shift(2),r:r}}}function Ia(e,t){return t||(t=Xr(8)),t.write_shift(2,e.s.r),t.write_shift(2,e.e.r),t.write_shift(2,e.s.c),t.write_shift(2,e.e.c),t}function Da(e,t,r){var n=1536,a=16;switch(r.bookType){case"biff8":case"xla":break;case"biff5":n=1280,a=8;break;case"biff4":n=4,a=6;break;case"biff3":n=3,a=6;break;case"biff2":n=2,a=4;break;default:throw new Error("unsupported BIFF version")}var i=Xr(a);return i.write_shift(2,n),i.write_shift(2,t),a>4&&i.write_shift(2,29282),a>6&&i.write_shift(2,1997),a>8&&(i.write_shift(2,49161),i.write_shift(2,1),i.write_shift(2,1798),i.write_shift(2,0)),i}function La(e,t){var r=!t||t.biff>=8?2:1,n=Xr(8+r*e.name.length);n.write_shift(4,e.pos),n.write_shift(1,e.hs||0),n.write_shift(1,e.dt),n.write_shift(1,e.name.length),t.biff>=8&&n.write_shift(1,1),n.write_shift(r*e.name.length,e.name,t.biff<8?"sbcs":"utf16le");var a=n.slice(0,n.l);return a.l=n.l,a}function Ma(e,t,r,n){var a=r&&5==r.biff;n||(n=Xr(a?3+t.length:5+2*t.length)),n.write_shift(2,e),n.write_shift(a?1:2,t.length),a||n.write_shift(1,1),n.write_shift((a?1:2)*t.length,t,a?"sbcs":"utf16le");var i=n.length>n.l?n.slice(0,n.l):n;return null==i.l&&(i.l=i.length),i}function Fa(e,t,r,n){var a=r&&5==r.biff;n||(n=Xr(a?16:20)),n.write_shift(2,0),e.style?(n.write_shift(2,e.numFmtId||0),n.write_shift(2,65524)):(n.write_shift(2,e.numFmtId||0),n.write_shift(2,t<<4));var i=0;return e.numFmtId>0&&a&&(i|=1024),n.write_shift(4,i),n.write_shift(4,0),a||n.write_shift(4,0),n.write_shift(2,0),n}function ja(e,t,r){if(r.biff<8)return function(e,t,r){3==e[e.l+1]&&e[e.l]++;var n=Ea(e,0,r);return 3==n.charCodeAt(0)?n.slice(1):n}(e,0,r);for(var n=[],a=e.l+t,i=e.read_shift(r.biff>8?4:2);0!==i--;)n.push(Ra(e,r.biff,r));if(e.l!=a)throw new Error("Bad ExternSheet: "+e.l+" != "+a);return n}function Ua(e){var t=Xr(24),r=on(e[0]);t.write_shift(2,r.r),t.write_shift(2,r.r),t.write_shift(2,r.c),t.write_shift(2,r.c);for(var n="d0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),a=0;a<16;++a)t.write_shift(1,parseInt(n[a],16));return re([t,Na(e[1])])}function Ba(e){var t=e[1].Tooltip,r=Xr(10+2*(t.length+1));r.write_shift(2,2048);var n=on(e[0]);r.write_shift(2,n.r),r.write_shift(2,n.r),r.write_shift(2,n.c),r.write_shift(2,n.c);for(var a=0;a<t.length;++a)r.write_shift(2,t.charCodeAt(a));return r.write_shift(2,0),r}function za(e,t,r){if(!r.cellStyles)return Yr(e,t);var n=r&&r.biff>=12?4:2,a=e.read_shift(n),i=e.read_shift(n),l=e.read_shift(n),o=e.read_shift(n),s=e.read_shift(2);2==n&&(e.l+=2);var c={s:a,e:i,w:l,ixfe:o,flags:s};return(r.biff>=5||!r.biff)&&(c.level=s>>8&7),c}var Ha=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},t=at({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function r(t,r){var n=r||{};n.dateNF||(n.dateNF="yyyymmdd");var a=gn(function(t,r){var n=[],a=q(1);switch(r.type){case"base64":a=Z(Y(t));break;case"binary":a=Z(t);break;case"buffer":case"array":a=t}Kr(a,0);var i=a.read_shift(1),l=!!(136&i),o=!1,s=!1;switch(i){case 2:case 3:case 131:case 139:case 245:break;case 48:case 49:o=!0,l=!0;break;case 140:s=!0;break;default:throw new Error("DBF Unsupported Version: "+i.toString(16))}var c=0,u=521;2==i&&(c=a.read_shift(2)),a.l+=3,2!=i&&(c=a.read_shift(4)),c>1048576&&(c=1e6),2!=i&&(u=a.read_shift(2));var f=a.read_shift(2),h=r.codepage||1252;2!=i&&(a.l+=16,a.read_shift(1),0!==a[a.l]&&(h=e[a[a.l]]),a.l+=1,a.l+=2),s&&(a.l+=36);for(var d=[],p={},m=Math.min(a.length,2==i?521:u-10-(o?264:0)),g=s?32:11;a.l<m&&13!=a[a.l];)switch((p={}).name=H.utils.decode(h,a.slice(a.l,a.l+g)).replace(/[\u0000\r\n].*$/g,""),a.l+=g,p.type=String.fromCharCode(a.read_shift(1)),2==i||s||(p.offset=a.read_shift(4)),p.len=a.read_shift(1),2==i&&(p.offset=a.read_shift(2)),p.dec=a.read_shift(1),p.name.length&&d.push(p),2!=i&&(a.l+=s?13:14),p.type){case"B":o&&8==p.len||!r.WTF||console.log("Skipping "+p.name+":"+p.type);break;case"G":case"P":r.WTF&&console.log("Skipping "+p.name+":"+p.type);break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw new Error("Unknown Field Type: "+p.type)}if(13!==a[a.l]&&(a.l=u-1),13!==a.read_shift(1))throw new Error("DBF Terminator not found "+a.l+" "+a[a.l]);a.l=u;var v=0,b=0;for(n[0]=[],b=0;b!=d.length;++b)n[0][b]=d[b].name;for(;c-- >0;)if(42!==a[a.l])for(++a.l,n[++v]=[],b=0,b=0;b!=d.length;++b){var y=a.slice(a.l,a.l+d[b].len);a.l+=d[b].len,Kr(y,0);var w=H.utils.decode(h,y);switch(d[b].type){case"C":w.trim().length&&(n[v][b]=w.replace(/\s+$/,""));break;case"D":8===w.length?n[v][b]=new Date(+w.slice(0,4),+w.slice(4,6)-1,+w.slice(6,8)):n[v][b]=w;break;case"F":n[v][b]=parseFloat(w.trim());break;case"+":case"I":n[v][b]=s?2147483648^y.read_shift(-4,"i"):y.read_shift(4,"i");break;case"L":switch(w.trim().toUpperCase()){case"Y":case"T":n[v][b]=!0;break;case"N":case"F":n[v][b]=!1;break;case"":case"?":break;default:throw new Error("DBF Unrecognized L:|"+w+"|")}break;case"M":if(!l)throw new Error("DBF Unexpected MEMO for type "+i.toString(16));n[v][b]="##MEMO##"+(s?parseInt(w.trim(),10):y.read_shift(4));break;case"N":(w=w.replace(/\u0000/g,"").trim())&&"."!=w&&(n[v][b]=+w||0);break;case"@":n[v][b]=new Date(y.read_shift(-8,"f")-621356832e5);break;case"T":n[v][b]=new Date(864e5*(y.read_shift(4)-2440588)+y.read_shift(4));break;case"Y":n[v][b]=y.read_shift(4,"i")/1e4+y.read_shift(4,"i")/1e4*Math.pow(2,32);break;case"O":n[v][b]=-y.read_shift(-8,"f");break;case"B":if(o&&8==d[b].len){n[v][b]=y.read_shift(8,"f");break}case"G":case"P":y.l+=d[b].len;break;case"0":if("_NullFlags"===d[b].name)break;default:throw new Error("DBF Unsupported data type "+d[b].type)}}else a.l+=f;if(2!=i&&a.l<a.length&&26!=a[a.l++])throw new Error("DBF EOF Marker missing "+(a.l-1)+" of "+a.length+" "+a[a.l-1].toString(16));return r&&r.sheetRows&&(n=n.slice(0,r.sheetRows)),r.DBF=d,n}(t,n),n);return a["!cols"]=n.DBF.map((function(e){return{wch:e.len,DBF:e}})),delete n.DBF,a}var n={B:8,C:250,L:1,D:8,"?":0,"":0};return{to_workbook:function(e,t){try{return pn(r(e,t),t)}catch(n){if(t&&t.WTF)throw n}return{SheetNames:[],Sheets:{}}},to_sheet:r,from_sheet:function(e,r){var a=r||{};if(+a.codepage>=0&&U(+a.codepage),"string"==a.type)throw new Error("Cannot write DBF to JS string");var i=Qr(),l=os(e,{header:1,raw:!0,cellDates:!0}),o=l[0],s=l.slice(1),c=e["!cols"]||[],u=0,f=0,h=0,d=1;for(u=0;u<o.length;++u)if(((c[u]||{}).DBF||{}).name)o[u]=c[u].DBF.name,++h;else if(null!=o[u]){if(++h,"number"===typeof o[u]&&(o[u]=o[u].toString(10)),"string"!==typeof o[u])throw new Error("DBF Invalid column name "+o[u]+" |"+typeof o[u]+"|");if(o.indexOf(o[u])!==u)for(f=0;f<1024;++f)if(-1==o.indexOf(o[u]+"_"+f)){o[u]+="_"+f;break}}var p=fn(e["!ref"]),m=[],g=[],v=[];for(u=0;u<=p.e.c-p.s.c;++u){var b="",y="",w=0,T=[];for(f=0;f<s.length;++f)null!=s[f][u]&&T.push(s[f][u]);if(0!=T.length&&null!=o[u]){for(f=0;f<T.length;++f){switch(typeof T[f]){case"number":y="B";break;case"string":default:y="C";break;case"boolean":y="L";break;case"object":y=T[f]instanceof Date?"D":"C"}w=Math.max(w,String(T[f]).length),b=b&&b!=y?"C":y}w>250&&(w=250),"C"==(y=((c[u]||{}).DBF||{}).type)&&c[u].DBF.len>w&&(w=c[u].DBF.len),"B"==b&&"N"==y&&(b="N",v[u]=c[u].DBF.dec,w=c[u].DBF.len),g[u]="C"==b||"N"==y?w:n[b]||0,d+=g[u],m[u]=b}else m[u]="?"}var E=i.next(32);for(E.write_shift(4,318902576),E.write_shift(4,s.length),E.write_shift(2,296+32*h),E.write_shift(2,d),u=0;u<4;++u)E.write_shift(4,0);for(E.write_shift(4,(+t[L]||3)<<8),u=0,f=0;u<o.length;++u)if(null!=o[u]){var S=i.next(32),x=(o[u].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);S.write_shift(1,x,"sbcs"),S.write_shift(1,"?"==m[u]?"C":m[u],"sbcs"),S.write_shift(4,f),S.write_shift(1,g[u]||n[m[u]]||0),S.write_shift(1,v[u]||0),S.write_shift(1,2),S.write_shift(4,0),S.write_shift(1,0),S.write_shift(4,0),S.write_shift(4,0),f+=g[u]||n[m[u]]||0}var k=i.next(264);for(k.write_shift(4,13),u=0;u<65;++u)k.write_shift(4,0);for(u=0;u<s.length;++u){var _=i.next(d);for(_.write_shift(1,0),f=0;f<o.length;++f)if(null!=o[f])switch(m[f]){case"L":_.write_shift(1,null==s[u][f]?63:s[u][f]?84:70);break;case"B":_.write_shift(8,s[u][f]||0,"f");break;case"N":var C="0";for("number"==typeof s[u][f]&&(C=s[u][f].toFixed(v[f]||0)),h=0;h<g[f]-C.length;++h)_.write_shift(1,32);_.write_shift(1,C,"sbcs");break;case"D":s[u][f]?(_.write_shift(4,("0000"+s[u][f].getFullYear()).slice(-4),"sbcs"),_.write_shift(2,("00"+(s[u][f].getMonth()+1)).slice(-2),"sbcs"),_.write_shift(2,("00"+s[u][f].getDate()).slice(-2),"sbcs")):_.write_shift(8,"00000000","sbcs");break;case"C":var A=String(null!=s[u][f]?s[u][f]:"").slice(0,g[f]);for(_.write_shift(1,A,"sbcs"),h=0;h<g[f]-A.length;++h)_.write_shift(1,32)}}return i.next(1).write_shift(1,26),i.end()}}}(),Wa=function(){var e={AA:"\xc0",BA:"\xc1",CA:"\xc2",DA:195,HA:"\xc4",JA:197,AE:"\xc8",BE:"\xc9",CE:"\xca",HE:"\xcb",AI:"\xcc",BI:"\xcd",CI:"\xce",HI:"\xcf",AO:"\xd2",BO:"\xd3",CO:"\xd4",DO:213,HO:"\xd6",AU:"\xd9",BU:"\xda",CU:"\xdb",HU:"\xdc",Aa:"\xe0",Ba:"\xe1",Ca:"\xe2",Da:227,Ha:"\xe4",Ja:229,Ae:"\xe8",Be:"\xe9",Ce:"\xea",He:"\xeb",Ai:"\xec",Bi:"\xed",Ci:"\xee",Hi:"\xef",Ao:"\xf2",Bo:"\xf3",Co:"\xf4",Do:245,Ho:"\xf6",Au:"\xf9",Bu:"\xfa",Cu:"\xfb",Hu:"\xfc",KC:"\xc7",Kc:"\xe7",q:"\xe6",z:"\u0153",a:"\xc6",j:"\u0152",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},t=new RegExp("\x1bN("+rt(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm"),r=function(t,r){var n=e[r];return"number"==typeof n?V(n):n},n=function(e,t,r){var n=t.charCodeAt(0)-32<<4|r.charCodeAt(0)-48;return 59==n?e:V(n)};function a(e,a){var i,l=e.split(/[\n\r]+/),o=-1,s=-1,c=0,u=0,f=[],h=[],d=null,p={},m=[],g=[],v=[],b=0;for(+a.codepage>=0&&U(+a.codepage);c!==l.length;++c){b=0;var y,w=l[c].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,n).replace(t,r),T=w.replace(/;;/g,"\0").split(";").map((function(e){return e.replace(/\u0000/g,";")})),E=T[0];if(w.length>0)switch(E){case"ID":case"E":case"B":case"O":case"W":break;case"P":"P"==T[1].charAt(0)&&h.push(w.slice(3).replace(/;;/g,";"));break;case"C":var S=!1,x=!1,k=!1,_=!1,C=-1,A=-1;for(u=1;u<T.length;++u)switch(T[u].charAt(0)){case"A":case"G":break;case"X":s=parseInt(T[u].slice(1))-1,x=!0;break;case"Y":for(o=parseInt(T[u].slice(1))-1,x||(s=0),i=f.length;i<=o;++i)f[i]=[];break;case"K":'"'===(y=T[u].slice(1)).charAt(0)?y=y.slice(1,y.length-1):"TRUE"===y?y=!0:"FALSE"===y?y=!1:isNaN(yt(y))?isNaN(Tt(y).getDate())||(y=mt(y)):(y=yt(y),null!==d&&He(d)&&(y=ft(y))),"undefined"!==typeof H&&"string"==typeof y&&"string"!=(a||{}).type&&(a||{}).codepage&&(y=H.utils.decode(a.codepage,y)),S=!0;break;case"E":_=!0;var N=Di(T[u].slice(1),{r:o,c:s});f[o][s]=[f[o][s],N];break;case"S":k=!0,f[o][s]=[f[o][s],"S5S"];break;case"R":C=parseInt(T[u].slice(1))-1;break;case"C":A=parseInt(T[u].slice(1))-1;break;default:if(a&&a.WTF)throw new Error("SYLK bad record "+w)}if(S&&(f[o][s]&&2==f[o][s].length?f[o][s][0]=y:f[o][s]=y,d=null),k){if(_)throw new Error("SYLK shared formula cannot have own formula");var O=C>-1&&f[C][A];if(!O||!O[1])throw new Error("SYLK shared formula cannot find base");f[o][s][1]=Fi(O[1],{r:o-C,c:s-A})}break;case"F":var R=0;for(u=1;u<T.length;++u)switch(T[u].charAt(0)){case"X":s=parseInt(T[u].slice(1))-1,++R;break;case"Y":for(o=parseInt(T[u].slice(1))-1,i=f.length;i<=o;++i)f[i]=[];break;case"M":b=parseInt(T[u].slice(1))/20;break;case"F":case"G":case"S":case"D":case"N":break;case"P":d=h[parseInt(T[u].slice(1))];break;case"W":for(v=T[u].slice(1).split(" "),i=parseInt(v[0],10);i<=parseInt(v[1],10);++i)b=parseInt(v[2],10),g[i-1]=0===b?{hidden:!0}:{wch:b},li(g[i-1]);break;case"C":g[s=parseInt(T[u].slice(1))-1]||(g[s]={});break;case"R":m[o=parseInt(T[u].slice(1))-1]||(m[o]={}),b>0?(m[o].hpt=b,m[o].hpx=ci(b)):0===b&&(m[o].hidden=!0);break;default:if(a&&a.WTF)throw new Error("SYLK bad record "+w)}R<1&&(d=null);break;default:if(a&&a.WTF)throw new Error("SYLK bad record "+w)}}return m.length>0&&(p["!rows"]=m),g.length>0&&(p["!cols"]=g),a&&a.sheetRows&&(f=f.slice(0,a.sheetRows)),[f,p]}function i(e,t){var r=function(e,t){switch(t.type){case"base64":return a(Y(e),t);case"binary":return a(e,t);case"buffer":return a(X&&Buffer.isBuffer(e)?e.toString("binary"):te(e),t);case"array":return a(gt(e),t)}throw new Error("Unrecognized type "+t.type)}(e,t),n=r[0],i=r[1],l=gn(n,t);return rt(i).forEach((function(e){l[e]=i[e]})),l}function l(e,t,r,n){var a="C;Y"+(r+1)+";X"+(n+1)+";K";switch(e.t){case"n":a+=e.v||0,e.f&&!e.F&&(a+=";E"+Mi(e.f,{r:r,c:n}));break;case"b":a+=e.v?"TRUE":"FALSE";break;case"e":a+=e.w||e.v;break;case"d":a+='"'+(e.w||e.v)+'"';break;case"s":a+='"'+e.v.replace(/"/g,"").replace(/;/g,";;")+'"'}return a}return e["|"]=254,{to_workbook:function(e,t){return pn(i(e,t),t)},to_sheet:i,from_sheet:function(e,t){var r,n,a=["ID;PWXL;N;E"],i=[],o=fn(e["!ref"]),s=Array.isArray(e),c="\r\n";a.push("P;PGeneral"),a.push("F;P0;DG0G8;M255"),e["!cols"]&&(n=a,e["!cols"].forEach((function(e,t){var r="F;W"+(t+1)+" "+(t+1)+" ";e.hidden?r+="0":("number"!=typeof e.width||e.wpx||(e.wpx=ni(e.width)),"number"!=typeof e.wpx||e.wch||(e.wch=ai(e.wpx)),"number"==typeof e.wch&&(r+=Math.round(e.wch)))," "!=r.charAt(r.length-1)&&n.push(r)}))),e["!rows"]&&function(e,t){t.forEach((function(t,r){var n="F;";t.hidden?n+="M0;":t.hpt?n+="M"+20*t.hpt+";":t.hpx&&(n+="M"+20*si(t.hpx)+";"),n.length>2&&e.push(n+"R"+(r+1))}))}(a,e["!rows"]),a.push("B;Y"+(o.e.r-o.s.r+1)+";X"+(o.e.c-o.s.c+1)+";D"+[o.s.c,o.s.r,o.e.c,o.e.r].join(" "));for(var u=o.s.r;u<=o.e.r;++u)for(var f=o.s.c;f<=o.e.c;++f){var h=sn({r:u,c:f});(r=s?(e[u]||[])[f]:e[h])&&(null!=r.v||r.f&&!r.F)&&i.push(l(r,0,u,f))}return a.join(c)+c+i.join(c)+c+"E"+c}}}(),Va=function(){function e(e,t){for(var r=e.split("\n"),n=-1,a=-1,i=0,l=[];i!==r.length;++i)if("BOT"!==r[i].trim()){if(!(n<0)){for(var o=r[i].trim().split(","),s=o[0],c=o[1],u=r[++i]||"";1&(u.match(/["]/g)||[]).length&&i<r.length-1;)u+="\n"+r[++i];switch(u=u.trim(),+s){case-1:if("BOT"===u){l[++n]=[],a=0;continue}if("EOD"!==u)throw new Error("Unrecognized DIF special command "+u);break;case 0:"TRUE"===u?l[n][a]=!0:"FALSE"===u?l[n][a]=!1:isNaN(yt(c))?isNaN(Tt(c).getDate())?l[n][a]=c:l[n][a]=mt(c):l[n][a]=yt(c),++a;break;case 1:(u=(u=u.slice(1,u.length-1)).replace(/""/g,'"'))&&u.match(/^=".*"$/)&&(u=u.slice(2,-1)),l[n][a++]=""!==u?u:null}if("EOD"===u)break}}else l[++n]=[],a=0;return t&&t.sheetRows&&(l=l.slice(0,t.sheetRows)),l}function t(t,r){return gn(function(t,r){switch(r.type){case"base64":return e(Y(t),r);case"binary":return e(t,r);case"buffer":return e(X&&Buffer.isBuffer(t)?t.toString("binary"):te(t),r);case"array":return e(gt(t),r)}throw new Error("Unrecognized type "+r.type)}(t,r),r)}return{to_workbook:function(e,r){return pn(t(e,r),r)},to_sheet:t,from_sheet:function(){var e=function(e,t,r,n,a){e.push(t),e.push(r+","+n),e.push('"'+a.replace(/"/g,'""')+'"')},t=function(e,t,r,n){e.push(t+","+r),e.push(1==t?'"'+n.replace(/"/g,'""')+'"':n)};return function(r){var n,a=[],i=fn(r["!ref"]),l=Array.isArray(r);e(a,"TABLE",0,1,"sheetjs"),e(a,"VECTORS",0,i.e.r-i.s.r+1,""),e(a,"TUPLES",0,i.e.c-i.s.c+1,""),e(a,"DATA",0,0,"");for(var o=i.s.r;o<=i.e.r;++o){t(a,-1,0,"BOT");for(var s=i.s.c;s<=i.e.c;++s){var c=sn({r:o,c:s});if(n=l?(r[o]||[])[s]:r[c])switch(n.t){case"n":var u=n.w;u||null==n.v||(u=n.v),null==u?n.f&&!n.F?t(a,1,0,"="+n.f):t(a,1,0,""):t(a,0,u,"V");break;case"b":t(a,0,n.v?1:0,n.v?"TRUE":"FALSE");break;case"s":t(a,1,0,isNaN(n.v)?n.v:'="'+n.v+'"');break;case"d":n.w||(n.w=Ge(n.z||pe[14],ot(mt(n.v)))),t(a,0,n.w,"V");break;default:t(a,1,0,"")}else t(a,1,0,"")}}t(a,-1,0,"EOD");return a.join("\r\n")}}()}}(),$a=function(){function e(e){return e.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function t(e,t){return gn(function(e,t){for(var r=e.split("\n"),n=-1,a=-1,i=0,l=[];i!==r.length;++i){var o=r[i].trim().split(":");if("cell"===o[0]){var s=on(o[1]);if(l.length<=s.r)for(n=l.length;n<=s.r;++n)l[n]||(l[n]=[]);switch(n=s.r,a=s.c,o[2]){case"t":l[n][a]=o[3].replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,"\n");break;case"v":l[n][a]=+o[3];break;case"vtf":var c=o[o.length-1];case"vtc":"nl"===o[3]?l[n][a]=!!+o[4]:l[n][a]=+o[4],"vtf"==o[2]&&(l[n][a]=[l[n][a],c])}}}return t&&t.sheetRows&&(l=l.slice(0,t.sheetRows)),l}(e,t),t)}var r=["socialcalc:version:1.5","MIME-Version: 1.0","Content-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave"].join("\n"),n=["--SocialCalcSpreadsheetControlSave","Content-type: text/plain; charset=UTF-8"].join("\n")+"\n",a=["# SocialCalc Spreadsheet Control Save","part:sheet"].join("\n"),i="--SocialCalcSpreadsheetControlSave--";function l(t){if(!t||!t["!ref"])return"";for(var r,n=[],a=[],i="",l=cn(t["!ref"]),o=Array.isArray(t),s=l.s.r;s<=l.e.r;++s)for(var c=l.s.c;c<=l.e.c;++c)if(i=sn({r:s,c:c}),(r=o?(t[s]||[])[c]:t[i])&&null!=r.v&&"z"!==r.t){switch(a=["cell",i,"t"],r.t){case"s":case"str":a.push(e(r.v));break;case"n":r.f?(a[2]="vtf",a[3]="n",a[4]=r.v,a[5]=e(r.f)):(a[2]="v",a[3]=r.v);break;case"b":a[2]="vt"+(r.f?"f":"c"),a[3]="nl",a[4]=r.v?"1":"0",a[5]=e(r.f||(r.v?"TRUE":"FALSE"));break;case"d":var u=ot(mt(r.v));a[2]="vtc",a[3]="nd",a[4]=""+u,a[5]=r.w||Ge(r.z||pe[14],u);break;case"e":continue}n.push(a.join(":"))}return n.push("sheet:c:"+(l.e.c-l.s.c+1)+":r:"+(l.e.r-l.s.r+1)+":tvf:1"),n.push("valueformat:1:text-wiki"),n.join("\n")}return{to_workbook:function(e,r){return pn(t(e,r),r)},to_sheet:t,from_sheet:function(e){return[r,n,a,n,l(e),i].join("\n")}}}(),Ga=function(){function e(e,t,r,n,a){a.raw?t[r][n]=e:""===e||("TRUE"===e?t[r][n]=!0:"FALSE"===e?t[r][n]=!1:isNaN(yt(e))?isNaN(Tt(e).getDate())?t[r][n]=e:t[r][n]=mt(e):t[r][n]=yt(e))}var t={44:",",9:"\t",59:";",124:"|"},r={44:3,9:2,59:1,124:0};function n(e){for(var n={},a=!1,i=0,l=0;i<e.length;++i)34==(l=e.charCodeAt(i))?a=!a:!a&&l in t&&(n[l]=(n[l]||0)+1);for(i in l=[],n)Object.prototype.hasOwnProperty.call(n,i)&&l.push([n[i],i]);if(!l.length)for(i in n=r)Object.prototype.hasOwnProperty.call(n,i)&&l.push([n[i],i]);return l.sort((function(e,t){return e[0]-t[0]||r[e[1]]-r[t[1]]})),t[l.pop()[1]]||44}function a(e,t){var r=t||{},a="";var i=r.dense?[]:{},l={s:{c:0,r:0},e:{c:0,r:0}};"sep="==e.slice(0,4)?13==e.charCodeAt(5)&&10==e.charCodeAt(6)?(a=e.charAt(4),e=e.slice(7)):13==e.charCodeAt(5)||10==e.charCodeAt(5)?(a=e.charAt(4),e=e.slice(6)):a=n(e.slice(0,1024)):a=r&&r.FS?r.FS:n(e.slice(0,1024));var o=0,s=0,c=0,u=0,f=0,h=a.charCodeAt(0),d=!1,p=0,m=e.charCodeAt(0);e=e.replace(/\r\n/gm,"\n");var g=null!=r.dateNF?function(e){var t="number"==typeof e?pe[e]:e;return t=t.replace(Qe,"(\\d+)"),new RegExp("^"+t+"$")}(r.dateNF):null;function v(){var t=e.slice(u,f),n={};if('"'==t.charAt(0)&&'"'==t.charAt(t.length-1)&&(t=t.slice(1,-1).replace(/""/g,'"')),0===t.length)n.t="z";else if(r.raw)n.t="s",n.v=t;else if(0===t.trim().length)n.t="s",n.v=t;else if(61==t.charCodeAt(0))34==t.charCodeAt(1)&&34==t.charCodeAt(t.length-1)?(n.t="s",n.v=t.slice(2,-1).replace(/""/g,'"')):1!=t.length?(n.t="n",n.f=t.slice(1)):(n.t="s",n.v=t);else if("TRUE"==t)n.t="b",n.v=!0;else if("FALSE"==t)n.t="b",n.v=!1;else if(isNaN(c=yt(t)))if(!isNaN(Tt(t).getDate())||g&&t.match(g)){n.z=r.dateNF||pe[14];var a=0;g&&t.match(g)&&(t=function(e,t,r){var n=-1,a=-1,i=-1,l=-1,o=-1,s=-1;(t.match(Qe)||[]).forEach((function(e,t){var c=parseInt(r[t+1],10);switch(e.toLowerCase().charAt(0)){case"y":n=c;break;case"d":i=c;break;case"h":l=c;break;case"s":s=c;break;case"m":l>=0?o=c:a=c}})),s>=0&&-1==o&&a>=0&&(o=a,a=-1);var c=(""+(n>=0?n:(new Date).getFullYear())).slice(-4)+"-"+("00"+(a>=1?a:1)).slice(-2)+"-"+("00"+(i>=1?i:1)).slice(-2);7==c.length&&(c="0"+c),8==c.length&&(c="20"+c);var u=("00"+(l>=0?l:0)).slice(-2)+":"+("00"+(o>=0?o:0)).slice(-2)+":"+("00"+(s>=0?s:0)).slice(-2);return-1==l&&-1==o&&-1==s?c:-1==n&&-1==a&&-1==i?u:c+"T"+u}(0,r.dateNF,t.match(g)||[]),a=1),r.cellDates?(n.t="d",n.v=mt(t,a)):(n.t="n",n.v=ot(mt(t,a))),!1!==r.cellText&&(n.w=Ge(n.z,n.v instanceof Date?ot(n.v):n.v)),r.cellNF||delete n.z}else n.t="s",n.v=t;else n.t="n",!1!==r.cellText&&(n.w=t),n.v=c;if("z"==n.t||(r.dense?(i[o]||(i[o]=[]),i[o][s]=n):i[sn({c:s,r:o})]=n),u=f+1,m=e.charCodeAt(u),l.e.c<s&&(l.e.c=s),l.e.r<o&&(l.e.r=o),p==h)++s;else if(s=0,++o,r.sheetRows&&r.sheetRows<=o)return!0}e:for(;f<e.length;++f)switch(p=e.charCodeAt(f)){case 34:34===m&&(d=!d);break;case h:case 10:case 13:if(!d&&v())break e}return f-u>0&&v(),i["!ref"]=un(l),i}function i(t,r){return r&&r.PRN?r.FS||"sep="==t.slice(0,4)||t.indexOf("\t")>=0||t.indexOf(",")>=0||t.indexOf(";")>=0?a(t,r):gn(function(t,r){var n=r||{},a=[];if(!t||0===t.length)return a;for(var i=t.split(/[\r\n]/),l=i.length-1;l>=0&&0===i[l].length;)--l;for(var o=10,s=0,c=0;c<=l;++c)-1==(s=i[c].indexOf(" "))?s=i[c].length:s++,o=Math.max(o,s);for(c=0;c<=l;++c){a[c]=[];var u=0;for(e(i[c].slice(0,o).trim(),a,c,u,n),u=1;u<=(i[c].length-o)/10+1;++u)e(i[c].slice(o+10*(u-1),o+10*u).trim(),a,c,u,n)}return n.sheetRows&&(a=a.slice(0,n.sheetRows)),a}(t,r),r):a(t,r)}function l(e,t){var r="",n="string"==t.type?[0,0,0,0]:Zo(e,t);switch(t.type){case"base64":r=Y(e);break;case"binary":case"string":r=e;break;case"buffer":r=65001==t.codepage?e.toString("utf8"):t.codepage&&"undefined"!==typeof H?H.utils.decode(t.codepage,e):X&&Buffer.isBuffer(e)?e.toString("binary"):te(e);break;case"array":r=gt(e);break;default:throw new Error("Unrecognized type "+t.type)}return 239==n[0]&&187==n[1]&&191==n[2]?r=jt(r.slice(3)):"string"!=t.type&&"buffer"!=t.type&&65001==t.codepage?r=jt(r):"binary"==t.type&&"undefined"!==typeof H&&t.codepage&&(r=H.utils.decode(t.codepage,H.utils.encode(28591,r))),"socialcalc:version:"==r.slice(0,19)?$a.to_sheet("string"==t.type?r:jt(r),t):i(r,t)}return{to_workbook:function(e,t){return pn(l(e,t),t)},to_sheet:l,from_sheet:function(e){for(var t,r=[],n=fn(e["!ref"]),a=Array.isArray(e),i=n.s.r;i<=n.e.r;++i){for(var l=[],o=n.s.c;o<=n.e.c;++o){var s=sn({r:i,c:o});if((t=a?(e[i]||[])[o]:e[s])&&null!=t.v){for(var c=(t.w||(dn(t),t.w)||"").slice(0,10);c.length<10;)c+=" ";l.push(c+(0===o?" ":""))}else l.push("          ")}r.push(l.join(""))}return r.join("\n")}}}();var Ka=function(){function e(e,t,r){if(e){Kr(e,e.l||0);for(var n=r.Enum||v;e.l<e.length;){var a=e.read_shift(2),i=n[a]||n[65535],l=e.read_shift(2),o=e.l+l,s=i.f&&i.f(e,l,r);if(e.l=o,t(s,i,a))return}}}function t(t,r){if(!t)return t;var n=r||{};var a=n.dense?[]:{},i="Sheet1",l="",o=0,s={},c=[],u=[],f={s:{r:0,c:0},e:{r:0,c:0}},h=n.sheetRows||0;if(0==t[2]&&(8==t[3]||9==t[3])&&t.length>=16&&5==t[14]&&108===t[15])throw new Error("Unsupported Works 3 for Mac file");if(2==t[2])n.Enum=v,e(t,(function(e,t,r){switch(r){case 0:n.vers=e,e>=4096&&(n.qpro=!0);break;case 6:f=e;break;case 204:e&&(l=e);break;case 222:l=e;break;case 15:case 51:n.qpro||(e[1].v=e[1].v.slice(1));case 13:case 14:case 16:14==r&&112==(112&e[2])&&(15&e[2])>1&&(15&e[2])<15&&(e[1].z=n.dateNF||pe[14],n.cellDates&&(e[1].t="d",e[1].v=ft(e[1].v))),n.qpro&&e[3]>o&&(a["!ref"]=un(f),s[i]=a,c.push(i),a=n.dense?[]:{},f={s:{r:0,c:0},e:{r:0,c:0}},o=e[3],i=l||"Sheet"+(o+1),l="");var u=n.dense?(a[e[0].r]||[])[e[0].c]:a[sn(e[0])];if(u){u.t=e[1].t,u.v=e[1].v,null!=e[1].z&&(u.z=e[1].z),null!=e[1].f&&(u.f=e[1].f);break}n.dense?(a[e[0].r]||(a[e[0].r]=[]),a[e[0].r][e[0].c]=e[1]):a[sn(e[0])]=e[1]}}),n);else{if(26!=t[2]&&14!=t[2])throw new Error("Unrecognized LOTUS BOF "+t[2]);n.Enum=b,14==t[2]&&(n.qpro=!0,t.l=0),e(t,(function(e,t,r){switch(r){case 204:i=e;break;case 22:e[1].v=e[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(e[3]>o&&(a["!ref"]=un(f),s[i]=a,c.push(i),a=n.dense?[]:{},f={s:{r:0,c:0},e:{r:0,c:0}},o=e[3],i="Sheet"+(o+1)),h>0&&e[0].r>=h)break;n.dense?(a[e[0].r]||(a[e[0].r]=[]),a[e[0].r][e[0].c]=e[1]):a[sn(e[0])]=e[1],f.e.c<e[0].c&&(f.e.c=e[0].c),f.e.r<e[0].r&&(f.e.r=e[0].r);break;case 27:e[14e3]&&(u[e[14e3][0]]=e[14e3][1]);break;case 1537:u[e[0]]=e[1],e[0]==o&&(i=e[1])}}),n)}if(a["!ref"]=un(f),s[l||i]=a,c.push(l||i),!u.length)return{SheetNames:c,Sheets:s};for(var d={},p=[],m=0;m<u.length;++m)s[c[m]]?(p.push(u[m]||c[m]),d[u[m]]=s[u[m]]||s[c[m]]):(p.push(u[m]),d[u[m]]={"!ref":"A1"});return{SheetNames:p,Sheets:d}}function r(e,t,r){var n=[{c:0,r:0},{t:"n",v:0},0,0];return r.qpro&&20768!=r.vers?(n[0].c=e.read_shift(1),n[3]=e.read_shift(1),n[0].r=e.read_shift(2),e.l+=2):(n[2]=e.read_shift(1),n[0].c=e.read_shift(2),n[0].r=e.read_shift(2)),n}function n(e,t,n){var a=e.l+t,i=r(e,0,n);if(i[1].t="s",20768==n.vers){e.l++;var l=e.read_shift(1);return i[1].v=e.read_shift(l,"utf8"),i}return n.qpro&&e.l++,i[1].v=e.read_shift(a-e.l,"cstr"),i}function a(e,t,r){var n=Xr(7+r.length);n.write_shift(1,255),n.write_shift(2,t),n.write_shift(2,e),n.write_shift(1,39);for(var a=0;a<n.length;++a){var i=r.charCodeAt(a);n.write_shift(1,i>=128?95:i)}return n.write_shift(1,0),n}function i(e,t,r){var n=Xr(7);return n.write_shift(1,255),n.write_shift(2,t),n.write_shift(2,e),n.write_shift(2,r,"i"),n}function l(e,t,r){var n=Xr(13);return n.write_shift(1,255),n.write_shift(2,t),n.write_shift(2,e),n.write_shift(8,r,"f"),n}function o(e,t,r){var n=32768&t;return t=(n?e:0)+((t&=-32769)>=8192?t-16384:t),(n?"":"$")+(r?ln(t):nn(t))}var s={51:["FALSE",0],52:["TRUE",0],70:["LEN",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],111:["T",1]},c=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function u(e){var t=[{c:0,r:0},{t:"n",v:0},0];return t[0].r=e.read_shift(2),t[3]=e[e.l++],t[0].c=e[e.l++],t}function f(e,t,r,n){var a=Xr(6+n.length);a.write_shift(2,e),a.write_shift(1,r),a.write_shift(1,t),a.write_shift(1,39);for(var i=0;i<n.length;++i){var l=n.charCodeAt(i);a.write_shift(1,l>=128?95:l)}return a.write_shift(1,0),a}function h(e,t){var r=u(e),n=e.read_shift(4),a=e.read_shift(4),i=e.read_shift(2);if(65535==i)return 0===n&&3221225472===a?(r[1].t="e",r[1].v=15):0===n&&3489660928===a?(r[1].t="e",r[1].v=42):r[1].v=0,r;var l=32768&i;return i=(32767&i)-16446,r[1].v=(1-2*l)*(a*Math.pow(2,i+32)+n*Math.pow(2,i)),r}function d(e,t,r,n){var a=Xr(14);if(a.write_shift(2,e),a.write_shift(1,r),a.write_shift(1,t),0==n)return a.write_shift(4,0),a.write_shift(4,0),a.write_shift(2,65535),a;var i,l=0,o=0,s=0;return n<0&&(l=1,n=-n),o=0|Math.log2(n),0==(2147483648&(s=(n/=Math.pow(2,o-31))>>>0))&&(++o,s=(n/=2)>>>0),n-=s,s|=2147483648,s>>>=0,i=(n*=Math.pow(2,32))>>>0,a.write_shift(4,i),a.write_shift(4,s),o+=16383+(l?32768:0),a.write_shift(2,o),a}function p(e,t){var r=u(e),n=e.read_shift(8,"f");return r[1].v=n,r}function m(e,t){return 0==e[e.l+t-1]?e.read_shift(t,"cstr"):""}function g(e,t){var r=Xr(5+e.length);r.write_shift(2,14e3),r.write_shift(2,t);for(var n=0;n<e.length;++n){var a=e.charCodeAt(n);r[r.l++]=a>127?95:a}return r[r.l++]=0,r}var v={0:{n:"BOF",f:ya},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f:function(e,t,r){var n={s:{c:0,r:0},e:{c:0,r:0}};return 8==t&&r.qpro?(n.s.c=e.read_shift(1),e.l++,n.s.r=e.read_shift(2),n.e.c=e.read_shift(1),e.l++,n.e.r=e.read_shift(2),n):(n.s.c=e.read_shift(2),n.s.r=e.read_shift(2),12==t&&r.qpro&&(e.l+=2),n.e.c=e.read_shift(2),n.e.r=e.read_shift(2),12==t&&r.qpro&&(e.l+=2),65535==n.s.c&&(n.s.c=n.e.c=n.s.r=n.e.r=0),n)}},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:function(e,t,n){var a=r(e,0,n);return a[1].v=e.read_shift(2,"i"),a}},14:{n:"NUMBER",f:function(e,t,n){var a=r(e,0,n);return a[1].v=e.read_shift(8,"f"),a}},15:{n:"LABEL",f:n},16:{n:"FORMULA",f:function(e,t,n){var a=e.l+t,i=r(e,0,n);if(i[1].v=e.read_shift(8,"f"),n.qpro)e.l=a;else{var l=e.read_shift(2);!function(e,t){Kr(e,0);var r=[],n=0,a="",i="",l="",u="";for(;e.l<e.length;){var f=e[e.l++];switch(f){case 0:r.push(e.read_shift(8,"f"));break;case 1:i=o(t[0].c,e.read_shift(2),!0),a=o(t[0].r,e.read_shift(2),!1),r.push(i+a);break;case 2:var h=o(t[0].c,e.read_shift(2),!0),d=o(t[0].r,e.read_shift(2),!1);i=o(t[0].c,e.read_shift(2),!0),a=o(t[0].r,e.read_shift(2),!1),r.push(h+d+":"+i+a);break;case 3:if(e.l<e.length)return void console.error("WK1 premature formula end");break;case 4:r.push("("+r.pop()+")");break;case 5:r.push(e.read_shift(2));break;case 6:for(var p="";f=e[e.l++];)p+=String.fromCharCode(f);r.push('"'+p.replace(/"/g,'""')+'"');break;case 8:r.push("-"+r.pop());break;case 23:r.push("+"+r.pop());break;case 22:r.push("NOT("+r.pop()+")");break;case 20:case 21:u=r.pop(),l=r.pop(),r.push(["AND","OR"][f-20]+"("+l+","+u+")");break;default:if(f<32&&c[f])u=r.pop(),l=r.pop(),r.push(l+c[f]+u);else{if(!s[f])return f<=7?console.error("WK1 invalid opcode "+f.toString(16)):f<=24?console.error("WK1 unsupported op "+f.toString(16)):f<=30?console.error("WK1 invalid opcode "+f.toString(16)):f<=115?console.error("WK1 unsupported function opcode "+f.toString(16)):console.error("WK1 unrecognized opcode "+f.toString(16));if(69==(n=s[f][1])&&(n=e[e.l++]),n>r.length)return void console.error("WK1 bad formula parse 0x"+f.toString(16)+":|"+r.join("|")+"|");var m=r.slice(-n);r.length-=n,r.push(s[f][0]+"("+m.join(",")+")")}}}1==r.length?t[1].f=""+r[0]:console.error("WK1 bad formula parse |"+r.join("|")+"|")}(e.slice(e.l,e.l+l),i),e.l+=l}return i}},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:n},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:m},222:{n:"SHEETNAMELP",f:function(e,t){var r=e[e.l++];r>t-1&&(r=t-1);for(var n="";n.length<r;)n+=String.fromCharCode(e[e.l++]);return n}},65535:{n:""}},b={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:function(e,t){var r=u(e);return r[1].t="s",r[1].v=e.read_shift(t-4,"cstr"),r}},23:{n:"NUMBER17",f:h},24:{n:"NUMBER18",f:function(e,t){var r=u(e);r[1].v=e.read_shift(2);var n=r[1].v>>1;if(1&r[1].v)switch(7&n){case 0:n=5e3*(n>>3);break;case 1:n=500*(n>>3);break;case 2:n=(n>>3)/20;break;case 3:n=(n>>3)/200;break;case 4:n=(n>>3)/2e3;break;case 5:n=(n>>3)/2e4;break;case 6:n=(n>>3)/16;break;case 7:n=(n>>3)/64}return r[1].v=n,r}},25:{n:"FORMULA19",f:function(e,t){var r=h(e);return e.l+=t-14,r}},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:function(e,t){for(var r={},n=e.l+t;e.l<n;){var a=e.read_shift(2);if(14e3==a){for(r[a]=[0,""],r[a][0]=e.read_shift(2);e[e.l];)r[a][1]+=String.fromCharCode(e[e.l]),e.l++;e.l++}}return r}},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:function(e,t){var r=u(e),n=e.read_shift(4);return r[1].v=n>>6,r}},38:{n:"??"},39:{n:"NUMBER27",f:p},40:{n:"FORMULA28",f:function(e,t){var r=p(e);return e.l+=t-10,r}},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:m},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:function(e,t,r){if(r.qpro&&!(t<21)){var n=e.read_shift(1);return e.l+=17,e.l+=1,e.l+=2,[n,e.read_shift(t-21,"cstr")]}}},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}};return{sheet_to_wk1:function(e,t){var r=t||{};if(+r.codepage>=0&&U(+r.codepage),"string"==r.type)throw new Error("Cannot write WK1 to JS string");var n=Qr(),o=fn(e["!ref"]),s=Array.isArray(e),c=[];uo(n,0,function(e){var t=Xr(2);return t.write_shift(2,e),t}(1030)),uo(n,6,function(e){var t=Xr(8);return t.write_shift(2,e.s.c),t.write_shift(2,e.s.r),t.write_shift(2,e.e.c),t.write_shift(2,e.e.r),t}(o));for(var u=Math.min(o.e.r,8191),f=o.s.r;f<=u;++f)for(var h=nn(f),d=o.s.c;d<=o.e.c;++d){f===o.s.r&&(c[d]=ln(d));var p=c[d]+h,m=s?(e[f]||[])[d]:e[p];if(m&&"z"!=m.t)if("n"==m.t)(0|m.v)==m.v&&m.v>=-32768&&m.v<=32767?uo(n,13,i(f,d,m.v)):uo(n,14,l(f,d,m.v));else uo(n,15,a(f,d,dn(m).slice(0,239)))}return uo(n,1),n.end()},book_to_wk3:function(e,t){var r=t||{};if(+r.codepage>=0&&U(+r.codepage),"string"==r.type)throw new Error("Cannot write WK3 to JS string");var n=Qr();uo(n,0,function(e){var t=Xr(26);t.write_shift(2,4096),t.write_shift(2,4),t.write_shift(4,0);for(var r=0,n=0,a=0,i=0;i<e.SheetNames.length;++i){var l=e.SheetNames[i],o=e.Sheets[l];if(o&&o["!ref"]){++a;var s=cn(o["!ref"]);r<s.e.r&&(r=s.e.r),n<s.e.c&&(n=s.e.c)}}r>8191&&(r=8191);return t.write_shift(2,r),t.write_shift(1,a),t.write_shift(1,n),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(1,1),t.write_shift(1,2),t.write_shift(4,0),t.write_shift(4,0),t}(e));for(var a=0,i=0;a<e.SheetNames.length;++a)(e.Sheets[e.SheetNames[a]]||{})["!ref"]&&uo(n,27,g(e.SheetNames[a],i++));var l=0;for(a=0;a<e.SheetNames.length;++a){var o=e.Sheets[e.SheetNames[a]];if(o&&o["!ref"]){for(var s=fn(o["!ref"]),c=Array.isArray(o),u=[],h=Math.min(s.e.r,8191),p=s.s.r;p<=h;++p)for(var m=nn(p),v=s.s.c;v<=s.e.c;++v){p===s.s.r&&(u[v]=ln(v));var b=u[v]+m,y=c?(o[p]||[])[v]:o[b];if(y&&"z"!=y.t)if("n"==y.t)uo(n,23,d(p,v,l,y.v));else uo(n,22,f(p,v,l,dn(y).slice(0,239)))}++l}}return uo(n,1),n.end()},to_workbook:function(e,r){switch(r.type){case"base64":return t(Z(Y(e)),r);case"binary":return t(Z(e),r);case"buffer":case"array":return t(e,r)}throw"Unsupported type "+r.type}}}();var Ya=/^\s|\s$|[\t\n\r]/;function Xa(e,t){if(!t.bookSST)return"";var r=[xt];r[r.length]=Vt("sst",null,{xmlns:ir[0],count:e.Count,uniqueCount:e.Unique});for(var n=0;n!=e.length;++n)if(null!=e[n]){var a=e[n],i="<si>";a.r?i+=a.r:(i+="<t",a.t||(a.t=""),a.t.match(Ya)&&(i+=' xml:space="preserve"'),i+=">"+Nt(a.t)+"</t>"),i+="</si>",r[r.length]=i}return r.length>2&&(r[r.length]="</sst>",r[1]=r[1].replace("/>",">")),r.join("")}var Qa=function(e,t){var r=!1;return null==t&&(r=!0,t=Xr(15+4*e.t.length)),t.write_shift(1,0),yn(e.t,t),r?t.slice(0,t.l):t};function qa(e){var t=Qr();qr(t,159,function(e,t){return t||(t=Xr(8)),t.write_shift(4,e.Count),t.write_shift(4,e.Unique),t}(e));for(var r=0;r<e.length;++r)qr(t,19,Qa(e[r]));return qr(t,160),t.end()}function Ja(e){if("undefined"!==typeof H)return H.utils.encode(L,e);for(var t=[],r=e.split(""),n=0;n<r.length;++n)t[n]=r[n].charCodeAt(0);return t}function Za(e){var t,r,n=0,a=Ja(e),i=a.length+1;for((t=q(i))[0]=a.length,r=1;r!=i;++r)t[r]=a[r-1];for(r=i-1;r>=0;--r)n=((0===(16384&n)?0:1)|n<<1&32767)^t[r];return 52811^n}var ei=function(){function e(e,r){switch(r.type){case"base64":return t(Y(e),r);case"binary":return t(e,r);case"buffer":return t(X&&Buffer.isBuffer(e)?e.toString("binary"):te(e),r);case"array":return t(gt(e),r)}throw new Error("Unrecognized type "+r.type)}function t(e,t){var r=(t||{}).dense?[]:{},n=e.match(/\\trowd.*?\\row\b/g);if(!n.length)throw new Error("RTF missing table");var a={s:{c:0,r:0},e:{c:0,r:n.length-1}};return n.forEach((function(e,t){Array.isArray(r)&&(r[t]=[]);for(var n,i=/\\\w+\b/g,l=0,o=-1;n=i.exec(e);){if("\\cell"===n[0]){var s=e.slice(l,i.lastIndex-n[0].length);if(" "==s[0]&&(s=s.slice(1)),++o,s.length){var c={v:s,t:"s"};Array.isArray(r)?r[t][o]=c:r[sn({r:t,c:o})]=c}}l=i.lastIndex}o>a.e.c&&(a.e.c=o)})),r["!ref"]=un(a),r}return{to_workbook:function(t,r){return pn(e(t,r),r)},to_sheet:e,from_sheet:function(e){for(var t,r=["{\\rtf1\\ansi"],n=fn(e["!ref"]),a=Array.isArray(e),i=n.s.r;i<=n.e.r;++i){r.push("\\trowd\\trautofit1");for(var l=n.s.c;l<=n.e.c;++l)r.push("\\cellx"+(l+1));for(r.push("\\pard\\intbl"),l=n.s.c;l<=n.e.c;++l){var o=sn({r:i,c:l});(t=a?(e[i]||[])[l]:e[o])&&(null!=t.v||t.f&&!t.F)&&(r.push(" "+(t.w||(dn(t),t.w))),r.push("\\cell"))}r.push("\\pard\\intbl\\row")}return r.join("")+"}"}}}();function ti(e){for(var t=0,r=1;3!=t;++t)r=256*r+(e[t]>255?255:e[t]<0?0:e[t]);return r.toString(16).toUpperCase().slice(1)}var ri=6;function ni(e){return Math.floor((e+Math.round(128/ri)/256)*ri)}function ai(e){return Math.floor((e-5)/ri*100+.5)/100}function ii(e){return Math.round((e*ri+5)/ri*256)/256}function li(e){e.width?(e.wpx=ni(e.width),e.wch=ai(e.wpx),e.MDW=ri):e.wpx?(e.wch=ai(e.wpx),e.width=ii(e.wch),e.MDW=ri):"number"==typeof e.wch&&(e.width=ii(e.wch),e.wpx=ni(e.width),e.MDW=ri),e.customWidth&&delete e.customWidth}var oi=96;function si(e){return 96*e/oi}function ci(e){return e*oi/96}function ui(e,t){var r,n=[xt,Vt("styleSheet",null,{xmlns:ir[0],"xmlns:vt":rr})];return e.SSF&&null!=(r=function(e){var t=["<numFmts>"];return[[5,8],[23,26],[41,44],[50,392]].forEach((function(r){for(var n=r[0];n<=r[1];++n)null!=e[n]&&(t[t.length]=Vt("numFmt",null,{numFmtId:n,formatCode:Nt(e[n])}))})),1===t.length?"":(t[t.length]="</numFmts>",t[0]=Vt("numFmts",null,{count:t.length-2}).replace("/>",">"),t.join(""))}(e.SSF))&&(n[n.length]=r),n[n.length]='<fonts count="1"><font><sz val="12"/><color theme="1"/><name val="Calibri"/><family val="2"/><scheme val="minor"/></font></fonts>',n[n.length]='<fills count="2"><fill><patternFill patternType="none"/></fill><fill><patternFill patternType="gray125"/></fill></fills>',n[n.length]='<borders count="1"><border><left/><right/><top/><bottom/><diagonal/></border></borders>',n[n.length]='<cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0"/></cellStyleXfs>',(r=function(e){var t=[];return t[t.length]=Vt("cellXfs",null),e.forEach((function(e){t[t.length]=Vt("xf",null,e)})),t[t.length]="</cellXfs>",2===t.length?"":(t[0]=Vt("cellXfs",null,{count:t.length-2}).replace("/>",">"),t.join(""))}(t.cellXfs))&&(n[n.length]=r),n[n.length]='<cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0"/></cellStyles>',n[n.length]='<dxfs count="0"/>',n[n.length]='<tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4"/>',n.length>2&&(n[n.length]="</styleSheet>",n[1]=n[1].replace("/>",">")),n.join("")}function fi(e,t,r){r||(r=Xr(6+4*t.length)),r.write_shift(2,e),yn(t,r);var n=r.length>r.l?r.slice(0,r.l):r;return null==r.l&&(r.l=r.length),n}function hi(e,t){t||(t=Xr(153)),t.write_shift(2,20*e.sz),function(e,t){t||(t=Xr(2));var r=(e.italic?2:0)|(e.strike?8:0)|(e.outline?16:0)|(e.shadow?32:0)|(e.condense?64:0)|(e.extend?128:0);t.write_shift(1,r),t.write_shift(1,0)}(e,t),t.write_shift(2,e.bold?700:400);var r=0;"superscript"==e.vertAlign?r=1:"subscript"==e.vertAlign&&(r=2),t.write_shift(2,r),t.write_shift(1,e.underline||0),t.write_shift(1,e.family||0),t.write_shift(1,e.charset||0),t.write_shift(1,0),Hn(e.color,t);var n=0;return"major"==e.scheme&&(n=1),"minor"==e.scheme&&(n=2),t.write_shift(1,n),yn(e.name,t),t.length>t.l?t.slice(0,t.l):t}var di,pi=["none","solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"],mi=Yr;function gi(e,t){t||(t=Xr(84)),di||(di=at(pi));var r=di[e.patternType];null==r&&(r=40),t.write_shift(4,r);var n=0;if(40!=r)for(Hn({auto:1},t),Hn({auto:1},t);n<12;++n)t.write_shift(4,0);else{for(;n<4;++n)t.write_shift(4,0);for(;n<12;++n)t.write_shift(4,0)}return t.length>t.l?t.slice(0,t.l):t}function vi(e,t,r){r||(r=Xr(16)),r.write_shift(2,t||0),r.write_shift(2,e.numFmtId||0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(1,0),r.write_shift(1,0);return r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r}function bi(e,t){return t||(t=Xr(10)),t.write_shift(1,0),t.write_shift(1,0),t.write_shift(4,0),t.write_shift(4,0),t}var yi=Yr;function wi(e){var t;qr(e,613,vn(1)),qr(e,46,(t||(t=Xr(51)),t.write_shift(1,0),bi(0,t),bi(0,t),bi(0,t),bi(0,t),bi(0,t),t.length>t.l?t.slice(0,t.l):t)),qr(e,614)}function Ti(e){var t,r;qr(e,619,vn(1)),qr(e,48,(t={xfId:0,builtinId:0,name:"Normal"},r||(r=Xr(52)),r.write_shift(4,t.xfId),r.write_shift(2,1),r.write_shift(1,+t.builtinId),r.write_shift(1,0),Rn(t.name||"",r),r.length>r.l?r.slice(0,r.l):r)),qr(e,620)}function Ei(e){qr(e,508,function(e,t,r){var n=Xr(2052);return n.write_shift(4,e),Rn(t,n),Rn(r,n),n.length>n.l?n.slice(0,n.l):n}(0,"TableStyleMedium9","PivotStyleMedium4")),qr(e,509)}function Si(e,t){var r=Qr();return qr(r,278),function(e,t){if(t){var r=0;[[5,8],[23,26],[41,44],[50,392]].forEach((function(e){for(var n=e[0];n<=e[1];++n)null!=t[n]&&++r})),0!=r&&(qr(e,615,vn(r)),[[5,8],[23,26],[41,44],[50,392]].forEach((function(r){for(var n=r[0];n<=r[1];++n)null!=t[n]&&qr(e,44,fi(n,t[n]))})),qr(e,616))}}(r,e.SSF),function(e){qr(e,611,vn(1)),qr(e,43,hi({sz:12,color:{theme:1},name:"Calibri",family:2,scheme:"minor"})),qr(e,612)}(r),function(e){qr(e,603,vn(2)),qr(e,45,gi({patternType:"none"})),qr(e,45,gi({patternType:"gray125"})),qr(e,604)}(r),wi(r),function(e){qr(e,626,vn(1)),qr(e,47,vi({numFmtId:0,fontId:0,fillId:0,borderId:0},65535)),qr(e,627)}(r),function(e,t){qr(e,617,vn(t.length)),t.forEach((function(t){qr(e,47,vi(t,0))})),qr(e,618)}(r,t.cellXfs),Ti(r),function(e){qr(e,505,vn(0)),qr(e,506)}(r),Ei(r),qr(r,279),r.end()}function xi(e,t){if(t&&t.themeXLSX)return t.themeXLSX;if(e&&"string"==typeof e.raw)return e.raw;var r=[xt];return r[r.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',r[r.length]="<a:themeElements>",r[r.length]='<a:clrScheme name="Office">',r[r.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',r[r.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',r[r.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',r[r.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',r[r.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',r[r.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',r[r.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',r[r.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',r[r.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',r[r.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',r[r.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',r[r.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',r[r.length]="</a:clrScheme>",r[r.length]='<a:fontScheme name="Office">',r[r.length]="<a:majorFont>",r[r.length]='<a:latin typeface="Cambria"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="\uff2d\uff33 \uff30\u30b4\u30b7\u30c3\u30af"/>',r[r.length]='<a:font script="Hang" typeface="\ub9d1\uc740 \uace0\ub515"/>',r[r.length]='<a:font script="Hans" typeface="\u5b8b\u4f53"/>',r[r.length]='<a:font script="Hant" typeface="\u65b0\u7d30\u660e\u9ad4"/>',r[r.length]='<a:font script="Arab" typeface="Times New Roman"/>',r[r.length]='<a:font script="Hebr" typeface="Times New Roman"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="MoolBoran"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Times New Roman"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:majorFont>",r[r.length]="<a:minorFont>",r[r.length]='<a:latin typeface="Calibri"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="\uff2d\uff33 \uff30\u30b4\u30b7\u30c3\u30af"/>',r[r.length]='<a:font script="Hang" typeface="\ub9d1\uc740 \uace0\ub515"/>',r[r.length]='<a:font script="Hans" typeface="\u5b8b\u4f53"/>',r[r.length]='<a:font script="Hant" typeface="\u65b0\u7d30\u660e\u9ad4"/>',r[r.length]='<a:font script="Arab" typeface="Arial"/>',r[r.length]='<a:font script="Hebr" typeface="Arial"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="DaunPenh"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Arial"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:minorFont>",r[r.length]="</a:fontScheme>",r[r.length]='<a:fmtScheme name="Office">',r[r.length]="<a:fillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="1"/>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="0"/>',r[r.length]="</a:gradFill>",r[r.length]="</a:fillStyleLst>",r[r.length]="<a:lnStyleLst>",r[r.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]="</a:lnStyleLst>",r[r.length]="<a:effectStyleLst>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',r[r.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',r[r.length]="</a:effectStyle>",r[r.length]="</a:effectStyleLst>",r[r.length]="<a:bgFillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]="</a:bgFillStyleLst>",r[r.length]="</a:fmtScheme>",r[r.length]="</a:themeElements>",r[r.length]="<a:objectDefaults>",r[r.length]="<a:spDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',r[r.length]="</a:spDef>",r[r.length]="<a:lnDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',r[r.length]="</a:lnDef>",r[r.length]="</a:objectDefaults>",r[r.length]="<a:extraClrSchemeLst/>",r[r.length]="</a:theme>",r.join("")}function ki(){var e=Qr();return qr(e,332),qr(e,334,vn(1)),qr(e,335,function(e){var t=Xr(12+2*e.name.length);return t.write_shift(4,e.flags),t.write_shift(4,e.version),yn(e.name,t),t.slice(0,t.l)}({name:"XLDAPR",version:12e4,flags:3496657072})),qr(e,336),qr(e,339,function(e,t){var r=Xr(8+2*t.length);return r.write_shift(4,e),yn(t,r),r.slice(0,r.l)}(1,"XLDAPR")),qr(e,52),qr(e,35,vn(514)),qr(e,4096,vn(0)),qr(e,4097,wa(1)),qr(e,36),qr(e,53),qr(e,340),qr(e,337,function(e,t){var r=Xr(8);return r.write_shift(4,e),r.write_shift(4,t?1:0),r}(1,!0)),qr(e,51,function(e){var t=Xr(4+8*e.length);t.write_shift(4,e.length);for(var r=0;r<e.length;++r)t.write_shift(4,e[r][0]),t.write_shift(4,e[r][1]);return t}([[1,0]])),qr(e,338),qr(e,333),e.end()}function _i(){var e=[xt];return e.push('<metadata xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:xlrd="http://schemas.microsoft.com/office/spreadsheetml/2017/richdata" xmlns:xda="http://schemas.microsoft.com/office/spreadsheetml/2017/dynamicarray">\n  <metadataTypes count="1">\n    <metadataType name="XLDAPR" minSupportedVersion="120000" copy="1" pasteAll="1" pasteValues="1" merge="1" splitFirst="1" rowColShift="1" clearFormats="1" clearComments="1" assign="1" coerce="1" cellMeta="1"/>\n  </metadataTypes>\n  <futureMetadata name="XLDAPR" count="1">\n    <bk>\n      <extLst>\n        <ext uri="{bdbb8cdc-fa1e-496e-a857-3c3f30c029c3}">\n          <xda:dynamicArrayProperties fDynamic="1" fCollapsed="0"/>\n        </ext>\n      </extLst>\n    </bk>\n  </futureMetadata>\n  <cellMetadata count="1">\n    <bk>\n      <rc t="1" v="0"/>\n    </bk>\n  </cellMetadata>\n</metadata>'),e.join("")}var Ci=1024;function Ai(e,t){for(var r=[21600,21600],n=["m0,0l0",r[1],r[0],r[1],r[0],"0xe"].join(","),a=[Vt("xml",null,{"xmlns:v":fr,"xmlns:o":lr,"xmlns:x":or,"xmlns:mv":ur}).replace(/\/>/,">"),Vt("o:shapelayout",Vt("o:idmap",null,{"v:ext":"edit",data:e}),{"v:ext":"edit"}),Vt("v:shapetype",[Vt("v:stroke",null,{joinstyle:"miter"}),Vt("v:path",null,{gradientshapeok:"t","o:connecttype":"rect"})].join(""),{id:"_x0000_t202","o:spt":202,coordsize:r.join(","),path:n})];Ci<1e3*e;)Ci+=1e3;return t.forEach((function(e){var t=on(e[0]),r={color2:"#BEFF82",type:"gradient"};"gradient"==r.type&&(r.angle="-180");var n="gradient"==r.type?Vt("o:fill",null,{type:"gradientUnscaled","v:ext":"view"}):null,i=Vt("v:fill",n,r);++Ci,a=a.concat(["<v:shape"+Wt({id:"_x0000_s"+Ci,type:"#_x0000_t202",style:"position:absolute; margin-left:80pt;margin-top:5pt;width:104pt;height:64pt;z-index:10"+(e[1].hidden?";visibility:hidden":""),fillcolor:"#ECFAD4",strokecolor:"#edeaa1"})+">",i,Vt("v:shadow",null,{on:"t",obscured:"t"}),Vt("v:path",null,{"o:connecttype":"none"}),'<v:textbox><div style="text-align:left"></div></v:textbox>','<x:ClientData ObjectType="Note">',"<x:MoveWithCells/>","<x:SizeWithCells/>",Ht("x:Anchor",[t.c+1,0,t.r+1,0,t.c+3,20,t.r+5,20].join(",")),Ht("x:AutoFill","False"),Ht("x:Row",String(t.r)),Ht("x:Column",String(t.c)),e[1].hidden?"":"<x:Visible/>","</x:ClientData>","</v:shape>"])})),a.push("</xml>"),a.join("")}function Ni(e){var t=[xt,Vt("comments",null,{xmlns:ir[0]})],r=[];return t.push("<authors>"),e.forEach((function(e){e[1].forEach((function(e){var n=Nt(e.a);-1==r.indexOf(n)&&(r.push(n),t.push("<author>"+n+"</author>")),e.T&&e.ID&&-1==r.indexOf("tc="+e.ID)&&(r.push("tc="+e.ID),t.push("<author>tc="+e.ID+"</author>"))}))})),0==r.length&&(r.push("SheetJ5"),t.push("<author>SheetJ5</author>")),t.push("</authors>"),t.push("<commentList>"),e.forEach((function(e){var n=0,a=[];if(e[1][0]&&e[1][0].T&&e[1][0].ID?n=r.indexOf("tc="+e[1][0].ID):e[1].forEach((function(e){e.a&&(n=r.indexOf(Nt(e.a))),a.push(e.t||"")})),t.push('<comment ref="'+e[0]+'" authorId="'+n+'"><text>'),a.length<=1)t.push(Ht("t",Nt(a[0]||"")));else{for(var i="Comment:\n    "+a[0]+"\n",l=1;l<a.length;++l)i+="Reply:\n    "+a[l]+"\n";t.push(Ht("t",Nt(i)))}t.push("</text></comment>")})),t.push("</commentList>"),t.length>2&&(t[t.length]="</comments>",t[1]=t[1].replace("/>",">")),t.join("")}function Oi(e,t,r){var n=[xt,Vt("ThreadedComments",null,{xmlns:qt}).replace(/[\/]>/,">")];return e.forEach((function(e){var a="";(e[1]||[]).forEach((function(i,l){if(i.T){i.a&&-1==t.indexOf(i.a)&&t.push(i.a);var o={ref:e[0],id:"{54EE7951-7262-4200-6969-"+("000000000000"+r.tcid++).slice(-12)+"}"};0==l?a=o.id:o.parentId=a,i.ID=o.id,i.a&&(o.personId="{54EE7950-7262-4200-6969-"+("000000000000"+t.indexOf(i.a)).slice(-12)+"}"),n.push(Vt("threadedComment",Ht("text",i.t||""),o))}else delete i.ID}))})),n.push("</ThreadedComments>"),n.join("")}var Ri=bn;function Pi(e){var t=Qr(),r=[];return qr(t,628),qr(t,630),e.forEach((function(e){e[1].forEach((function(e){r.indexOf(e.a)>-1||(r.push(e.a.slice(0,54)),qr(t,632,function(e){return yn(e.slice(0,54))}(e.a)))}))})),qr(t,631),qr(t,633),e.forEach((function(e){e[1].forEach((function(n){n.iauthor=r.indexOf(n.a);var a={s:on(e[0]),e:on(e[0])};qr(t,635,function(e,t){return null==t&&(t=Xr(36)),t.write_shift(4,e[1].iauthor),Un(e[0],t),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t}([a,n])),n.t&&n.t.length>0&&qr(t,637,Sn(n)),qr(t,636),delete n.iauthor}))})),qr(t,634),qr(t,629),t.end()}var Ii=["xlsb","xlsm","xlam","biff8","xla"];var Di=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,t={r:0,c:0};function r(e,r,n,a){var i=!1,l=!1;0==n.length?l=!0:"["==n.charAt(0)&&(l=!0,n=n.slice(1,-1)),0==a.length?i=!0:"["==a.charAt(0)&&(i=!0,a=a.slice(1,-1));var o=n.length>0?0|parseInt(n,10):0,s=a.length>0?0|parseInt(a,10):0;return i?s+=t.c:--s,l?o+=t.r:--o,r+(i?"":"$")+ln(s)+(l?"":"$")+nn(o)}return function(n,a){return t=a,n.replace(e,r)}}(),Li=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g,Mi=function(){return function(e,t){return e.replace(Li,(function(e,r,n,a,i,l){var o=an(a)-(n?0:t.c),s=rn(l)-(i?0:t.r);return r+"R"+(0==s?"":i?s+1:"["+s+"]")+"C"+(0==o?"":n?o+1:"["+o+"]")}))}}();function Fi(e,t){return e.replace(Li,(function(e,r,n,a,i,l){return r+("$"==n?n+a:ln(an(a)+t.c))+("$"==i?i+l:nn(rn(l)+t.r))}))}function ji(e){e.l+=1}function Ui(e,t){var r=e.read_shift(1==t?1:2);return[16383&r,r>>14&1,r>>15&1]}function Bi(e,t,r){var n=2;if(r){if(r.biff>=2&&r.biff<=5)return zi(e);12==r.biff&&(n=4)}var a=e.read_shift(n),i=e.read_shift(n),l=Ui(e,2),o=Ui(e,2);return{s:{r:a,c:l[0],cRel:l[1],rRel:l[2]},e:{r:i,c:o[0],cRel:o[1],rRel:o[2]}}}function zi(e){var t=Ui(e,2),r=Ui(e,2),n=e.read_shift(1),a=e.read_shift(1);return{s:{r:t[0],c:n,cRel:t[1],rRel:t[2]},e:{r:r[0],c:a,cRel:r[1],rRel:r[2]}}}function Hi(e,t,r){if(r&&r.biff>=2&&r.biff<=5)return function(e){var t=Ui(e,2),r=e.read_shift(1);return{r:t[0],c:r,cRel:t[1],rRel:t[2]}}(e);var n=e.read_shift(r&&12==r.biff?4:2),a=Ui(e,2);return{r:n,c:a[0],cRel:a[1],rRel:a[2]}}function Wi(e){var t=e.read_shift(2),r=e.read_shift(2);return{r:t,c:255&r,fQuoted:!!(16384&r),cRel:r>>15,rRel:r>>15}}function Vi(e){var t=1&e[e.l+1];return e.l+=4,[t,1]}function $i(e){return[e.read_shift(1),e.read_shift(1)]}function Gi(e,t){var r=[e.read_shift(1)];if(12==t)switch(r[0]){case 2:r[0]=4;break;case 4:r[0]=16;break;case 0:r[0]=1;break;case 1:r[0]=2}switch(r[0]){case 4:r[1]=va(e,1)?"TRUE":"FALSE",12!=t&&(e.l+=7);break;case 37:case 16:r[1]=Yn[e[e.l]],e.l+=12==t?4:8;break;case 0:e.l+=8;break;case 1:r[1]=Bn(e);break;case 2:r[1]=_a(e,0,{biff:t>0&&t<8?2:t});break;default:throw new Error("Bad SerAr: "+r[0])}return r}function Ki(e,t,r){for(var n=e.read_shift(12==r.biff?4:2),a=[],i=0;i!=n;++i)a.push((12==r.biff?jn:Pa)(e,8));return a}function Yi(e,t,r){var n=0,a=0;12==r.biff?(n=e.read_shift(4),a=e.read_shift(4)):(a=1+e.read_shift(1),n=1+e.read_shift(2)),r.biff>=2&&r.biff<8&&(--n,0==--a&&(a=256));for(var i=0,l=[];i!=n&&(l[i]=[]);++i)for(var o=0;o!=a;++o)l[i][o]=Gi(e,r.biff);return l}function Xi(e,t,r){return e.l+=2,[Wi(e)]}function Qi(e){return e.l+=6,[]}function qi(e){return e.l+=2,[ya(e),1&e.read_shift(2)]}var Ji=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"];var Zi={1:{n:"PtgExp",f:function(e,t,r){return e.l++,r&&12==r.biff?[e.read_shift(4,"i"),0]:[e.read_shift(2),e.read_shift(r&&2==r.biff?1:2)]}},2:{n:"PtgTbl",f:Yr},3:{n:"PtgAdd",f:ji},4:{n:"PtgSub",f:ji},5:{n:"PtgMul",f:ji},6:{n:"PtgDiv",f:ji},7:{n:"PtgPower",f:ji},8:{n:"PtgConcat",f:ji},9:{n:"PtgLt",f:ji},10:{n:"PtgLe",f:ji},11:{n:"PtgEq",f:ji},12:{n:"PtgGe",f:ji},13:{n:"PtgGt",f:ji},14:{n:"PtgNe",f:ji},15:{n:"PtgIsect",f:ji},16:{n:"PtgUnion",f:ji},17:{n:"PtgRange",f:ji},18:{n:"PtgUplus",f:ji},19:{n:"PtgUminus",f:ji},20:{n:"PtgPercent",f:ji},21:{n:"PtgParen",f:ji},22:{n:"PtgMissArg",f:ji},23:{n:"PtgStr",f:function(e,t,r){return e.l++,Ea(e,0,r)}},26:{n:"PtgSheet",f:function(e,t,r){return e.l+=5,e.l+=2,e.l+=2==r.biff?1:4,["PTGSHEET"]}},27:{n:"PtgEndSheet",f:function(e,t,r){return e.l+=2==r.biff?4:5,["PTGENDSHEET"]}},28:{n:"PtgErr",f:function(e){return e.l++,Yn[e.read_shift(1)]}},29:{n:"PtgBool",f:function(e){return e.l++,0!==e.read_shift(1)}},30:{n:"PtgInt",f:function(e){return e.l++,e.read_shift(2)}},31:{n:"PtgNum",f:function(e){return e.l++,Bn(e)}},32:{n:"PtgArray",f:function(e,t,r){var n=(96&e[e.l++])>>5;return e.l+=2==r.biff?6:12==r.biff?14:7,[n]}},33:{n:"PtgFunc",f:function(e,t,r){var n=(96&e[e.l])>>5;e.l+=1;var a=e.read_shift(r&&r.biff<=3?1:2);return[bl[a],vl[a],n]}},34:{n:"PtgFuncVar",f:function(e,t,r){var n=e[e.l++],a=e.read_shift(1),i=r&&r.biff<=3?[88==n?-1:0,e.read_shift(1)]:function(e){return[e[e.l+1]>>7,32767&e.read_shift(2)]}(e);return[a,(0===i[0]?vl:gl)[i[1]]]}},35:{n:"PtgName",f:function(e,t,r){var n=e.read_shift(1)>>>5&3,a=!r||r.biff>=8?4:2,i=e.read_shift(a);switch(r.biff){case 2:e.l+=5;break;case 3:case 4:e.l+=8;break;case 5:e.l+=12}return[n,0,i]}},36:{n:"PtgRef",f:function(e,t,r){var n=(96&e[e.l])>>5;return e.l+=1,[n,Hi(e,0,r)]}},37:{n:"PtgArea",f:function(e,t,r){return[(96&e[e.l++])>>5,Bi(e,r.biff>=2&&r.biff,r)]}},38:{n:"PtgMemArea",f:function(e,t,r){var n=e.read_shift(1)>>>5&3;return e.l+=r&&2==r.biff?3:4,[n,e.read_shift(r&&2==r.biff?1:2)]}},39:{n:"PtgMemErr",f:Yr},40:{n:"PtgMemNoMem",f:Yr},41:{n:"PtgMemFunc",f:function(e,t,r){return[e.read_shift(1)>>>5&3,e.read_shift(r&&2==r.biff?1:2)]}},42:{n:"PtgRefErr",f:function(e,t,r){var n=e.read_shift(1)>>>5&3;return e.l+=4,r.biff<8&&e.l--,12==r.biff&&(e.l+=2),[n]}},43:{n:"PtgAreaErr",f:function(e,t,r){var n=(96&e[e.l++])>>5;return e.l+=r&&r.biff>8?12:r.biff<8?6:8,[n]}},44:{n:"PtgRefN",f:function(e,t,r){var n=(96&e[e.l])>>5;e.l+=1;var a=function(e,t,r){var n=r&&r.biff?r.biff:8;if(n>=2&&n<=5)return function(e){var t=e.read_shift(2),r=e.read_shift(1),n=(32768&t)>>15,a=(16384&t)>>14;return t&=16383,1==n&&t>=8192&&(t-=16384),1==a&&r>=128&&(r-=256),{r:t,c:r,cRel:a,rRel:n}}(e);var a=e.read_shift(n>=12?4:2),i=e.read_shift(2),l=(16384&i)>>14,o=(32768&i)>>15;if(i&=16383,1==o)for(;a>524287;)a-=1048576;if(1==l)for(;i>8191;)i-=16384;return{r:a,c:i,cRel:l,rRel:o}}(e,0,r);return[n,a]}},45:{n:"PtgAreaN",f:function(e,t,r){var n=(96&e[e.l++])>>5,a=function(e,t,r){if(r.biff<8)return zi(e);var n=e.read_shift(12==r.biff?4:2),a=e.read_shift(12==r.biff?4:2),i=Ui(e,2),l=Ui(e,2);return{s:{r:n,c:i[0],cRel:i[1],rRel:i[2]},e:{r:a,c:l[0],cRel:l[1],rRel:l[2]}}}(e,0,r);return[n,a]}},46:{n:"PtgMemAreaN",f:function(e){return[e.read_shift(1)>>>5&3,e.read_shift(2)]}},47:{n:"PtgMemNoMemN",f:function(e){return[e.read_shift(1)>>>5&3,e.read_shift(2)]}},57:{n:"PtgNameX",f:function(e,t,r){return 5==r.biff?function(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2,"i");e.l+=8;var n=e.read_shift(2);return e.l+=12,[t,r,n]}(e):[e.read_shift(1)>>>5&3,e.read_shift(2),e.read_shift(4)]}},58:{n:"PtgRef3d",f:function(e,t,r){var n=(96&e[e.l])>>5;e.l+=1;var a=e.read_shift(2);return r&&5==r.biff&&(e.l+=12),[n,a,Hi(e,0,r)]}},59:{n:"PtgArea3d",f:function(e,t,r){var n=(96&e[e.l++])>>5,a=e.read_shift(2,"i");if(r)switch(r.biff){case 5:e.l+=12;break;case 12:0}return[n,a,Bi(e,0,r)]}},60:{n:"PtgRefErr3d",f:function(e,t,r){var n=(96&e[e.l++])>>5,a=e.read_shift(2),i=4;if(r)switch(r.biff){case 5:i=15;break;case 12:i=6}return e.l+=i,[n,a]}},61:{n:"PtgAreaErr3d",f:function(e,t,r){var n=(96&e[e.l++])>>5,a=e.read_shift(2),i=8;if(r)switch(r.biff){case 5:e.l+=12,i=6;break;case 12:i=12}return e.l+=i,[n,a]}},255:{}},el={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},tl={1:{n:"PtgElfLel",f:qi},2:{n:"PtgElfRw",f:Xi},3:{n:"PtgElfCol",f:Xi},6:{n:"PtgElfRwV",f:Xi},7:{n:"PtgElfColV",f:Xi},10:{n:"PtgElfRadical",f:Xi},11:{n:"PtgElfRadicalS",f:Qi},13:{n:"PtgElfColS",f:Qi},15:{n:"PtgElfColSV",f:Qi},16:{n:"PtgElfRadicalLel",f:qi},25:{n:"PtgList",f:function(e){e.l+=2;var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(4),a=e.read_shift(2),i=e.read_shift(2);return{ixti:t,coltype:3&r,rt:Ji[r>>2&31],idx:n,c:a,C:i}}},29:{n:"PtgSxName",f:function(e){return e.l+=2,[e.read_shift(4)]}},255:{}},rl={0:{n:"PtgAttrNoop",f:function(e){return e.l+=4,[0,0]}},1:{n:"PtgAttrSemi",f:function(e,t,r){var n=255&e[e.l+1]?1:0;return e.l+=r&&2==r.biff?3:4,[n]}},2:{n:"PtgAttrIf",f:function(e,t,r){var n=255&e[e.l+1]?1:0;return e.l+=2,[n,e.read_shift(r&&2==r.biff?1:2)]}},4:{n:"PtgAttrChoose",f:function(e,t,r){e.l+=2;for(var n=e.read_shift(r&&2==r.biff?1:2),a=[],i=0;i<=n;++i)a.push(e.read_shift(r&&2==r.biff?1:2));return a}},8:{n:"PtgAttrGoto",f:function(e,t,r){var n=255&e[e.l+1]?1:0;return e.l+=2,[n,e.read_shift(r&&2==r.biff?1:2)]}},16:{n:"PtgAttrSum",f:function(e,t,r){e.l+=r&&2==r.biff?3:4}},32:{n:"PtgAttrBaxcel",f:Vi},33:{n:"PtgAttrBaxcel",f:Vi},64:{n:"PtgAttrSpace",f:function(e){return e.read_shift(2),$i(e)}},65:{n:"PtgAttrSpaceSemi",f:function(e){return e.read_shift(2),$i(e)}},128:{n:"PtgAttrIfError",f:function(e){var t=255&e[e.l+1]?1:0;return e.l+=2,[t,e.read_shift(2)]}},255:{}};function nl(e,t,r,n){if(n.biff<8)return Yr(e,t);for(var a=e.l+t,i=[],l=0;l!==r.length;++l)switch(r[l][0]){case"PtgArray":r[l][1]=Yi(e,0,n),i.push(r[l][1]);break;case"PtgMemArea":r[l][2]=Ki(e,r[l][1],n),i.push(r[l][2]);break;case"PtgExp":n&&12==n.biff&&(r[l][1][1]=e.read_shift(4),i.push(r[l][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+r[l][0]}return 0!==(t=a-e.l)&&i.push(Yr(e,t)),i}function al(e,t,r){for(var n,a,i=e.l+t,l=[];i!=e.l;)t=i-e.l,a=e[e.l],n=Zi[a]||Zi[el[a]],24!==a&&25!==a||(n=(24===a?tl:rl)[e[e.l+1]]),n&&n.f?l.push([n.n,n.f(e,t,r)]):Yr(e,t);return l}function il(e){for(var t=[],r=0;r<e.length;++r){for(var n=e[r],a=[],i=0;i<n.length;++i){var l=n[i];if(l)if(2===l[0])a.push('"'+l[1].replace(/"/g,'""')+'"');else a.push(l[1]);else a.push("")}t.push(a.join(","))}return t.join(";")}var ll={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function ol(e,t,r){if(!e)return"SH33TJSERR0";if(r.biff>8&&(!e.XTI||!e.XTI[t]))return e.SheetNames[t];if(!e.XTI)return"SH33TJSERR6";var n=e.XTI[t];if(r.biff<8)return t>1e4&&(t-=65536),t<0&&(t=-t),0==t?"":e.XTI[t-1];if(!n)return"SH33TJSERR1";var a="";if(r.biff>8)switch(e[n[0]][0]){case 357:return a=-1==n[1]?"#REF":e.SheetNames[n[1]],n[1]==n[2]?a:a+":"+e.SheetNames[n[2]];case 358:return null!=r.SID?e.SheetNames[r.SID]:"SH33TJSSAME"+e[n[0]][0];default:return"SH33TJSSRC"+e[n[0]][0]}switch(e[n[0]][0][0]){case 1025:return a=-1==n[1]?"#REF":e.SheetNames[n[1]]||"SH33TJSERR3",n[1]==n[2]?a:a+":"+e.SheetNames[n[2]];case 14849:return e[n[0]].slice(1).map((function(e){return e.Name})).join(";;");default:return e[n[0]][0][3]?(a=-1==n[1]?"#REF":e[n[0]][0][3][n[1]]||"SH33TJSERR4",n[1]==n[2]?a:a+":"+e[n[0]][0][3][n[2]]):"SH33TJSERR2"}}function sl(e,t,r){var n=ol(e,t,r);return"#REF"==n?n:function(e,t){if(!e&&!(t&&t.biff<=5&&t.biff>=2))throw new Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e)?"'"+e+"'":e}(n,r)}function cl(e,t,r,n,a){var i,l,o,s,c=a&&a.biff||8,u={s:{c:0,r:0},e:{c:0,r:0}},f=[],h=0,d=0,p="";if(!e[0]||!e[0][0])return"";for(var m=-1,g="",v=0,b=e[0].length;v<b;++v){var y=e[0][v];switch(y[0]){case"PtgUminus":f.push("-"+f.pop());break;case"PtgUplus":f.push("+"+f.pop());break;case"PtgPercent":f.push(f.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(i=f.pop(),l=f.pop(),m>=0){switch(e[0][m][1][0]){case 0:g=bt(" ",e[0][m][1][1]);break;case 1:g=bt("\r",e[0][m][1][1]);break;default:if(g="",a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}l+=g,m=-1}f.push(l+ll[y[0]]+i);break;case"PtgIsect":i=f.pop(),l=f.pop(),f.push(l+" "+i);break;case"PtgUnion":i=f.pop(),l=f.pop(),f.push(l+","+i);break;case"PtgRange":i=f.pop(),l=f.pop(),f.push(l+":"+i);break;case"PtgAttrChoose":case"PtgAttrGoto":case"PtgAttrIf":case"PtgAttrIfError":case"PtgAttrBaxcel":case"PtgAttrSemi":case"PtgMemArea":case"PtgTbl":case"PtgMemErr":case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":case"PtgMemFunc":case"PtgMemNoMem":break;case"PtgRef":o=Jr(y[1][1],u,a),f.push(en(o,c));break;case"PtgRefN":o=r?Jr(y[1][1],r,a):y[1][1],f.push(en(o,c));break;case"PtgRef3d":h=y[1][1],o=Jr(y[1][2],u,a);p=sl(n,h,a);f.push(p+"!"+en(o,c));break;case"PtgFunc":case"PtgFuncVar":var w=y[1][0],T=y[1][1];w||(w=0);var E=0==(w&=127)?[]:f.slice(-w);f.length-=w,"User"===T&&(T=E.shift()),f.push(T+"("+E.join(",")+")");break;case"PtgBool":f.push(y[1]?"TRUE":"FALSE");break;case"PtgInt":case"PtgErr":f.push(y[1]);break;case"PtgNum":f.push(String(y[1]));break;case"PtgStr":f.push('"'+y[1].replace(/"/g,'""')+'"');break;case"PtgAreaN":s=Zr(y[1][1],r?{s:r}:u,a),f.push(tn(s,a));break;case"PtgArea":s=Zr(y[1][1],u,a),f.push(tn(s,a));break;case"PtgArea3d":h=y[1][1],s=y[1][2],p=sl(n,h,a),f.push(p+"!"+tn(s,a));break;case"PtgAttrSum":f.push("SUM("+f.pop()+")");break;case"PtgName":d=y[1][2];var S=(n.names||[])[d-1]||(n[0]||[])[d],x=S?S.Name:"SH33TJSNAME"+String(d);x&&"_xlfn."==x.slice(0,6)&&!a.xlfn&&(x=x.slice(6)),f.push(x);break;case"PtgNameX":var k,_=y[1][1];if(d=y[1][2],!(a.biff<=5)){var C="";if(14849==((n[_]||[])[0]||[])[0]||(1025==((n[_]||[])[0]||[])[0]?n[_][d]&&n[_][d].itab>0&&(C=n.SheetNames[n[_][d].itab-1]+"!"):C=n.SheetNames[d-1]+"!"),n[_]&&n[_][d])C+=n[_][d].Name;else if(n[0]&&n[0][d])C+=n[0][d].Name;else{var A=(ol(n,_,a)||"").split(";;");A[d-1]?C=A[d-1]:C+="SH33TJSERRX"}f.push(C);break}_<0&&(_=-_),n[_]&&(k=n[_][d]),k||(k={Name:"SH33TJSERRY"}),f.push(k.Name);break;case"PtgParen":var N="(",O=")";if(m>=0){switch(g="",e[0][m][1][0]){case 2:N=bt(" ",e[0][m][1][1])+N;break;case 3:N=bt("\r",e[0][m][1][1])+N;break;case 4:O=bt(" ",e[0][m][1][1])+O;break;case 5:O=bt("\r",e[0][m][1][1])+O;break;default:if(a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][m][1][0])}m=-1}f.push(N+f.pop()+O);break;case"PtgRefErr":case"PtgRefErr3d":case"PtgAreaErr":case"PtgAreaErr3d":f.push("#REF!");break;case"PtgExp":o={c:y[1][1],r:y[1][0]};var R={c:r.c,r:r.r};if(n.sharedf[sn(o)]){var P=n.sharedf[sn(o)];f.push(cl(P,u,R,n,a))}else{var I=!1;for(i=0;i!=n.arrayf.length;++i)if(l=n.arrayf[i],!(o.c<l[0].s.c||o.c>l[0].e.c)&&!(o.r<l[0].s.r||o.r>l[0].e.r)){f.push(cl(l[1],u,R,n,a)),I=!0;break}I||f.push(y[1])}break;case"PtgArray":f.push("{"+il(y[1])+"}");break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":m=v;break;case"PtgMissArg":f.push("");break;case"PtgList":f.push("Table"+y[1].idx+"[#"+y[1].rt+"]");break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw new Error("Unsupported ELFs");default:throw new Error("Unrecognized Formula Token: "+String(y))}if(3!=a.biff&&m>=0&&-1==["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"].indexOf(e[0][v][0])){var D=!0;switch((y=e[0][m])[1][0]){case 4:D=!1;case 0:g=bt(" ",y[1][1]);break;case 5:D=!1;case 1:g=bt("\r",y[1][1]);break;default:if(g="",a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+y[1][0])}f.push((D?g:"")+f.pop()+(D?"":g)),m=-1}}if(f.length>1&&a.WTF)throw new Error("bad formula stack");return f[0]}function ul(e,t,r,n,a){var i=Oa(t,r,a),l=function(e){if(null==e){var t=Xr(8);return t.write_shift(1,3),t.write_shift(1,0),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(2,65535),t}return zn("number"==typeof e?e:0)}(e.v),o=Xr(6);o.write_shift(2,33),o.write_shift(4,0);for(var s=Xr(e.bf.length),c=0;c<e.bf.length;++c)s[c]=e.bf[c];return re([i,l,o,s])}function fl(e,t,r){var n=e.read_shift(4),a=al(e,n,r),i=e.read_shift(4);return[a,i>0?nl(e,i,a,r):null]}var hl=fl,dl=fl,pl=fl,ml=fl,gl={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},vl={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},bl={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};var yl="undefined"!==typeof Map;function wl(e,t,r){var n=0,a=e.length;if(r){if(yl?r.has(t):Object.prototype.hasOwnProperty.call(r,t))for(var i=yl?r.get(t):r[t];n<i.length;++n)if(e[i[n]].t===t)return e.Count++,i[n]}else for(;n<a;++n)if(e[n].t===t)return e.Count++,n;return e[a]={t:t},e.Count++,e.Unique++,r&&(yl?(r.has(t)||r.set(t,[]),r.get(t).push(a)):(Object.prototype.hasOwnProperty.call(r,t)||(r[t]=[]),r[t].push(a))),a}function Tl(e,t){var r={min:e+1,max:e+1},n=-1;return t.MDW&&(ri=t.MDW),null!=t.width?r.customWidth=1:null!=t.wpx?n=ai(t.wpx):null!=t.wch&&(n=t.wch),n>-1?(r.width=ii(n),r.customWidth=1):null!=t.width&&(r.width=t.width),t.hidden&&(r.hidden=!0),null!=t.level&&(r.outlineLevel=r.level=t.level),r}function El(e,t){if(e){var r=[.7,.7,.75,.75,.3,.3];"xlml"==t&&(r=[1,1,1,1,.5,.5]),null==e.left&&(e.left=r[0]),null==e.right&&(e.right=r[1]),null==e.top&&(e.top=r[2]),null==e.bottom&&(e.bottom=r[3]),null==e.header&&(e.header=r[4]),null==e.footer&&(e.footer=r[5])}}function Sl(e,t,r){var n=r.revssf[null!=t.z?t.z:"General"],a=60,i=e.length;if(null==n&&r.ssf)for(;a<392;++a)if(null==r.ssf[a]){Ke(t.z,a),r.ssf[a]=t.z,r.revssf[t.z]=n=a;break}for(a=0;a!=i;++a)if(e[a].numFmtId===n)return a;return e[i]={numFmtId:n,fontId:0,fillId:0,borderId:0,xfId:0,applyNumberFormat:1},i}function xl(e,t,r){if(e&&e["!ref"]){var n=fn(e["!ref"]);if(n.e.c<n.s.c||n.e.r<n.s.r)throw new Error("Bad range ("+r+"): "+e["!ref"])}}var kl=["objects","scenarios","selectLockedCells","selectUnlockedCells"],_l=["formatColumns","formatRows","formatCells","insertColumns","insertRows","insertHyperlinks","deleteColumns","deleteRows","sort","autoFilter","pivotTables"];function Cl(e,t,r,n){if(e.c&&r["!comments"].push([t,e.c]),void 0===e.v&&"string"!==typeof e.f||"z"===e.t&&!e.f)return"";var a="",i=e.t,l=e.v;if("z"!==e.t)switch(e.t){case"b":a=e.v?"1":"0";break;case"n":a=""+e.v;break;case"e":a=Yn[e.v];break;case"d":n&&n.cellDates?a=mt(e.v,-1).toISOString():((e=vt(e)).t="n",a=""+(e.v=ot(mt(e.v)))),"undefined"===typeof e.z&&(e.z=pe[14]);break;default:a=e.v}var o=Ht("v",Nt(a)),s={r:t},c=Sl(n.cellXfs,e,n);switch(0!==c&&(s.s=c),e.t){case"n":case"z":break;case"d":s.t="d";break;case"b":s.t="b";break;case"e":s.t="e";break;default:if(null==e.v){delete e.t;break}if(e.v.length>32767)throw new Error("Text length must not exceed 32767 characters");if(n&&n.bookSST){o=Ht("v",""+wl(n.Strings,e.v,n.revStrings)),s.t="s";break}s.t="str"}if(e.t!=i&&(e.t=i,e.v=l),"string"==typeof e.f&&e.f){var u=e.F&&e.F.slice(0,t.length)==t?{t:"array",ref:e.F}:null;o=Vt("f",Nt(e.f),u)+(null!=e.v?o:"")}return e.l&&r["!links"].push([t,e.l]),e.D&&(s.cm=1),Vt("c",o,s)}function Al(e,t,r,n){var a,i=[xt,Vt("worksheet",null,{xmlns:ir[0],"xmlns:r":tr})],l=r.SheetNames[e],o="",s=r.Sheets[l];null==s&&(s={});var c=s["!ref"]||"A1",u=fn(c);if(u.e.c>16383||u.e.r>1048575){if(t.WTF)throw new Error("Range "+c+" exceeds format limit A1:XFD1048576");u.e.c=Math.min(u.e.c,16383),u.e.r=Math.min(u.e.c,1048575),c=un(u)}n||(n={}),s["!comments"]=[];var f=[];!function(e,t,r,n,a){var i=!1,l={},o=null;if("xlsx"!==n.bookType&&t.vbaraw){var s=t.SheetNames[r];try{t.Workbook&&(s=t.Workbook.Sheets[r].CodeName||s)}catch(u){}i=!0,l.codeName=Ut(Nt(s))}if(e&&e["!outline"]){var c={summaryBelow:1,summaryRight:1};e["!outline"].above&&(c.summaryBelow=0),e["!outline"].left&&(c.summaryRight=0),o=(o||"")+Vt("outlinePr",null,c)}(i||o)&&(a[a.length]=Vt("sheetPr",o,l))}(s,r,e,t,i),i[i.length]=Vt("dimension",null,{ref:c}),i[i.length]=function(e,t,r,n){var a={workbookViewId:"0"};return(((n||{}).Workbook||{}).Views||[])[0]&&(a.rightToLeft=n.Workbook.Views[0].RTL?"1":"0"),Vt("sheetViews",Vt("sheetView",null,a),{})}(0,0,0,r),t.sheetFormat&&(i[i.length]=Vt("sheetFormatPr",null,{defaultRowHeight:t.sheetFormat.defaultRowHeight||"16",baseColWidth:t.sheetFormat.baseColWidth||"10",outlineLevelRow:t.sheetFormat.outlineLevelRow||"7"})),null!=s["!cols"]&&s["!cols"].length>0&&(i[i.length]=function(e,t){for(var r,n=["<cols>"],a=0;a!=t.length;++a)(r=t[a])&&(n[n.length]=Vt("col",null,Tl(a,r)));return n[n.length]="</cols>",n.join("")}(0,s["!cols"])),i[a=i.length]="<sheetData/>",s["!links"]=[],null!=s["!ref"]&&(o=function(e,t){var r,n,a=[],i=[],l=fn(e["!ref"]),o="",s="",c=[],u=0,f=0,h=e["!rows"],d=Array.isArray(e),p={r:s},m=-1;for(f=l.s.c;f<=l.e.c;++f)c[f]=ln(f);for(u=l.s.r;u<=l.e.r;++u){for(i=[],s=nn(u),f=l.s.c;f<=l.e.c;++f){r=c[f]+s;var g=d?(e[u]||[])[f]:e[r];void 0!==g&&null!=(o=Cl(g,r,e,t))&&i.push(o)}(i.length>0||h&&h[u])&&(p={r:s},h&&h[u]&&((n=h[u]).hidden&&(p.hidden=1),m=-1,n.hpx?m=si(n.hpx):n.hpt&&(m=n.hpt),m>-1&&(p.ht=m,p.customHeight=1),n.level&&(p.outlineLevel=n.level)),a[a.length]=Vt("row",i.join(""),p))}if(h)for(;u<h.length;++u)h&&h[u]&&(p={r:u+1},(n=h[u]).hidden&&(p.hidden=1),m=-1,n.hpx?m=si(n.hpx):n.hpt&&(m=n.hpt),m>-1&&(p.ht=m,p.customHeight=1),n.level&&(p.outlineLevel=n.level),a[a.length]=Vt("row","",p));return a.join("")}(s,t,0,0),o.length>0&&(i[i.length]=o)),i.length>a+1&&(i[i.length]="</sheetData>",i[a]=i[a].replace("/>",">")),s["!protect"]&&(i[i.length]=function(e){var t={sheet:1};return kl.forEach((function(r){null!=e[r]&&e[r]&&(t[r]="1")})),_l.forEach((function(r){null==e[r]||e[r]||(t[r]="0")})),e.password&&(t.password=Za(e.password).toString(16).toUpperCase()),Vt("sheetProtection",null,t)}(s["!protect"])),null!=s["!autofilter"]&&(i[i.length]=function(e,t,r,n){var a="string"==typeof e.ref?e.ref:un(e.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var i=r.Workbook.Names,l=cn(a);l.s.r==l.e.r&&(l.e.r=cn(t["!ref"]).e.r,a=un(l));for(var o=0;o<i.length;++o){var s=i[o];if("_xlnm._FilterDatabase"==s.Name&&s.Sheet==n){s.Ref="'"+r.SheetNames[n]+"'!"+a;break}}return o==i.length&&i.push({Name:"_xlnm._FilterDatabase",Sheet:n,Ref:"'"+r.SheetNames[n]+"'!"+a}),Vt("autoFilter",null,{ref:a})}(s["!autofilter"],s,r,e)),null!=s["!merges"]&&s["!merges"].length>0&&(i[i.length]=function(e){if(0===e.length)return"";for(var t='<mergeCells count="'+e.length+'">',r=0;r!=e.length;++r)t+='<mergeCell ref="'+un(e[r])+'"/>';return t+"</mergeCells>"}(s["!merges"]));var h,d,p=-1,m=-1;return s["!links"].length>0&&(i[i.length]="<hyperlinks>",s["!links"].forEach((function(e){e[1].Target&&(h={ref:e[0]},"#"!=e[1].Target.charAt(0)&&(m=ta(n,-1,Nt(e[1].Target).replace(/#.*$/,""),Jn.HLINK),h["r:id"]="rId"+m),(p=e[1].Target.indexOf("#"))>-1&&(h.location=Nt(e[1].Target.slice(p+1))),e[1].Tooltip&&(h.tooltip=Nt(e[1].Tooltip)),i[i.length]=Vt("hyperlink",null,h))})),i[i.length]="</hyperlinks>"),delete s["!links"],null!=s["!margins"]&&(i[i.length]=(El(d=s["!margins"]),Vt("pageMargins",null,d))),t&&!t.ignoreEC&&void 0!=t.ignoreEC||(i[i.length]=Ht("ignoredErrors",Vt("ignoredError",null,{numberStoredAsText:1,sqref:c}))),f.length>0&&(m=ta(n,-1,"../drawings/drawing"+(e+1)+".xml",Jn.DRAW),i[i.length]=Vt("drawing",null,{"r:id":"rId"+m}),s["!drawing"]=f),s["!comments"].length>0&&(m=ta(n,-1,"../drawings/vmlDrawing"+(e+1)+".vml",Jn.VML),i[i.length]=Vt("legacyDrawing",null,{"r:id":"rId"+m}),s["!legacy"]=m),i.length>1&&(i[i.length]="</worksheet>",i[1]=i[1].replace("/>",">")),i.join("")}function Nl(e,t,r,n){var a=function(e,t,r){var n=Xr(145),a=(r["!rows"]||[])[e]||{};n.write_shift(4,e),n.write_shift(4,0);var i=320;a.hpx?i=20*si(a.hpx):a.hpt&&(i=20*a.hpt),n.write_shift(2,i),n.write_shift(1,0);var l=0;a.level&&(l|=a.level),a.hidden&&(l|=16),(a.hpx||a.hpt)&&(l|=32),n.write_shift(1,l),n.write_shift(1,0);var o=0,s=n.l;n.l+=4;for(var c={r:e,c:0},u=0;u<16;++u)if(!(t.s.c>u+1<<10||t.e.c<u<<10)){for(var f=-1,h=-1,d=u<<10;d<u+1<<10;++d)c.c=d,(Array.isArray(r)?(r[c.r]||[])[c.c]:r[sn(c)])&&(f<0&&(f=d),h=d);f<0||(++o,n.write_shift(4,f),n.write_shift(4,h))}var p=n.l;return n.l=s,n.write_shift(4,o),n.l=p,n.length>n.l?n.slice(0,n.l):n}(n,r,t);(a.length>17||(t["!rows"]||[])[n])&&qr(e,0,a)}var Ol=jn,Rl=Un;function Pl(e){return[_n(e),Bn(e),"n"]}var Il=jn,Dl=Un;var Ll=["left","right","top","bottom","header","footer"];function Ml(e,t,r,n,a,i,l){if(void 0===t.v)return!1;var o="";switch(t.t){case"b":o=t.v?"1":"0";break;case"d":(t=vt(t)).z=t.z||pe[14],t.v=ot(mt(t.v)),t.t="n";break;case"n":case"e":o=""+t.v;break;default:o=t.v}var s={r:r,c:n};switch(s.s=Sl(a.cellXfs,t,a),t.l&&i["!links"].push([sn(s),t.l]),t.c&&i["!comments"].push([sn(s),t.c]),t.t){case"s":case"str":return a.bookSST?(o=wl(a.Strings,t.v,a.revStrings),s.t="s",s.v=o,l?qr(e,18,function(e,t,r){return null==r&&(r=Xr(8)),Cn(t,r),r.write_shift(4,t.v),r}(0,s)):qr(e,7,function(e,t,r){return null==r&&(r=Xr(12)),kn(t,r),r.write_shift(4,t.v),r}(0,s))):(s.t="str",l?qr(e,17,function(e,t,r){return null==r&&(r=Xr(8+4*e.v.length)),Cn(t,r),yn(e.v,r),r.length>r.l?r.slice(0,r.l):r}(t,s)):qr(e,6,function(e,t,r){return null==r&&(r=Xr(12+4*e.v.length)),kn(t,r),yn(e.v,r),r.length>r.l?r.slice(0,r.l):r}(t,s))),!0;case"n":return t.v==(0|t.v)&&t.v>-1e3&&t.v<1e3?l?qr(e,13,function(e,t,r){return null==r&&(r=Xr(8)),Cn(t,r),Mn(e.v,r),r}(t,s)):qr(e,2,function(e,t,r){return null==r&&(r=Xr(12)),kn(t,r),Mn(e.v,r),r}(t,s)):l?qr(e,16,function(e,t,r){return null==r&&(r=Xr(12)),Cn(t,r),zn(e.v,r),r}(t,s)):qr(e,5,function(e,t,r){return null==r&&(r=Xr(16)),kn(t,r),zn(e.v,r),r}(t,s)),!0;case"b":return s.t="b",l?qr(e,15,function(e,t,r){return null==r&&(r=Xr(5)),Cn(t,r),r.write_shift(1,e.v?1:0),r}(t,s)):qr(e,4,function(e,t,r){return null==r&&(r=Xr(9)),kn(t,r),r.write_shift(1,e.v?1:0),r}(t,s)),!0;case"e":return s.t="e",l?qr(e,14,function(e,t,r){return null==r&&(r=Xr(8)),Cn(t,r),r.write_shift(1,e.v),r.write_shift(2,0),r.write_shift(1,0),r}(t,s)):qr(e,3,function(e,t,r){return null==r&&(r=Xr(9)),kn(t,r),r.write_shift(1,e.v),r}(t,s)),!0}return l?qr(e,12,function(e,t,r){return null==r&&(r=Xr(4)),Cn(t,r)}(0,s)):qr(e,1,function(e,t,r){return null==r&&(r=Xr(8)),kn(t,r)}(0,s)),!0}function Fl(e,t){var r,n;t&&t["!merges"]&&(qr(e,177,(r=t["!merges"].length,null==n&&(n=Xr(4)),n.write_shift(4,r),n)),t["!merges"].forEach((function(t){qr(e,176,Dl(t))})),qr(e,178))}function jl(e,t){t&&t["!cols"]&&(qr(e,390),t["!cols"].forEach((function(t,r){t&&qr(e,60,function(e,t,r){null==r&&(r=Xr(18));var n=Tl(e,t);r.write_shift(-4,e),r.write_shift(-4,e),r.write_shift(4,256*(n.width||10)),r.write_shift(4,0);var a=0;return t.hidden&&(a|=1),"number"==typeof n.width&&(a|=2),t.level&&(a|=t.level<<8),r.write_shift(2,a),r}(r,t))})),qr(e,391))}function Ul(e,t){t&&t["!ref"]&&(qr(e,648),qr(e,649,function(e){var t=Xr(24);return t.write_shift(4,4),t.write_shift(4,1),Un(e,t),t}(fn(t["!ref"]))),qr(e,650))}function Bl(e,t,r){t["!links"].forEach((function(t){if(t[1].Target){var n=ta(r,-1,t[1].Target.replace(/#.*$/,""),Jn.HLINK);qr(e,494,function(e,t){var r=Xr(50+4*(e[1].Target.length+(e[1].Tooltip||"").length));Un({s:on(e[0]),e:on(e[0])},r),Dn("rId"+t,r);var n=e[1].Target.indexOf("#");return yn((-1==n?"":e[1].Target.slice(n+1))||"",r),yn(e[1].Tooltip||"",r),yn("",r),r.slice(0,r.l)}(t,n))}})),delete t["!links"]}function zl(e,t,r){qr(e,133),qr(e,137,function(e,t,r){null==r&&(r=Xr(30));var n=924;return(((t||{}).Views||[])[0]||{}).RTL&&(n|=32),r.write_shift(2,n),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(2,0),r.write_shift(2,100),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(4,0),r}(0,r)),qr(e,138),qr(e,134)}function Hl(e,t){var r,n;t["!protect"]&&qr(e,535,(r=t["!protect"],null==n&&(n=Xr(66)),n.write_shift(2,r.password?Za(r.password):0),n.write_shift(4,1),[["objects",!1],["scenarios",!1],["formatCells",!0],["formatColumns",!0],["formatRows",!0],["insertColumns",!0],["insertRows",!0],["insertHyperlinks",!0],["deleteColumns",!0],["deleteRows",!0],["selectLockedCells",!1],["sort",!0],["autoFilter",!0],["pivotTables",!0],["selectUnlockedCells",!1]].forEach((function(e){e[1]?n.write_shift(4,null==r[e[0]]||r[e[0]]?0:1):n.write_shift(4,null!=r[e[0]]&&r[e[0]]?0:1)})),n))}function Wl(e,t,r,n){var a=Qr(),i=r.SheetNames[e],l=r.Sheets[i]||{},o=i;try{r&&r.Workbook&&(o=r.Workbook.Sheets[e].CodeName||o)}catch(f){}var s,c,u=fn(l["!ref"]||"A1");if(u.e.c>16383||u.e.r>1048575){if(t.WTF)throw new Error("Range "+(l["!ref"]||"A1")+" exceeds format limit A1:XFD1048576");u.e.c=Math.min(u.e.c,16383),u.e.r=Math.min(u.e.c,1048575)}return l["!links"]=[],l["!comments"]=[],qr(a,129),(r.vbaraw||l["!outline"])&&qr(a,147,function(e,t,r){null==r&&(r=Xr(84+4*e.length));var n=192;t&&(t.above&&(n&=-65),t.left&&(n&=-129)),r.write_shift(1,n);for(var a=1;a<3;++a)r.write_shift(1,0);return Hn({auto:1},r),r.write_shift(-4,-1),r.write_shift(-4,-1),Nn(e,r),r.slice(0,r.l)}(o,l["!outline"])),qr(a,148,Rl(u)),zl(a,0,r.Workbook),jl(a,l),function(e,t,r,n){var a,i=fn(t["!ref"]||"A1"),l="",o=[];qr(e,145);var s=Array.isArray(t),c=i.e.r;t["!rows"]&&(c=Math.max(i.e.r,t["!rows"].length-1));for(var u=i.s.r;u<=c;++u){l=nn(u),Nl(e,t,i,u);var f=!1;if(u<=i.e.r)for(var h=i.s.c;h<=i.e.c;++h){u===i.s.r&&(o[h]=ln(h)),a=o[h]+l;var d=s?(t[u]||[])[h]:t[a];f=!!d&&Ml(e,d,u,h,n,t,f)}}qr(e,146)}(a,l,0,t),Hl(a,l),function(e,t,r,n){if(t["!autofilter"]){var a=t["!autofilter"],i="string"===typeof a.ref?a.ref:un(a.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var l=r.Workbook.Names,o=cn(i);o.s.r==o.e.r&&(o.e.r=cn(t["!ref"]).e.r,i=un(o));for(var s=0;s<l.length;++s){var c=l[s];if("_xlnm._FilterDatabase"==c.Name&&c.Sheet==n){c.Ref="'"+r.SheetNames[n]+"'!"+i;break}}s==l.length&&l.push({Name:"_xlnm._FilterDatabase",Sheet:n,Ref:"'"+r.SheetNames[n]+"'!"+i}),qr(e,161,Un(fn(i))),qr(e,162)}}(a,l,r,e),Fl(a,l),Bl(a,l,n),l["!margins"]&&qr(a,476,(s=l["!margins"],null==c&&(c=Xr(48)),El(s),Ll.forEach((function(e){zn(s[e],c)})),c)),t&&!t.ignoreEC&&void 0!=t.ignoreEC||Ul(a,l),function(e,t,r,n){if(t["!comments"].length>0){var a=ta(n,-1,"../drawings/vmlDrawing"+(r+1)+".vml",Jn.VML);qr(e,551,Dn("rId"+a)),t["!legacy"]=a}}(a,l,e,n),qr(a,130),a.end()}var Vl=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]];var $l="][*?/\\".split("");function Gl(e,t){if(e.length>31){if(t)return!1;throw new Error("Sheet names cannot exceed 31 chars")}var r=!0;return $l.forEach((function(n){if(-1!=e.indexOf(n)){if(!t)throw new Error("Sheet name cannot contain : \\ / ? * [ ]");r=!1}})),r}function Kl(e){if(!e||!e.SheetNames||!e.Sheets)throw new Error("Invalid Workbook");if(!e.SheetNames.length)throw new Error("Workbook is empty");var t,r,n,a=e.Workbook&&e.Workbook.Sheets||[];t=e.SheetNames,r=a,n=!!e.vbaraw,t.forEach((function(e,a){Gl(e);for(var i=0;i<a;++i)if(e==t[i])throw new Error("Duplicate Sheet Name: "+e);if(n){var l=r&&r[a]&&r[a].CodeName||e;if(95==l.charCodeAt(0)&&l.length>22)throw new Error("Bad Code Name: Worksheet"+l)}}));for(var i=0;i<e.SheetNames.length;++i)xl(e.Sheets[e.SheetNames[i]],e.SheetNames[i],i)}function Yl(e){var t=[xt];t[t.length]=Vt("workbook",null,{xmlns:ir[0],"xmlns:r":tr});var r=e.Workbook&&(e.Workbook.Names||[]).length>0,n={codeName:"ThisWorkbook"};e.Workbook&&e.Workbook.WBProps&&(Vl.forEach((function(t){null!=e.Workbook.WBProps[t[0]]&&e.Workbook.WBProps[t[0]]!=t[1]&&(n[t[0]]=e.Workbook.WBProps[t[0]])})),e.Workbook.WBProps.CodeName&&(n.codeName=e.Workbook.WBProps.CodeName,delete n.CodeName)),t[t.length]=Vt("workbookPr",null,n);var a=e.Workbook&&e.Workbook.Sheets||[],i=0;if(a&&a[0]&&a[0].Hidden){for(t[t.length]="<bookViews>",i=0;i!=e.SheetNames.length&&a[i]&&a[i].Hidden;++i);i==e.SheetNames.length&&(i=0),t[t.length]='<workbookView firstSheet="'+i+'" activeTab="'+i+'"/>',t[t.length]="</bookViews>"}for(t[t.length]="<sheets>",i=0;i!=e.SheetNames.length;++i){var l={name:Nt(e.SheetNames[i].slice(0,31))};if(l.sheetId=""+(i+1),l["r:id"]="rId"+(i+1),a[i])switch(a[i].Hidden){case 1:l.state="hidden";break;case 2:l.state="veryHidden"}t[t.length]=Vt("sheet",null,l)}return t[t.length]="</sheets>",r&&(t[t.length]="<definedNames>",e.Workbook&&e.Workbook.Names&&e.Workbook.Names.forEach((function(e){var r={name:e.Name};e.Comment&&(r.comment=e.Comment),null!=e.Sheet&&(r.localSheetId=""+e.Sheet),e.Hidden&&(r.hidden="1"),e.Ref&&(t[t.length]=Vt("definedName",Nt(e.Ref),r))})),t[t.length]="</definedNames>"),t.length>2&&(t[t.length]="</workbook>",t[1]=t[1].replace("/>",">")),t.join("")}function Xl(e,t){if(t.Workbook&&t.Workbook.Sheets){for(var r,n,a=t.Workbook.Sheets,i=0,l=-1,o=-1;i<a.length;++i)!a[i]||!a[i].Hidden&&-1==l?l=i:1==a[i].Hidden&&-1==o&&(o=i);if(!(o>l))qr(e,135),qr(e,158,(r=l,n||(n=Xr(29)),n.write_shift(-4,0),n.write_shift(-4,460),n.write_shift(4,28800),n.write_shift(4,17600),n.write_shift(4,500),n.write_shift(4,r),n.write_shift(4,r),n.write_shift(1,120),n.length>n.l?n.slice(0,n.l):n)),qr(e,136)}}function Ql(e,t){var r=Qr();return qr(r,131),qr(r,128,function(e,t){t||(t=Xr(127));for(var r=0;4!=r;++r)t.write_shift(4,0);return yn("SheetJS",t),yn(I.version,t),yn(I.version,t),yn("7262",t),t.length>t.l?t.slice(0,t.l):t}()),qr(r,153,function(e,t){t||(t=Xr(72));var r=0;return e&&e.filterPrivacy&&(r|=8),t.write_shift(4,r),t.write_shift(4,0),Nn(e&&e.CodeName||"ThisWorkbook",t),t.slice(0,t.l)}(e.Workbook&&e.Workbook.WBProps||null)),Xl(r,e),function(e,t){qr(e,143);for(var r=0;r!=t.SheetNames.length;++r){qr(e,156,(n={Hidden:t.Workbook&&t.Workbook.Sheets&&t.Workbook.Sheets[r]&&t.Workbook.Sheets[r].Hidden||0,iTabID:r+1,strRelID:"rId"+(r+1),name:t.SheetNames[r]},(a=void 0)||(a=Xr(127)),a.write_shift(4,n.Hidden),a.write_shift(4,n.iTabID),Dn(n.strRelID,a),yn(n.name.slice(0,31),a),a.length>a.l?a.slice(0,a.l):a))}var n,a;qr(e,144)}(r,e),qr(r,132),r.end()}function ql(e,t,r,n,a){return(".bin"===t.slice(-4)?Wl:Al)(e,r,n,a)}function Jl(e,t,r){return(".bin"===t.slice(-4)?Pi:Ni)(e,r)}function Zl(e,t){var r=[];return e.Props&&r.push(function(e,t){var r=[];return rt(fa).map((function(e){for(var t=0;t<aa.length;++t)if(aa[t][1]==e)return aa[t];for(t=0;t<oa.length;++t)if(oa[t][1]==e)return oa[t];throw e})).forEach((function(n){if(null!=e[n[1]]){var a=t&&t.Props&&null!=t.Props[n[1]]?t.Props[n[1]]:e[n[1]];"date"===n[2]&&(a=new Date(a).toISOString().replace(/\.\d*Z/,"Z")),"number"==typeof a?a=String(a):!0===a||!1===a?a=a?"1":"0":a instanceof Date&&(a=new Date(a).toISOString().replace(/\.\d*Z/,"")),r.push(Ht(fa[n[1]]||n[1],a))}})),Vt("DocumentProperties",r.join(""),{xmlns:lr})}(e.Props,t)),e.Custprops&&r.push(function(e,t){var r=["Worksheets","SheetNames"],n="CustomDocumentProperties",a=[];return e&&rt(e).forEach((function(t){if(Object.prototype.hasOwnProperty.call(e,t)){for(var n=0;n<aa.length;++n)if(t==aa[n][1])return;for(n=0;n<oa.length;++n)if(t==oa[n][1])return;for(n=0;n<r.length;++n)if(t==r[n])return;var i=e[t],l="string";"number"==typeof i?(l="float",i=String(i)):!0===i||!1===i?(l="boolean",i=i?"1":"0"):i=String(i),a.push(Vt(Ot(t),i,{"dt:dt":l}))}})),t&&rt(t).forEach((function(r){if(Object.prototype.hasOwnProperty.call(t,r)&&(!e||!Object.prototype.hasOwnProperty.call(e,r))){var n=t[r],i="string";"number"==typeof n?(i="float",n=String(n)):!0===n||!1===n?(i="boolean",n=n?"1":"0"):n instanceof Date?(i="dateTime.tz",n=n.toISOString()):n=String(n),a.push(Vt(Ot(r),n,{"dt:dt":i}))}})),"<"+n+' xmlns="'+lr+'">'+a.join("")+"</"+n+">"}(e.Props,e.Custprops)),r.join("")}function eo(e){return Vt("NamedRange",null,{"ss:Name":e.Name,"ss:RefersTo":"="+Mi(e.Ref,{r:0,c:0})})}function to(e,t,r,n,a,i,l){if(!e||void 0==e.v&&void 0==e.f)return"";var o={};if(e.f&&(o["ss:Formula"]="="+Nt(Mi(e.f,l))),e.F&&e.F.slice(0,t.length)==t){var s=on(e.F.slice(t.length+1));o["ss:ArrayRange"]="RC:R"+(s.r==l.r?"":"["+(s.r-l.r)+"]")+"C"+(s.c==l.c?"":"["+(s.c-l.c)+"]")}if(e.l&&e.l.Target&&(o["ss:HRef"]=Nt(e.l.Target),e.l.Tooltip&&(o["x:HRefScreenTip"]=Nt(e.l.Tooltip))),r["!merges"])for(var c=r["!merges"],u=0;u!=c.length;++u)c[u].s.c==l.c&&c[u].s.r==l.r&&(c[u].e.c>c[u].s.c&&(o["ss:MergeAcross"]=c[u].e.c-c[u].s.c),c[u].e.r>c[u].s.r&&(o["ss:MergeDown"]=c[u].e.r-c[u].s.r));var f="",h="";switch(e.t){case"z":if(!n.sheetStubs)return"";break;case"n":f="Number",h=String(e.v);break;case"b":f="Boolean",h=e.v?"1":"0";break;case"e":f="Error",h=Yn[e.v];break;case"d":f="DateTime",h=new Date(e.v).toISOString(),null==e.z&&(e.z=e.z||pe[14]);break;case"s":f="String",h=((e.v||"")+"").replace(Ct,(function(e){return _t[e]})).replace(Rt,(function(e){return"&#x"+e.charCodeAt(0).toString(16).toUpperCase()+";"}))}var d=Sl(n.cellXfs,e,n);o["ss:StyleID"]="s"+(21+d),o["ss:Index"]=l.c+1;var p=null!=e.v?h:"",m="z"==e.t?"":'<Data ss:Type="'+f+'">'+p+"</Data>";return(e.c||[]).length>0&&(m+=e.c.map((function(e){var t=Vt("ss:Data",(e.t||"").replace(/(\r\n|[\r\n])/g,"&#10;"),{xmlns:"http://www.w3.org/TR/REC-html40"});return Vt("Comment",t,{"ss:Author":e.a})})).join("")),Vt("Cell",m,o)}function ro(e,t){var r='<Row ss:Index="'+(e+1)+'"';return t&&(t.hpt&&!t.hpx&&(t.hpx=ci(t.hpt)),t.hpx&&(r+=' ss:AutoFitHeight="0" ss:Height="'+t.hpx+'"'),t.hidden&&(r+=' ss:Hidden="1"')),r+">"}function no(e,t,r){var n=[],a=r.SheetNames[e],i=r.Sheets[a],l=i?function(e,t,r,n){if(!e)return"";if(!((n||{}).Workbook||{}).Names)return"";for(var a=n.Workbook.Names,i=[],l=0;l<a.length;++l){var o=a[l];o.Sheet==r&&(o.Name.match(/^_xlfn\./)||i.push(eo(o)))}return i.join("")}(i,0,e,r):"";return l.length>0&&n.push("<Names>"+l+"</Names>"),l=i?function(e,t){if(!e["!ref"])return"";var r=fn(e["!ref"]),n=e["!merges"]||[],a=0,i=[];e["!cols"]&&e["!cols"].forEach((function(e,t){li(e);var r=!!e.width,n=Tl(t,e),a={"ss:Index":t+1};r&&(a["ss:Width"]=ni(n.width)),e.hidden&&(a["ss:Hidden"]="1"),i.push(Vt("Column",null,a))}));for(var l=Array.isArray(e),o=r.s.r;o<=r.e.r;++o){for(var s=[ro(o,(e["!rows"]||[])[o])],c=r.s.c;c<=r.e.c;++c){var u=!1;for(a=0;a!=n.length;++a)if(!(n[a].s.c>c)&&!(n[a].s.r>o)&&!(n[a].e.c<c)&&!(n[a].e.r<o)){n[a].s.c==c&&n[a].s.r==o||(u=!0);break}if(!u){var f={r:o,c:c},h=sn(f),d=l?(e[o]||[])[c]:e[h];s.push(to(d,h,e,t,0,0,f))}}s.push("</Row>"),s.length>2&&i.push(s.join(""))}return i.join("")}(i,t):"",l.length>0&&n.push("<Table>"+l+"</Table>"),n.push(function(e,t,r,n){if(!e)return"";var a=[];if(e["!margins"]&&(a.push("<PageSetup>"),e["!margins"].header&&a.push(Vt("Header",null,{"x:Margin":e["!margins"].header})),e["!margins"].footer&&a.push(Vt("Footer",null,{"x:Margin":e["!margins"].footer})),a.push(Vt("PageMargins",null,{"x:Bottom":e["!margins"].bottom||"0.75","x:Left":e["!margins"].left||"0.7","x:Right":e["!margins"].right||"0.7","x:Top":e["!margins"].top||"0.75"})),a.push("</PageSetup>")),n&&n.Workbook&&n.Workbook.Sheets&&n.Workbook.Sheets[r])if(n.Workbook.Sheets[r].Hidden)a.push(Vt("Visible",1==n.Workbook.Sheets[r].Hidden?"SheetHidden":"SheetVeryHidden",{}));else{for(var i=0;i<r&&(!n.Workbook.Sheets[i]||n.Workbook.Sheets[i].Hidden);++i);i==r&&a.push("<Selected/>")}return((((n||{}).Workbook||{}).Views||[])[0]||{}).RTL&&a.push("<DisplayRightToLeft/>"),e["!protect"]&&(a.push(Ht("ProtectContents","True")),e["!protect"].objects&&a.push(Ht("ProtectObjects","True")),e["!protect"].scenarios&&a.push(Ht("ProtectScenarios","True")),null==e["!protect"].selectLockedCells||e["!protect"].selectLockedCells?null==e["!protect"].selectUnlockedCells||e["!protect"].selectUnlockedCells||a.push(Ht("EnableSelection","UnlockedCells")):a.push(Ht("EnableSelection","NoSelection")),[["formatCells","AllowFormatCells"],["formatColumns","AllowSizeCols"],["formatRows","AllowSizeRows"],["insertColumns","AllowInsertCols"],["insertRows","AllowInsertRows"],["insertHyperlinks","AllowInsertHyperlinks"],["deleteColumns","AllowDeleteCols"],["deleteRows","AllowDeleteRows"],["sort","AllowSort"],["autoFilter","AllowFilter"],["pivotTables","AllowUsePivotTables"]].forEach((function(t){e["!protect"][t[0]]&&a.push("<"+t[1]+"/>")}))),0==a.length?"":Vt("WorksheetOptions",a.join(""),{xmlns:or})}(i,0,e,r)),n.join("")}function ao(e,t){t||(t={}),e.SSF||(e.SSF=vt(pe)),e.SSF&&(Xe(),Ye(e.SSF),t.revssf=it(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF,t.cellXfs=[],Sl(t.cellXfs,{},{revssf:{General:0}}));var r=[];r.push(Zl(e,t)),r.push(""),r.push(""),r.push("");for(var n=0;n<e.SheetNames.length;++n)r.push(Vt("Worksheet",no(n,t,e),{"ss:Name":Nt(e.SheetNames[n])}));return r[2]=function(e,t){var r=['<Style ss:ID="Default" ss:Name="Normal"><NumberFormat/></Style>'];return t.cellXfs.forEach((function(e,t){var n=[];n.push(Vt("NumberFormat",null,{"ss:Format":Nt(pe[e.numFmtId])}));var a={"ss:ID":"s"+(21+t)};r.push(Vt("Style",n.join(""),a))})),Vt("Styles",r.join(""))}(0,t),r[3]=function(e){if(!((e||{}).Workbook||{}).Names)return"";for(var t=e.Workbook.Names,r=[],n=0;n<t.length;++n){var a=t[n];null==a.Sheet&&(a.Name.match(/^_xlfn\./)||r.push(eo(a)))}return Vt("Names",r.join(""))}(e),xt+Vt("Workbook",r.join(""),{xmlns:sr,"xmlns:o":lr,"xmlns:x":or,"xmlns:ss":sr,"xmlns:dt":cr,"xmlns:html":hr})}var io="e0859ff2f94f6810ab9108002b27b3d9",lo="02d5cdd59c2e1b10939708002b2cf9ae",oo="05d5cdd59c2e1b10939708002b2cf9ae";function so(e,t){var r=t||{},n=Je.utils.cfb_new({root:"R"}),a="/Workbook";switch(r.bookType||"xls"){case"xls":r.bookType="biff8";case"xla":r.bookType||(r.bookType="xla");case"biff8":a="/Workbook",r.biff=8;break;case"biff5":a="/Book",r.biff=5;break;default:throw new Error("invalid type "+r.bookType+" for XLS CFB")}return Je.utils.cfb_add(n,a,wo(e,r)),8==r.biff&&(e.Props||e.Custprops)&&function(e,t){var r,n=[],a=[],i=[],l=0,o=nt(Vn,"n"),s=nt($n,"n");if(e.Props)for(r=rt(e.Props),l=0;l<r.length;++l)(Object.prototype.hasOwnProperty.call(o,r[l])?n:Object.prototype.hasOwnProperty.call(s,r[l])?a:i).push([r[l],e.Props[r[l]]]);if(e.Custprops)for(r=rt(e.Custprops),l=0;l<r.length;++l)Object.prototype.hasOwnProperty.call(e.Props||{},r[l])||(Object.prototype.hasOwnProperty.call(o,r[l])?n:Object.prototype.hasOwnProperty.call(s,r[l])?a:i).push([r[l],e.Custprops[r[l]]]);var c=[];for(l=0;l<i.length;++l)da.indexOf(i[l][0])>-1||sa.indexOf(i[l][0])>-1||null!=i[l][1]&&c.push(i[l]);a.length&&Je.utils.cfb_add(t,"/\x05SummaryInformation",ga(a,io,s,$n)),(n.length||c.length)&&Je.utils.cfb_add(t,"/\x05DocumentSummaryInformation",ga(n,lo,o,Vn,c.length?c:null,oo))}(e,n),8==r.biff&&e.vbaraw&&function(e,t){t.FullPaths.forEach((function(r,n){if(0!=n){var a=r.replace(/[^\/]*[\/]/,"/_VBA_PROJECT_CUR/");"/"!==a.slice(-1)&&Je.utils.cfb_add(e,a,t.FileIndex[n].content)}}))}(n,Je.read(e.vbaraw,{type:"string"==typeof e.vbaraw?"binary":"buffer"})),n}var co={0:{f:function(e,t){var r={},n=e.l+t;r.r=e.read_shift(4),e.l+=4;var a=e.read_shift(2);e.l+=1;var i=e.read_shift(1);return e.l=n,7&i&&(r.level=7&i),16&i&&(r.hidden=!0),32&i&&(r.hpt=a/20),r}},1:{f:function(e){return[xn(e)]}},2:{f:function(e){return[xn(e),Ln(e),"n"]}},3:{f:function(e){return[xn(e),e.read_shift(1),"e"]}},4:{f:function(e){return[xn(e),e.read_shift(1),"b"]}},5:{f:function(e){return[xn(e),Bn(e),"n"]}},6:{f:function(e){return[xn(e),bn(e),"str"]}},7:{f:function(e){return[xn(e),e.read_shift(4),"s"]}},8:{f:function(e,t,r){var n=e.l+t,a=xn(e);a.r=r["!row"];var i=[a,bn(e),"str"];if(r.cellFormula){e.l+=2;var l=dl(e,n-e.l,r);i[3]=cl(l,0,a,r.supbooks,r)}else e.l=n;return i}},9:{f:function(e,t,r){var n=e.l+t,a=xn(e);a.r=r["!row"];var i=[a,Bn(e),"n"];if(r.cellFormula){e.l+=2;var l=dl(e,n-e.l,r);i[3]=cl(l,0,a,r.supbooks,r)}else e.l=n;return i}},10:{f:function(e,t,r){var n=e.l+t,a=xn(e);a.r=r["!row"];var i=[a,e.read_shift(1),"b"];if(r.cellFormula){e.l+=2;var l=dl(e,n-e.l,r);i[3]=cl(l,0,a,r.supbooks,r)}else e.l=n;return i}},11:{f:function(e,t,r){var n=e.l+t,a=xn(e);a.r=r["!row"];var i=[a,e.read_shift(1),"e"];if(r.cellFormula){e.l+=2;var l=dl(e,n-e.l,r);i[3]=cl(l,0,a,r.supbooks,r)}else e.l=n;return i}},12:{f:function(e){return[_n(e)]}},13:{f:function(e){return[_n(e),Ln(e),"n"]}},14:{f:function(e){return[_n(e),e.read_shift(1),"e"]}},15:{f:function(e){return[_n(e),e.read_shift(1),"b"]}},16:{f:Pl},17:{f:function(e){return[_n(e),bn(e),"str"]}},18:{f:function(e){return[_n(e),e.read_shift(4),"s"]}},19:{f:Tn},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:function(e,t,r){var n=e.l+t;e.l+=4,e.l+=1;var a=e.read_shift(4),i=Pn(e),l=pl(e,0,r),o=On(e);e.l=n;var s={Name:i,Ptg:l};return a<268435455&&(s.Sheet=a),o&&(s.Comment=o),s}},40:{},42:{},43:{f:function(e,t,r){var n={};n.sz=e.read_shift(2)/20;var a=function(e){var t=e.read_shift(1);return e.l++,{fBold:1&t,fItalic:2&t,fUnderline:4&t,fStrikeout:8&t,fOutline:16&t,fShadow:32&t,fCondense:64&t,fExtend:128&t}}(e);switch(a.fItalic&&(n.italic=1),a.fCondense&&(n.condense=1),a.fExtend&&(n.extend=1),a.fShadow&&(n.shadow=1),a.fOutline&&(n.outline=1),a.fStrikeout&&(n.strike=1),700===e.read_shift(2)&&(n.bold=1),e.read_shift(2)){case 1:n.vertAlign="superscript";break;case 2:n.vertAlign="subscript"}var i=e.read_shift(1);0!=i&&(n.underline=i);var l=e.read_shift(1);l>0&&(n.family=l);var o=e.read_shift(1);switch(o>0&&(n.charset=o),e.l++,n.color=function(e){var t={},r=e.read_shift(1)>>>1,n=e.read_shift(1),a=e.read_shift(2,"i"),i=e.read_shift(1),l=e.read_shift(1),o=e.read_shift(1);switch(e.l++,r){case 0:t.auto=1;break;case 1:t.index=n;var s=Kn[n];s&&(t.rgb=ti(s));break;case 2:t.rgb=ti([i,l,o]);break;case 3:t.theme=n}return 0!=a&&(t.tint=a>0?a/32767:a/32768),t}(e),e.read_shift(1)){case 1:n.scheme="major";break;case 2:n.scheme="minor"}return n.name=bn(e),n}},44:{f:function(e,t){return[e.read_shift(2),bn(e)]}},45:{f:mi},46:{f:yi},47:{f:function(e,t){var r=e.l+t,n=e.read_shift(2),a=e.read_shift(2);return e.l=r,{ixfe:n,numFmtId:a}}},48:{},49:{f:function(e){return e.read_shift(4,"i")}},50:{},51:{f:function(e){for(var t=[],r=e.read_shift(4);r-- >0;)t.push([e.read_shift(4),e.read_shift(4)]);return t}},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:za},62:{f:function(e){return[xn(e),Tn(e),"is"]}},63:{f:function(e){var t={};t.i=e.read_shift(4);var r={};r.r=e.read_shift(4),r.c=e.read_shift(4),t.r=sn(r);var n=e.read_shift(1);return 2&n&&(t.l="1"),8&n&&(t.a="1"),t}},64:{f:function(){}},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:Yr,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:function(e){var t=e.read_shift(2);return e.l+=28,{RTL:32&t}}},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:function(e,t){var r={},n=e[e.l];return++e.l,r.above=!(64&n),r.left=!(128&n),e.l+=18,r.name=An(e,t-19),r}},148:{f:Ol,p:16},151:{f:function(){}},152:{},153:{f:function(e,t){var r={},n=e.read_shift(4);r.defaultThemeVersion=e.read_shift(4);var a=t>8?bn(e):"";return a.length>0&&(r.CodeName=a),r.autoCompressPictures=!!(65536&n),r.backupFile=!!(64&n),r.checkCompatibility=!!(4096&n),r.date1904=!!(1&n),r.filterPrivacy=!!(8&n),r.hidePivotFieldList=!!(1024&n),r.promptedSolutions=!!(16&n),r.publishItems=!!(2048&n),r.refreshAllConnections=!!(262144&n),r.saveExternalLinkValues=!!(128&n),r.showBorderUnselectedTables=!!(4&n),r.showInkAnnotation=!!(32&n),r.showObjects=["all","placeholders","none"][n>>13&3],r.showPivotChartFilter=!!(32768&n),r.updateLinks=["userSet","never","always"][n>>8&3],r}},154:{},155:{},156:{f:function(e,t){var r={};return r.Hidden=e.read_shift(4),r.iTabID=e.read_shift(4),r.strRelID=In(e,t-8),r.name=bn(e),r}},157:{},158:{},159:{T:1,f:function(e){return[e.read_shift(4),e.read_shift(4)]}},160:{T:-1},161:{T:1,f:jn},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:Il},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:function(e,t){return{flags:e.read_shift(4),version:e.read_shift(4),name:bn(e)}}},336:{T:-1},337:{f:function(e){return e.l+=4,0!=e.read_shift(4)},T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:In},357:{},358:{},359:{},360:{T:1},361:{},362:{f:ja},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:function(e,t,r){var n=e.l+t,a=Fn(e),i=e.read_shift(1),l=[a];if(l[2]=i,r.cellFormula){var o=hl(e,n-e.l,r);l[1]=o}else e.l=n;return l}},427:{f:function(e,t,r){var n=e.l+t,a=[jn(e,16)];if(r.cellFormula){var i=ml(e,n-e.l,r);a[1]=i,e.l=n}else e.l=n;return a}},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:function(e){var t={};return Ll.forEach((function(r){t[r]=Bn(e)})),t}},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:function(){}},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:function(e,t){var r=e.l+t,n=jn(e,16),a=On(e),i=bn(e),l=bn(e),o=bn(e);e.l=r;var s={rfx:n,relId:a,loc:i,display:o};return l&&(s.Tooltip=l),s}},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:In},551:{},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:Ri},633:{T:1},634:{T:-1},635:{T:1,f:function(e){var t={};t.iauthor=e.read_shift(4);var r=jn(e,16);return t.rfx=r.s,t.ref=sn(r.s),e.l+=16,t}},636:{T:-1},637:{f:En},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:function(e,t){return e.l+=10,{name:bn(e)}}},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:function(){}},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}};function uo(e,t,r,n){var a=t;if(!isNaN(a)){var i=n||(r||[]).length||0,l=e.next(4);l.write_shift(2,a),l.write_shift(2,i),i>0&&Ir(r)&&e.push(r)}}function fo(e,t,r){return e||(e=Xr(7)),e.write_shift(2,t),e.write_shift(2,r),e.write_shift(2,0),e.write_shift(1,0),e}function ho(e,t,r,n){if(null!=t.v)switch(t.t){case"d":case"n":var a="d"==t.t?ot(mt(t.v)):t.v;return void(a==(0|a)&&a>=0&&a<65536?uo(e,2,function(e,t,r){var n=Xr(9);return fo(n,e,t),n.write_shift(2,r),n}(r,n,a)):uo(e,3,function(e,t,r){var n=Xr(15);return fo(n,e,t),n.write_shift(8,r,"f"),n}(r,n,a)));case"b":case"e":return void uo(e,5,function(e,t,r,n){var a=Xr(9);return fo(a,e,t),Ta(r,n||"b",a),a}(r,n,t.v,t.t));case"s":case"str":return void uo(e,4,function(e,t,r){var n=Xr(8+2*r.length);return fo(n,e,t),n.write_shift(1,r.length),n.write_shift(r.length,r,"sbcs"),n.l<n.length?n.slice(0,n.l):n}(r,n,(t.v||"").slice(0,255)))}uo(e,1,fo(null,r,n))}function po(e,t){var r=t||{};for(var n=Qr(),a=0,i=0;i<e.SheetNames.length;++i)e.SheetNames[i]==r.sheet&&(a=i);if(0==a&&r.sheet&&e.SheetNames[0]!=r.sheet)throw new Error("Sheet not found: "+r.sheet);return uo(n,4==r.biff?1033:3==r.biff?521:9,Da(0,16,r)),function(e,t,r,n){var a,i=Array.isArray(t),l=fn(t["!ref"]||"A1"),o="",s=[];if(l.e.c>255||l.e.r>16383){if(n.WTF)throw new Error("Range "+(t["!ref"]||"A1")+" exceeds format limit A1:IV16384");l.e.c=Math.min(l.e.c,255),l.e.r=Math.min(l.e.c,16383),a=un(l)}for(var c=l.s.r;c<=l.e.r;++c){o=nn(c);for(var u=l.s.c;u<=l.e.c;++u){c===l.s.r&&(s[u]=ln(u)),a=s[u]+o;var f=i?(t[c]||[])[u]:t[a];f&&ho(e,f,c,u)}}}(n,e.Sheets[e.SheetNames[a]],0,r),uo(n,10),n.end()}function mo(e,t,r){uo(e,49,function(e,t){var r=e.name||"Arial",n=t&&5==t.biff,a=Xr(n?15+r.length:16+2*r.length);return a.write_shift(2,20*(e.sz||12)),a.write_shift(4,0),a.write_shift(2,400),a.write_shift(4,0),a.write_shift(2,0),a.write_shift(1,r.length),n||a.write_shift(1,1),a.write_shift((n?1:2)*r.length,r,n?"sbcs":"utf16le"),a}({sz:12,color:{theme:1},name:"Arial",family:2,scheme:"minor"},r))}function go(e,t){if(t){var r=0;t.forEach((function(t,n){++r<=256&&t&&uo(e,125,function(e,t){var r=Xr(12);r.write_shift(2,t),r.write_shift(2,t),r.write_shift(2,256*e.width),r.write_shift(2,0);var n=0;return e.hidden&&(n|=1),r.write_shift(1,n),n=e.level||0,r.write_shift(1,n),r.write_shift(2,0),r}(Tl(n,t),n))}))}}function vo(e,t,r,n,a){var i=16+Sl(a.cellXfs,t,a);if(null!=t.v||t.bf)if(t.bf)uo(e,6,ul(t,r,n,0,i));else switch(t.t){case"d":case"n":uo(e,515,function(e,t,r,n){var a=Xr(14);return Oa(e,t,n,a),zn(r,a),a}(r,n,"d"==t.t?ot(mt(t.v)):t.v,i));break;case"b":case"e":uo(e,517,function(e,t,r,n,a,i){var l=Xr(8);return Oa(e,t,n,l),Ta(r,i,l),l}(r,n,t.v,i,0,t.t));break;case"s":case"str":if(a.bookSST)uo(e,253,function(e,t,r,n){var a=Xr(10);return Oa(e,t,n,a),a.write_shift(4,r),a}(r,n,wl(a.Strings,t.v,a.revStrings),i));else uo(e,516,function(e,t,r,n,a){var i=!a||8==a.biff,l=Xr(+i+8+(1+i)*r.length);return Oa(e,t,n,l),l.write_shift(2,r.length),i&&l.write_shift(1,1),l.write_shift((1+i)*r.length,r,i?"utf16le":"sbcs"),l}(r,n,(t.v||"").slice(0,255),i,a));break;default:uo(e,513,Oa(r,n,i))}else uo(e,513,Oa(r,n,i))}function bo(e,t,r){var n,a=Qr(),i=r.SheetNames[e],l=r.Sheets[i]||{},o=(r||{}).Workbook||{},s=(o.Sheets||[])[e]||{},c=Array.isArray(l),u=8==t.biff,f="",h=[],d=fn(l["!ref"]||"A1"),p=u?65536:16384;if(d.e.c>255||d.e.r>=p){if(t.WTF)throw new Error("Range "+(l["!ref"]||"A1")+" exceeds format limit A1:IV16384");d.e.c=Math.min(d.e.c,255),d.e.r=Math.min(d.e.c,p-1)}uo(a,2057,Da(0,16,t)),uo(a,13,wa(1)),uo(a,12,wa(100)),uo(a,15,ba(!0)),uo(a,17,ba(!1)),uo(a,16,zn(.001)),uo(a,95,ba(!0)),uo(a,42,ba(!1)),uo(a,43,ba(!1)),uo(a,130,wa(1)),uo(a,128,function(e){var t=Xr(8);return t.write_shift(4,0),t.write_shift(2,e[0]?e[0]+1:0),t.write_shift(2,e[1]?e[1]+1:0),t}([0,0])),uo(a,131,ba(!1)),uo(a,132,ba(!1)),u&&go(a,l["!cols"]),uo(a,512,function(e,t){var r=8!=t.biff&&t.biff?2:4,n=Xr(2*r+6);return n.write_shift(r,e.s.r),n.write_shift(r,e.e.r+1),n.write_shift(2,e.s.c),n.write_shift(2,e.e.c+1),n.write_shift(2,0),n}(d,t)),u&&(l["!links"]=[]);for(var m=d.s.r;m<=d.e.r;++m){f=nn(m);for(var g=d.s.c;g<=d.e.c;++g){m===d.s.r&&(h[g]=ln(g)),n=h[g]+f;var v=c?(l[m]||[])[g]:l[n];v&&(vo(a,v,m,g,t),u&&v.l&&l["!links"].push([n,v.l]))}}var b=s.CodeName||s.name||i;return u&&uo(a,574,function(e){var t=Xr(18),r=1718;return e&&e.RTL&&(r|=64),t.write_shift(2,r),t.write_shift(4,0),t.write_shift(4,64),t.write_shift(4,0),t.write_shift(4,0),t}((o.Views||[])[0])),u&&(l["!merges"]||[]).length&&uo(a,229,function(e){var t=Xr(2+8*e.length);t.write_shift(2,e.length);for(var r=0;r<e.length;++r)Ia(e[r],t);return t}(l["!merges"])),u&&function(e,t){for(var r=0;r<t["!links"].length;++r){var n=t["!links"][r];uo(e,440,Ua(n)),n[1].Tooltip&&uo(e,2048,Ba(n))}delete t["!links"]}(a,l),uo(a,442,Ca(b)),u&&function(e,t){var r=Xr(19);r.write_shift(4,2151),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,1),r.write_shift(4,0),uo(e,2151,r),(r=Xr(39)).write_shift(4,2152),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,0),r.write_shift(4,0),r.write_shift(2,1),r.write_shift(4,4),r.write_shift(2,0),Ia(fn(t["!ref"]||"A1"),r),r.write_shift(4,4),uo(e,2152,r)}(a,l),uo(a,10),a.end()}function yo(e,t,r){var n=Qr(),a=(e||{}).Workbook||{},i=a.Sheets||[],l=a.WBProps||{},o=8==r.biff,s=5==r.biff;(uo(n,2057,Da(0,5,r)),"xla"==r.bookType&&uo(n,135),uo(n,225,o?wa(1200):null),uo(n,193,function(e,t){t||(t=Xr(e));for(var r=0;r<e;++r)t.write_shift(1,0);return t}(2)),s&&uo(n,191),s&&uo(n,192),uo(n,226),uo(n,92,function(e,t){var r=!t||8==t.biff,n=Xr(r?112:54);for(n.write_shift(8==t.biff?2:1,7),r&&n.write_shift(1,0),n.write_shift(4,859007059),n.write_shift(4,5458548|(r?0:536870912));n.l<n.length;)n.write_shift(1,r?0:32);return n}(0,r)),uo(n,66,wa(o?1200:1252)),o&&uo(n,353,wa(0)),o&&uo(n,448),uo(n,317,function(e){for(var t=Xr(2*e),r=0;r<e;++r)t.write_shift(2,r+1);return t}(e.SheetNames.length)),o&&e.vbaraw&&uo(n,211),o&&e.vbaraw)&&uo(n,442,Ca(l.CodeName||"ThisWorkbook"));uo(n,156,wa(17)),uo(n,25,ba(!1)),uo(n,18,ba(!1)),uo(n,19,wa(0)),o&&uo(n,431,ba(!1)),o&&uo(n,444,wa(0)),uo(n,61,function(){var e=Xr(18);return e.write_shift(2,0),e.write_shift(2,0),e.write_shift(2,29280),e.write_shift(2,17600),e.write_shift(2,56),e.write_shift(2,0),e.write_shift(2,0),e.write_shift(2,1),e.write_shift(2,500),e}()),uo(n,64,ba(!1)),uo(n,141,wa(0)),uo(n,34,ba("true"==function(e){return e.Workbook&&e.Workbook.WBProps&&It(e.Workbook.WBProps.date1904)?"true":"false"}(e))),uo(n,14,ba(!0)),o&&uo(n,439,ba(!1)),uo(n,218,wa(0)),mo(n,0,r),function(e,t,r){t&&[[5,8],[23,26],[41,44],[50,392]].forEach((function(n){for(var a=n[0];a<=n[1];++a)null!=t[a]&&uo(e,1054,Ma(a,t[a],r))}))}(n,e.SSF,r),function(e,t){for(var r=0;r<16;++r)uo(e,224,Fa({numFmtId:0,style:!0},0,t));t.cellXfs.forEach((function(r){uo(e,224,Fa(r,0,t))}))}(n,r),o&&uo(n,352,ba(!1));var c,u=n.end(),f=Qr();o&&uo(f,140,(c||(c=Xr(4)),c.write_shift(2,1),c.write_shift(2,1),c)),o&&r.Strings&&function(e,t,r,n){var a=n||(r||[]).length||0;if(a<=8224)return uo(e,t,r,a);var i=t;if(!isNaN(i)){for(var l=r.parts||[],o=0,s=0,c=0;c+(l[o]||8224)<=8224;)c+=l[o]||8224,o++;var u=e.next(4);for(u.write_shift(2,i),u.write_shift(2,c),e.push(r.slice(s,s+c)),s+=c;s<a;){for((u=e.next(4)).write_shift(2,60),c=0;c+(l[o]||8224)<=8224;)c+=l[o]||8224,o++;u.write_shift(2,c),e.push(r.slice(s,s+c)),s+=c}}}(f,252,function(e){var t=Xr(8);t.write_shift(4,e.Count),t.write_shift(4,e.Unique);for(var r=[],n=0;n<e.length;++n)r[n]=Sa(e[n]);var a=re([t].concat(r));return a.parts=[t.length].concat(r.map((function(e){return e.length}))),a}(r.Strings)),uo(f,10);var h=f.end(),d=Qr(),p=0,m=0;for(m=0;m<e.SheetNames.length;++m)p+=(o?12:11)+(o?2:1)*e.SheetNames[m].length;var g=u.length+p+h.length;for(m=0;m<e.SheetNames.length;++m){uo(d,133,La({pos:g,hs:(i[m]||{}).Hidden||0,dt:0,name:e.SheetNames[m]},r)),g+=t[m].length}var v=d.end();if(p!=v.length)throw new Error("BS8 "+p+" != "+v.length);var b=[];return u.length&&b.push(u),v.length&&b.push(v),h.length&&b.push(h),re(b)}function wo(e,t){for(var r=0;r<=e.SheetNames.length;++r){var n=e.Sheets[e.SheetNames[r]];if(n&&n["!ref"])cn(n["!ref"]).e.c>255&&"undefined"!=typeof console&&console.error&&console.error("Worksheet '"+e.SheetNames[r]+"' extends beyond column IV (255).  Data may be lost.")}var a=t||{};switch(a.biff||2){case 8:case 5:return function(e,t){var r=t||{},n=[];e&&!e.SSF&&(e.SSF=vt(pe)),e&&e.SSF&&(Xe(),Ye(e.SSF),r.revssf=it(e.SSF),r.revssf[e.SSF[65535]]=0,r.ssf=e.SSF),r.Strings=[],r.Strings.Count=0,r.Strings.Unique=0,Qo(r),r.cellXfs=[],Sl(r.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={});for(var a=0;a<e.SheetNames.length;++a)n[n.length]=bo(a,r,e);return n.unshift(yo(e,n,r)),re(n)}(e,t);case 4:case 3:case 2:return po(e,t)}throw new Error("invalid type "+a.bookType+" for BIFF")}function To(e,t,r,n){for(var a=e["!merges"]||[],i=[],l=t.s.c;l<=t.e.c;++l){for(var o=0,s=0,c=0;c<a.length;++c)if(!(a[c].s.r>r||a[c].s.c>l)&&!(a[c].e.r<r||a[c].e.c<l)){if(a[c].s.r<r||a[c].s.c<l){o=-1;break}o=a[c].e.r-a[c].s.r+1,s=a[c].e.c-a[c].s.c+1;break}if(!(o<0)){var u=sn({r:r,c:l}),f=n.dense?(e[r]||[])[l]:e[u],h=f&&null!=f.v&&(f.h||Pt(f.w||(dn(f),f.w)||""))||"",d={};o>1&&(d.rowspan=o),s>1&&(d.colspan=s),n.editable?h='<span contenteditable="true">'+h+"</span>":f&&(d["data-t"]=f&&f.t||"z",null!=f.v&&(d["data-v"]=f.v),null!=f.z&&(d["data-z"]=f.z),f.l&&"#"!=(f.l.Target||"#").charAt(0)&&(h='<a href="'+f.l.Target+'">'+h+"</a>")),d.id=(n.id||"sjs")+"-"+u,i.push(Vt("td",h,d))}}return"<tr>"+i.join("")+"</tr>"}var Eo='<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',So="</body></html>";function xo(e,t,r){return[].join("")+"<table"+(r&&r.id?' id="'+r.id+'"':"")+">"}function ko(e,t){var r=t||{},n=null!=r.header?r.header:Eo,a=null!=r.footer?r.footer:So,i=[n],l=cn(e["!ref"]);r.dense=Array.isArray(e),i.push(xo(0,0,r));for(var o=l.s.r;o<=l.e.r;++o)i.push(To(e,l,o,r));return i.push("</table>"+a),i.join("")}function _o(e,t,r){var n=r||{};var a=0,i=0;if(null!=n.origin)if("number"==typeof n.origin)a=n.origin;else{var l="string"==typeof n.origin?on(n.origin):n.origin;a=l.r,i=l.c}var o=t.getElementsByTagName("tr"),s=Math.min(n.sheetRows||1e7,o.length),c={s:{r:0,c:0},e:{r:a,c:i}};if(e["!ref"]){var u=cn(e["!ref"]);c.s.r=Math.min(c.s.r,u.s.r),c.s.c=Math.min(c.s.c,u.s.c),c.e.r=Math.max(c.e.r,u.e.r),c.e.c=Math.max(c.e.c,u.e.c),-1==a&&(c.e.r=a=u.e.r+1)}var f=[],h=0,d=e["!rows"]||(e["!rows"]=[]),p=0,m=0,g=0,v=0,b=0,y=0;for(e["!cols"]||(e["!cols"]=[]);p<o.length&&m<s;++p){var w=o[p];if(Ao(w)){if(n.display)continue;d[m]={hidden:!0}}var T=w.children;for(g=v=0;g<T.length;++g){var E=T[g];if(!n.display||!Ao(E)){var S=E.hasAttribute("data-v")?E.getAttribute("data-v"):E.hasAttribute("v")?E.getAttribute("v"):Bt(E.innerHTML),x=E.getAttribute("data-z")||E.getAttribute("z");for(h=0;h<f.length;++h){var k=f[h];k.s.c==v+i&&k.s.r<m+a&&m+a<=k.e.r&&(v=k.e.c+1-i,h=-1)}y=+E.getAttribute("colspan")||1,((b=+E.getAttribute("rowspan")||1)>1||y>1)&&f.push({s:{r:m+a,c:v+i},e:{r:m+a+(b||1)-1,c:v+i+(y||1)-1}});var _={t:"s",v:S},C=E.getAttribute("data-t")||E.getAttribute("t")||"";null!=S&&(0==S.length?_.t=C||"z":n.raw||0==S.trim().length||"s"==C||("TRUE"===S?_={t:"b",v:!0}:"FALSE"===S?_={t:"b",v:!1}:isNaN(yt(S))?isNaN(Tt(S).getDate())||(_={t:"d",v:mt(S)},n.cellDates||(_={t:"n",v:ot(_.v)}),_.z=n.dateNF||pe[14]):_={t:"n",v:yt(S)})),void 0===_.z&&null!=x&&(_.z=x);var A="",N=E.getElementsByTagName("A");if(N&&N.length)for(var O=0;O<N.length&&(!N[O].hasAttribute("href")||"#"==(A=N[O].getAttribute("href")).charAt(0));++O);A&&"#"!=A.charAt(0)&&(_.l={Target:A}),n.dense?(e[m+a]||(e[m+a]=[]),e[m+a][v+i]=_):e[sn({c:v+i,r:m+a})]=_,c.e.c<v+i&&(c.e.c=v+i),v+=y}}++m}return f.length&&(e["!merges"]=(e["!merges"]||[]).concat(f)),c.e.r=Math.max(c.e.r,m-1+a),e["!ref"]=un(c),m>=s&&(e["!fullref"]=un((c.e.r=o.length-p+m-1+a,c))),e}function Co(e,t){return _o((t||{}).dense?[]:{},e,t)}function Ao(e){var t="",r=function(e){return e.ownerDocument.defaultView&&"function"===typeof e.ownerDocument.defaultView.getComputedStyle?e.ownerDocument.defaultView.getComputedStyle:"function"===typeof getComputedStyle?getComputedStyle:null}(e);return r&&(t=r(e).getPropertyValue("display")),t||(t=e.style&&e.style.display),"none"===t}var No=function(){var e=["<office:master-styles>",'<style:master-page style:name="mp1" style:page-layout-name="mp1">',"<style:header/>",'<style:header-left style:display="false"/>',"<style:footer/>",'<style:footer-left style:display="false"/>',"</style:master-page>","</office:master-styles>"].join(""),t="<office:document-styles "+Wt({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","office:version":"1.2"})+">"+e+"</office:document-styles>";return function(){return xt+t}}(),Oo=function(){var e="          <table:table-cell />\n",t=function(t,r,n){var a=[];a.push('      <table:table table:name="'+Nt(r.SheetNames[n])+'" table:style-name="ta1">\n');var i=0,l=0,o=cn(t["!ref"]||"A1"),s=t["!merges"]||[],c=0,u=Array.isArray(t);if(t["!cols"])for(l=0;l<=o.e.c;++l)a.push("        <table:table-column"+(t["!cols"][l]?' table:style-name="co'+t["!cols"][l].ods+'"':"")+"></table:table-column>\n");var f="",h=t["!rows"]||[];for(i=0;i<o.s.r;++i)f=h[i]?' table:style-name="ro'+h[i].ods+'"':"",a.push("        <table:table-row"+f+"></table:table-row>\n");for(;i<=o.e.r;++i){for(f=h[i]?' table:style-name="ro'+h[i].ods+'"':"",a.push("        <table:table-row"+f+">\n"),l=0;l<o.s.c;++l)a.push(e);for(;l<=o.e.c;++l){var d=!1,p={},m="";for(c=0;c!=s.length;++c)if(!(s[c].s.c>l)&&!(s[c].s.r>i)&&!(s[c].e.c<l)&&!(s[c].e.r<i)){s[c].s.c==l&&s[c].s.r==i||(d=!0),p["table:number-columns-spanned"]=s[c].e.c-s[c].s.c+1,p["table:number-rows-spanned"]=s[c].e.r-s[c].s.r+1;break}if(d)a.push("          <table:covered-table-cell/>\n");else{var g=sn({r:i,c:l}),v=u?(t[i]||[])[l]:t[g];if(v&&v.f&&(p["table:formula"]=Nt(("of:="+v.f.replace(Li,"$1[.$2$3$4$5]").replace(/\]:\[/g,":")).replace(/;/g,"|").replace(/,/g,";")),v.F&&v.F.slice(0,g.length)==g)){var b=cn(v.F);p["table:number-matrix-columns-spanned"]=b.e.c-b.s.c+1,p["table:number-matrix-rows-spanned"]=b.e.r-b.s.r+1}if(v){switch(v.t){case"b":m=v.v?"TRUE":"FALSE",p["office:value-type"]="boolean",p["office:boolean-value"]=v.v?"true":"false";break;case"n":m=v.w||String(v.v||0),p["office:value-type"]="float",p["office:value"]=v.v||0;break;case"s":case"str":m=null==v.v?"":v.v,p["office:value-type"]="string";break;case"d":m=v.w||mt(v.v).toISOString(),p["office:value-type"]="date",p["office:date-value"]=mt(v.v).toISOString(),p["table:style-name"]="ce1";break;default:a.push(e);continue}var y=Nt(m).replace(/  +/g,(function(e){return'<text:s text:c="'+e.length+'"/>'})).replace(/\t/g,"<text:tab/>").replace(/\n/g,"</text:p><text:p>").replace(/^ /,"<text:s/>").replace(/ $/,"<text:s/>");if(v.l&&v.l.Target){var w=v.l.Target;"#"==(w="#"==w.charAt(0)?"#"+w.slice(1).replace(/\./,"!"):w).charAt(0)||w.match(/^\w+:/)||(w="../"+w),y=Vt("text:a",y,{"xlink:href":w.replace(/&/g,"&amp;")})}a.push("          "+Vt("table:table-cell",Vt("text:p",y,{}),p)+"\n")}else a.push(e)}}a.push("        </table:table-row>\n")}return a.push("      </table:table>\n"),a.join("")};return function(e,r){var n=[xt],a=Wt({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:meta":"urn:oasis:names:tc:opendocument:xmlns:meta:1.0","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:presentation":"urn:oasis:names:tc:opendocument:xmlns:presentation:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:chart":"urn:oasis:names:tc:opendocument:xmlns:chart:1.0","xmlns:dr3d":"urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0","xmlns:math":"http://www.w3.org/1998/Math/MathML","xmlns:form":"urn:oasis:names:tc:opendocument:xmlns:form:1.0","xmlns:script":"urn:oasis:names:tc:opendocument:xmlns:script:1.0","xmlns:ooo":"http://openoffice.org/2004/office","xmlns:ooow":"http://openoffice.org/2004/writer","xmlns:oooc":"http://openoffice.org/2004/calc","xmlns:dom":"http://www.w3.org/2001/xml-events","xmlns:xforms":"http://www.w3.org/2002/xforms","xmlns:xsd":"http://www.w3.org/2001/XMLSchema","xmlns:xsi":"http://www.w3.org/2001/XMLSchema-instance","xmlns:sheet":"urn:oasis:names:tc:opendocument:sh33tjs:1.0","xmlns:rpt":"http://openoffice.org/2005/report","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","xmlns:xhtml":"http://www.w3.org/1999/xhtml","xmlns:grddl":"http://www.w3.org/2003/g/data-view#","xmlns:tableooo":"http://openoffice.org/2009/table","xmlns:drawooo":"http://openoffice.org/2010/draw","xmlns:calcext":"urn:org:documentfoundation:names:experimental:calc:xmlns:calcext:1.0","xmlns:loext":"urn:org:documentfoundation:names:experimental:office:xmlns:loext:1.0","xmlns:field":"urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0","xmlns:formx":"urn:openoffice:names:experimental:ooxml-odf-interop:xmlns:form:1.0","xmlns:css3t":"http://www.w3.org/TR/css3-text/","office:version":"1.2"}),i=Wt({"xmlns:config":"urn:oasis:names:tc:opendocument:xmlns:config:1.0","office:mimetype":"application/vnd.oasis.opendocument.spreadsheet"});"fods"==r.bookType?(n.push("<office:document"+a+i+">\n"),n.push(na().replace(/office:document-meta/g,"office:meta"))):n.push("<office:document-content"+a+">\n"),function(e,t){e.push(" <office:automatic-styles>\n"),e.push('  <number:date-style style:name="N37" number:automatic-order="true">\n'),e.push('   <number:month number:style="long"/>\n'),e.push("   <number:text>/</number:text>\n"),e.push('   <number:day number:style="long"/>\n'),e.push("   <number:text>/</number:text>\n"),e.push("   <number:year/>\n"),e.push("  </number:date-style>\n");var r=0;t.SheetNames.map((function(e){return t.Sheets[e]})).forEach((function(t){if(t&&t["!cols"])for(var n=0;n<t["!cols"].length;++n)if(t["!cols"][n]){var a=t["!cols"][n];if(null==a.width&&null==a.wpx&&null==a.wch)continue;li(a),a.ods=r;var i=t["!cols"][n].wpx+"px";e.push('  <style:style style:name="co'+r+'" style:family="table-column">\n'),e.push('   <style:table-column-properties fo:break-before="auto" style:column-width="'+i+'"/>\n'),e.push("  </style:style>\n"),++r}}));var n=0;t.SheetNames.map((function(e){return t.Sheets[e]})).forEach((function(t){if(t&&t["!rows"])for(var r=0;r<t["!rows"].length;++r)if(t["!rows"][r]){t["!rows"][r].ods=n;var a=t["!rows"][r].hpx+"px";e.push('  <style:style style:name="ro'+n+'" style:family="table-row">\n'),e.push('   <style:table-row-properties fo:break-before="auto" style:row-height="'+a+'"/>\n'),e.push("  </style:style>\n"),++n}})),e.push('  <style:style style:name="ta1" style:family="table" style:master-page-name="mp1">\n'),e.push('   <style:table-properties table:display="true" style:writing-mode="lr-tb"/>\n'),e.push("  </style:style>\n"),e.push('  <style:style style:name="ce1" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N37"/>\n'),e.push(" </office:automatic-styles>\n")}(n,e),n.push("  <office:body>\n"),n.push("    <office:spreadsheet>\n");for(var l=0;l!=e.SheetNames.length;++l)n.push(t(e.Sheets[e.SheetNames[l]],e,l));return n.push("    </office:spreadsheet>\n"),n.push("  </office:body>\n"),"fods"==r.bookType?n.push("</office:document>"):n.push("</office:document-content>"),n.join("")}}();function Ro(e,t){if("fods"==t.bookType)return Oo(e,t);var r=St(),n="",a=[],i=[];return Et(r,n="mimetype","application/vnd.oasis.opendocument.spreadsheet"),Et(r,n="content.xml",Oo(e,t)),a.push([n,"text/xml"]),i.push([n,"ContentFile"]),Et(r,n="styles.xml",No(e,t)),a.push([n,"text/xml"]),i.push([n,"StylesFile"]),Et(r,n="meta.xml",xt+na()),a.push([n,"text/xml"]),i.push([n,"MetadataFile"]),Et(r,n="manifest.rdf",function(e){var t,r,n=[xt];n.push('<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">\n');for(var a=0;a!=e.length;++a)n.push(ra(e[a][0],e[a][1])),n.push((t="",r=e[a][0],['  <rdf:Description rdf:about="'+t+'">\n','    <ns0:hasPart xmlns:ns0="http://docs.oasis-open.org/ns/office/1.2/meta/pkg#" rdf:resource="'+r+'"/>\n',"  </rdf:Description>\n"].join("")));return n.push(ra("","Document","pkg")),n.push("</rdf:RDF>"),n.join("")}(i)),a.push([n,"application/rdf+xml"]),Et(r,n="META-INF/manifest.xml",function(e){var t=[xt];t.push('<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0" manifest:version="1.2">\n'),t.push('  <manifest:file-entry manifest:full-path="/" manifest:version="1.2" manifest:media-type="application/vnd.oasis.opendocument.spreadsheet"/>\n');for(var r=0;r<e.length;++r)t.push('  <manifest:file-entry manifest:full-path="'+e[r][0]+'" manifest:media-type="'+e[r][1]+'"/>\n');return t.push("</manifest:manifest>"),t.join("")}(a)),r}function Po(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function Io(e){return"undefined"!=typeof TextEncoder?(new TextEncoder).encode(e):Z(Ut(e))}function Do(e){var t=e.reduce((function(e,t){return e+t.length}),0),r=new Uint8Array(t),n=0;return e.forEach((function(e){r.set(e,n),n+=e.length})),r}function Lo(e,t){var r=t?t[0]:0,n=127&e[r];e:if(e[r++]>=128){if(n|=(127&e[r])<<7,e[r++]<128)break e;if(n|=(127&e[r])<<14,e[r++]<128)break e;if(n|=(127&e[r])<<21,e[r++]<128)break e;if(n+=(127&e[r])*Math.pow(2,28),++r,e[r++]<128)break e;if(n+=(127&e[r])*Math.pow(2,35),++r,e[r++]<128)break e;if(n+=(127&e[r])*Math.pow(2,42),++r,e[r++]<128)break e}return t&&(t[0]=r),n}function Mo(e){var t=new Uint8Array(7);t[0]=127&e;var r=1;e:if(e>127){if(t[r-1]|=128,t[r]=e>>7&127,++r,e<=16383)break e;if(t[r-1]|=128,t[r]=e>>14&127,++r,e<=2097151)break e;if(t[r-1]|=128,t[r]=e>>21&127,++r,e<=268435455)break e;if(t[r-1]|=128,t[r]=e/256>>>21&127,++r,e<=34359738367)break e;if(t[r-1]|=128,t[r]=e/65536>>>21&127,++r,e<=4398046511103)break e;t[r-1]|=128,t[r]=e/16777216>>>21&127,++r}return t.slice(0,r)}function Fo(e){var t=0,r=127&e[t];e:if(e[t++]>=128){if(r|=(127&e[t])<<7,e[t++]<128)break e;if(r|=(127&e[t])<<14,e[t++]<128)break e;if(r|=(127&e[t])<<21,e[t++]<128)break e;r|=(127&e[t])<<28}return r}function jo(e){for(var t=[],r=[0];r[0]<e.length;){var n,a=r[0],i=Lo(e,r),l=7&i,o=0;if(0==(i=Math.floor(i/8)))break;switch(l){case 0:for(var s=r[0];e[r[0]++]>=128;);n=e.slice(s,r[0]);break;case 5:o=4,n=e.slice(r[0],r[0]+o),r[0]+=o;break;case 1:o=8,n=e.slice(r[0],r[0]+o),r[0]+=o;break;case 2:o=Lo(e,r),n=e.slice(r[0],r[0]+o),r[0]+=o;break;default:throw new Error("PB Type ".concat(l," for Field ").concat(i," at offset ").concat(a))}var c={data:n,type:l};null==t[i]?t[i]=[c]:t[i].push(c)}return t}function Uo(e){var t=[];return e.forEach((function(e,r){e.forEach((function(e){e.data&&(t.push(Mo(8*r+e.type)),2==e.type&&t.push(Mo(e.data.length)),t.push(e.data))}))})),Do(t)}function Bo(e){for(var t,r=[],n=[0];n[0]<e.length;){var a=Lo(e,n),i=jo(e.slice(n[0],n[0]+a));n[0]+=a;var l={id:Fo(i[1][0].data),messages:[]};i[2].forEach((function(t){var r=jo(t.data),a=Fo(r[3][0].data);l.messages.push({meta:r,data:e.slice(n[0],n[0]+a)}),n[0]+=a})),(null==(t=i[3])?void 0:t[0])&&(l.merge=Fo(i[3][0].data)>>>0>0),r.push(l)}return r}function zo(e){var t=[];return e.forEach((function(e){var r=[];r[1]=[{data:Mo(e.id),type:0}],r[2]=[],null!=e.merge&&(r[3]=[{data:Mo(+!!e.merge),type:0}]);var n=[];e.messages.forEach((function(e){n.push(e.data),e.meta[3]=[{type:0,data:Mo(e.data.length)}],r[2].push({data:Uo(e.meta),type:2})}));var a=Uo(r);t.push(Mo(a.length)),t.push(a),n.forEach((function(e){return t.push(e)}))})),Do(t)}function Ho(e,t){if(0!=e)throw new Error("Unexpected Snappy chunk type ".concat(e));for(var r=[0],n=Lo(t,r),a=[];r[0]<t.length;){var i=3&t[r[0]];if(0!=i){var l=0,o=0;if(1==i?(o=4+(t[r[0]]>>2&7),l=(224&t[r[0]++])<<3,l|=t[r[0]++]):(o=1+(t[r[0]++]>>2),2==i?(l=t[r[0]]|t[r[0]+1]<<8,r[0]+=2):(l=(t[r[0]]|t[r[0]+1]<<8|t[r[0]+2]<<16|t[r[0]+3]<<24)>>>0,r[0]+=4)),a=[Do(a)],0==l)throw new Error("Invalid offset 0");if(l>a[0].length)throw new Error("Invalid offset beyond length");if(o>=l)for(a.push(a[0].slice(-l)),o-=l;o>=a[a.length-1].length;)a.push(a[a.length-1]),o-=a[a.length-1].length;a.push(a[0].slice(-l,-l+o))}else{var s=t[r[0]++]>>2;if(s<60)++s;else{var c=s-59;s=t[r[0]],c>1&&(s|=t[r[0]+1]<<8),c>2&&(s|=t[r[0]+2]<<16),c>3&&(s|=t[r[0]+3]<<24),s>>>=0,s++,r[0]+=c}a.push(t.slice(r[0],r[0]+s)),r[0]+=s}}var u=Do(a);if(u.length!=n)throw new Error("Unexpected length: ".concat(u.length," != ").concat(n));return u}function Wo(e){for(var t=[],r=0;r<e.length;){var n=e[r++],a=e[r]|e[r+1]<<8|e[r+2]<<16;r+=3,t.push(Ho(n,e.slice(r,r+a))),r+=a}if(r!==e.length)throw new Error("data is not a valid framed stream!");return Do(t)}function Vo(e){for(var t=[],r=0;r<e.length;){var n=Math.min(e.length-r,268435455),a=new Uint8Array(4);t.push(a);var i=Mo(n),l=i.length;t.push(i),n<=60?(l++,t.push(new Uint8Array([n-1<<2]))):n<=256?(l+=2,t.push(new Uint8Array([240,n-1&255]))):n<=65536?(l+=3,t.push(new Uint8Array([244,n-1&255,n-1>>8&255]))):n<=16777216?(l+=4,t.push(new Uint8Array([248,n-1&255,n-1>>8&255,n-1>>16&255]))):n<=4294967296&&(l+=5,t.push(new Uint8Array([252,n-1&255,n-1>>8&255,n-1>>16&255,n-1>>>24&255]))),t.push(e.slice(r,r+n)),l+=n,a[0]=0,a[1]=255&l,a[2]=l>>8&255,a[3]=l>>16&255,r+=n}return Do(t)}function $o(e,t){var r=new Uint8Array(32),n=Po(r),a=12,i=0;switch(r[0]=5,e.t){case"n":r[1]=2,function(e,t,r){var n=Math.floor(0==r?0:Math.LOG10E*Math.log(Math.abs(r)))+6176-20,a=r/Math.pow(10,n-6176);e[t+15]|=n>>7,e[t+14]|=(127&n)<<1;for(var i=0;a>=1;++i,a/=256)e[t+i]=255&a;e[t+15]|=r>=0?0:128}(r,a,e.v),i|=1,a+=16;break;case"b":r[1]=6,n.setFloat64(a,e.v?1:0,!0),i|=2,a+=8;break;case"s":if(-1==t.indexOf(e.v))throw new Error("Value ".concat(e.v," missing from SST!"));r[1]=3,n.setUint32(a,t.indexOf(e.v),!0),i|=8,a+=4;break;default:throw"unsupported cell type "+e.t}return n.setUint32(8,i,!0),r.slice(0,a)}function Go(e,t){var r=new Uint8Array(32),n=Po(r),a=12,i=0;switch(r[0]=3,e.t){case"n":r[2]=2,n.setFloat64(a,e.v,!0),i|=32,a+=8;break;case"b":r[2]=6,n.setFloat64(a,e.v?1:0,!0),i|=32,a+=8;break;case"s":if(-1==t.indexOf(e.v))throw new Error("Value ".concat(e.v," missing from SST!"));r[2]=3,n.setUint32(a,t.indexOf(e.v),!0),i|=16,a+=4;break;default:throw"unsupported cell type "+e.t}return n.setUint32(4,i,!0),r.slice(0,a)}function Ko(e){return Lo(jo(e)[1][0].data)}function Yo(e,t,r){var n,a,i,l;if(!(null==(n=e[6])?void 0:n[0])||!(null==(a=e[7])?void 0:a[0]))throw"Mutation only works on post-BNC storages!";if((null==(l=null==(i=e[8])?void 0:i[0])?void 0:l.data)&&Fo(e[8][0].data)>0||!1)throw"Math only works with normal offsets";for(var o=0,s=Po(e[7][0].data),c=0,u=[],f=Po(e[4][0].data),h=0,d=[],p=0;p<t.length;++p)if(null!=t[p]){var m,g;switch(s.setUint16(2*p,c,!0),f.setUint16(2*p,h,!0),typeof t[p]){case"string":m=$o({t:"s",v:t[p]},r),g=Go({t:"s",v:t[p]},r);break;case"number":m=$o({t:"n",v:t[p]},r),g=Go({t:"n",v:t[p]},r);break;case"boolean":m=$o({t:"b",v:t[p]},r),g=Go({t:"b",v:t[p]},r);break;default:throw new Error("Unsupported value "+t[p])}u.push(m),c+=m.length,d.push(g),h+=g.length,++o}else s.setUint16(2*p,65535,!0),f.setUint16(2*p,65535);for(e[2][0].data=Mo(o);p<e[7][0].data.length/2;++p)s.setUint16(2*p,65535,!0),f.setUint16(2*p,65535,!0);return e[6][0].data=Do(u),e[3][0].data=Do(d),o}function Xo(e){return function(t){for(var r=0;r!=e.length;++r){var n=e[r];void 0===t[n[0]]&&(t[n[0]]=n[1]),"n"===n[2]&&(t[n[0]]=Number(t[n[0]]))}}}function Qo(e){Xo([["cellDates",!1],["bookSST",!1],["bookType","xlsx"],["compression",!1],["WTF",!1]])(e)}function qo(e,t){return"ods"==t.bookType?Ro(e,t):"numbers"==t.bookType?function(e,t){if(!t||!t.numbers)throw new Error("Must pass a `numbers` option -- check the README");var r=e.Sheets[e.SheetNames[0]];e.SheetNames.length>1&&console.error("The Numbers writer currently writes only the first table");var n=cn(r["!ref"]);n.s.r=n.s.c=0;var a=!1;n.e.c>9&&(a=!0,n.e.c=9),n.e.r>49&&(a=!0,n.e.r=49),a&&console.error("The Numbers writer is currently limited to ".concat(un(n)));var i=os(r,{range:n,header:1}),l=["~Sh33tJ5~"];i.forEach((function(e){return e.forEach((function(e){"string"==typeof e&&l.push(e)}))}));var o={},s=[],c=Je.read(t.numbers,{type:"base64"});c.FileIndex.map((function(e,t){return[e,c.FullPaths[t]]})).forEach((function(e){var t=e[0],r=e[1];2==t.type&&t.name.match(/\.iwa/)&&Bo(Wo(t.content)).forEach((function(e){s.push(e.id),o[e.id]={deps:[],location:r,type:Fo(e.messages[0].meta[1][0].data)}}))})),s.sort((function(e,t){return e-t}));var u=s.filter((function(e){return e>1})).map((function(e){return[e,Mo(e)]}));c.FileIndex.map((function(e,t){return[e,c.FullPaths[t]]})).forEach((function(e){var t=e[0];e[1],t.name.match(/\.iwa/)&&Bo(Wo(t.content)).forEach((function(e){e.messages.forEach((function(t){u.forEach((function(t){e.messages.some((function(e){return 11006!=Fo(e.meta[1][0].data)&&function(e,t){e:for(var r=0;r<=e.length-t.length;++r){for(var n=0;n<t.length;++n)if(e[r+n]!=t[n])continue e;return!0}return!1}(e.data,t[1])}))&&o[t[0]].deps.push(e.id)}))}))}))}));for(var f,h=Je.find(c,o[1].location),d=Bo(Wo(h.content)),p=0;p<d.length;++p){var m=d[p];1==m.id&&(f=m)}var g=Ko(jo(f.messages[0].data)[1][0].data);for(d=Bo(Wo((h=Je.find(c,o[g].location)).content)),p=0;p<d.length;++p)(m=d[p]).id==g&&(f=m);for(g=Ko(jo(f.messages[0].data)[2][0].data),d=Bo(Wo((h=Je.find(c,o[g].location)).content)),p=0;p<d.length;++p)(m=d[p]).id==g&&(f=m);for(g=Ko(jo(f.messages[0].data)[2][0].data),d=Bo(Wo((h=Je.find(c,o[g].location)).content)),p=0;p<d.length;++p)(m=d[p]).id==g&&(f=m);var v=jo(f.messages[0].data);v[6][0].data=Mo(n.e.r+1),v[7][0].data=Mo(n.e.c+1);for(var b=Ko(v[46][0].data),y=Je.find(c,o[b].location),w=Bo(Wo(y.content)),T=0;T<w.length&&w[T].id!=b;++T);if(w[T].id!=b)throw"Bad ColumnRowUIDMapArchive";var E=jo(w[T].messages[0].data);E[1]=[],E[2]=[],E[3]=[];for(var S=0;S<=n.e.c;++S){var x=[];x[1]=x[2]=[{type:0,data:Mo(S+420690)}],E[1].push({type:2,data:Uo(x)}),E[2].push({type:0,data:Mo(S)}),E[3].push({type:0,data:Mo(S)})}E[4]=[],E[5]=[],E[6]=[];for(var k=0;k<=n.e.r;++k)(x=[])[1]=x[2]=[{type:0,data:Mo(k+726270)}],E[4].push({type:2,data:Uo(x)}),E[5].push({type:0,data:Mo(k)}),E[6].push({type:0,data:Mo(k)});w[T].messages[0].data=Uo(E),y.content=Vo(zo(w)),y.size=y.content.length,delete v[46];var _=jo(v[4][0].data);_[7][0].data=Mo(n.e.r+1);var C=Ko(jo(_[1][0].data)[2][0].data);if((w=Bo(Wo((y=Je.find(c,o[C].location)).content)))[0].id!=C)throw"Bad HeaderStorageBucket";var A=jo(w[0].messages[0].data);for(k=0;k<i.length;++k){var N=jo(A[2][0].data);N[1][0].data=Mo(k),N[4][0].data=Mo(i[k].length),A[2][k]={type:A[2][0].type,data:Uo(N)}}w[0].messages[0].data=Uo(A),y.content=Vo(zo(w)),y.size=y.content.length;var O=Ko(_[2][0].data);if((w=Bo(Wo((y=Je.find(c,o[O].location)).content)))[0].id!=O)throw"Bad HeaderStorageBucket";for(A=jo(w[0].messages[0].data),S=0;S<=n.e.c;++S)(N=jo(A[2][0].data))[1][0].data=Mo(S),N[4][0].data=Mo(n.e.r+1),A[2][S]={type:A[2][0].type,data:Uo(N)};w[0].messages[0].data=Uo(A),y.content=Vo(zo(w)),y.size=y.content.length;var R=Ko(_[4][0].data);!function(){for(var e,t=Je.find(c,o[R].location),r=Bo(Wo(t.content)),n=0;n<r.length;++n){var a=r[n];a.id==R&&(e=a)}var i=jo(e.messages[0].data);i[3]=[];var s=[];l.forEach((function(e,t){s[1]=[{type:0,data:Mo(t)}],s[2]=[{type:0,data:Mo(1)}],s[3]=[{type:2,data:Io(e)}],i[3].push({type:2,data:Uo(s)})})),e.messages[0].data=Uo(i);var u=Vo(zo(r));t.content=u,t.size=t.content.length}();var P=jo(_[3][0].data),I=P[1][0];delete P[2];var D=jo(I.data),L=Ko(D[2][0].data);!function(){for(var e,t=Je.find(c,o[L].location),r=Bo(Wo(t.content)),a=0;a<r.length;++a){var s=r[a];s.id==L&&(e=s)}var u=jo(e.messages[0].data);delete u[6],delete P[7];var f=new Uint8Array(u[5][0].data);u[5]=[];for(var h=0,d=0;d<=n.e.r;++d){var p=jo(f);h+=Yo(p,i[d],l),p[1][0].data=Mo(d),u[5].push({data:Uo(p),type:2})}u[1]=[{type:0,data:Mo(n.e.c+1)}],u[2]=[{type:0,data:Mo(n.e.r+1)}],u[3]=[{type:0,data:Mo(h)}],u[4]=[{type:0,data:Mo(n.e.r+1)}],e.messages[0].data=Uo(u);var m=Vo(zo(r));t.content=m,t.size=t.content.length}(),I.data=Uo(D),_[3][0].data=Uo(P),v[4][0].data=Uo(_),f.messages[0].data=Uo(v);var M=Vo(zo(d));return h.content=M,h.size=h.content.length,c}(e,t):"xlsb"==t.bookType?function(e,t){Ci=1024,e&&!e.SSF&&(e.SSF=vt(pe));e&&e.SSF&&(Xe(),Ye(e.SSF),t.revssf=it(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF);t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,yl?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r="xlsb"==t.bookType?"bin":"xml",n=Ii.indexOf(t.bookType)>-1,a={workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""};Qo(t=t||{});var i=St(),l="",o=0;t.cellXfs=[],Sl(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={});if(Et(i,l="docProps/core.xml",la(e.Props,t)),a.coreprops.push(l),ta(t.rels,2,l,Jn.CORE_PROPS),l="docProps/app.xml",e.Props&&e.Props.SheetNames);else if(e.Workbook&&e.Workbook.Sheets){for(var s=[],c=0;c<e.SheetNames.length;++c)2!=(e.Workbook.Sheets[c]||{}).Hidden&&s.push(e.SheetNames[c]);e.Props.SheetNames=s}else e.Props.SheetNames=e.SheetNames;e.Props.Worksheets=e.Props.SheetNames.length,Et(i,l,ca(e.Props)),a.extprops.push(l),ta(t.rels,3,l,Jn.EXT_PROPS),e.Custprops!==e.Props&&rt(e.Custprops||{}).length>0&&(Et(i,l="docProps/custom.xml",ua(e.Custprops)),a.custprops.push(l),ta(t.rels,4,l,Jn.CUST_PROPS));for(o=1;o<=e.SheetNames.length;++o){var u={"!id":{}},f=e.Sheets[e.SheetNames[o-1]];(f||{})["!type"];if(Et(i,l="xl/worksheets/sheet"+o+"."+r,ql(o-1,l,t,e,u)),a.sheets.push(l),ta(t.wbrels,-1,"worksheets/sheet"+o+"."+r,Jn.WS[0]),f){var h=f["!comments"],d=!1,p="";h&&h.length>0&&(Et(i,p="xl/comments"+o+"."+r,Jl(h,p,t)),a.comments.push(p),ta(u,-1,"../comments"+o+"."+r,Jn.CMNT),d=!0),f["!legacy"]&&d&&Et(i,"xl/drawings/vmlDrawing"+o+".vml",Ai(o,f["!comments"])),delete f["!comments"],delete f["!legacy"]}u["!id"].rId1&&Et(i,Zn(l),ea(u))}null!=t.Strings&&t.Strings.length>0&&(Et(i,l="xl/sharedStrings."+r,function(e,t,r){return(".bin"===t.slice(-4)?qa:Xa)(e,r)}(t.Strings,l,t)),a.strs.push(l),ta(t.wbrels,-1,"sharedStrings."+r,Jn.SST));Et(i,l="xl/workbook."+r,function(e,t,r){return(".bin"===t.slice(-4)?Ql:Yl)(e,r)}(e,l,t)),a.workbooks.push(l),ta(t.rels,1,l,Jn.WB),Et(i,l="xl/theme/theme1.xml",xi(e.Themes,t)),a.themes.push(l),ta(t.wbrels,-1,"theme/theme1.xml",Jn.THEME),Et(i,l="xl/styles."+r,function(e,t,r){return(".bin"===t.slice(-4)?Si:ui)(e,r)}(e,l,t)),a.styles.push(l),ta(t.wbrels,-1,"styles."+r,Jn.STY),e.vbaraw&&n&&(Et(i,l="xl/vbaProject.bin",e.vbaraw),a.vba.push(l),ta(t.wbrels,-1,"vbaProject.bin",Jn.VBA));return Et(i,l="xl/metadata."+r,function(e){return(".bin"===e.slice(-4)?ki:_i)()}(l)),a.metadata.push(l),ta(t.wbrels,-1,"metadata."+r,Jn.XLMETA),Et(i,"[Content_Types].xml",qn(a,t)),Et(i,"_rels/.rels",ea(t.rels)),Et(i,"xl/_rels/workbook."+r+".rels",ea(t.wbrels)),delete t.revssf,delete t.ssf,i}(e,t):Jo(e,t)}function Jo(e,t){Ci=1024,e&&!e.SSF&&(e.SSF=vt(pe)),e&&e.SSF&&(Xe(),Ye(e.SSF),t.revssf=it(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,yl?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r="xml",n=Ii.indexOf(t.bookType)>-1,a={workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""};Qo(t=t||{});var i=St(),l="",o=0;if(t.cellXfs=[],Sl(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),Et(i,l="docProps/core.xml",la(e.Props,t)),a.coreprops.push(l),ta(t.rels,2,l,Jn.CORE_PROPS),l="docProps/app.xml",e.Props&&e.Props.SheetNames);else if(e.Workbook&&e.Workbook.Sheets){for(var s=[],c=0;c<e.SheetNames.length;++c)2!=(e.Workbook.Sheets[c]||{}).Hidden&&s.push(e.SheetNames[c]);e.Props.SheetNames=s}else e.Props.SheetNames=e.SheetNames;e.Props.Worksheets=e.Props.SheetNames.length,Et(i,l,ca(e.Props)),a.extprops.push(l),ta(t.rels,3,l,Jn.EXT_PROPS),e.Custprops!==e.Props&&rt(e.Custprops||{}).length>0&&(Et(i,l="docProps/custom.xml",ua(e.Custprops)),a.custprops.push(l),ta(t.rels,4,l,Jn.CUST_PROPS));var u=["SheetJ5"];for(t.tcid=0,o=1;o<=e.SheetNames.length;++o){var f={"!id":{}},h=e.Sheets[e.SheetNames[o-1]];(h||{})["!type"];if(Et(i,l="xl/worksheets/sheet"+o+"."+r,Al(o-1,t,e,f)),a.sheets.push(l),ta(t.wbrels,-1,"worksheets/sheet"+o+"."+r,Jn.WS[0]),h){var d=h["!comments"],p=!1,m="";if(d&&d.length>0){var g=!1;d.forEach((function(e){e[1].forEach((function(e){1==e.T&&(g=!0)}))})),g&&(Et(i,m="xl/threadedComments/threadedComment"+o+"."+r,Oi(d,u,t)),a.threadedcomments.push(m),ta(f,-1,"../threadedComments/threadedComment"+o+"."+r,Jn.TCMNT)),Et(i,m="xl/comments"+o+"."+r,Ni(d)),a.comments.push(m),ta(f,-1,"../comments"+o+"."+r,Jn.CMNT),p=!0}h["!legacy"]&&p&&Et(i,"xl/drawings/vmlDrawing"+o+".vml",Ai(o,h["!comments"])),delete h["!comments"],delete h["!legacy"]}f["!id"].rId1&&Et(i,Zn(l),ea(f))}return null!=t.Strings&&t.Strings.length>0&&(Et(i,l="xl/sharedStrings."+r,Xa(t.Strings,t)),a.strs.push(l),ta(t.wbrels,-1,"sharedStrings."+r,Jn.SST)),Et(i,l="xl/workbook."+r,Yl(e)),a.workbooks.push(l),ta(t.rels,1,l,Jn.WB),Et(i,l="xl/theme/theme1.xml",xi(e.Themes,t)),a.themes.push(l),ta(t.wbrels,-1,"theme/theme1.xml",Jn.THEME),Et(i,l="xl/styles."+r,ui(e,t)),a.styles.push(l),ta(t.wbrels,-1,"styles."+r,Jn.STY),e.vbaraw&&n&&(Et(i,l="xl/vbaProject.bin",e.vbaraw),a.vba.push(l),ta(t.wbrels,-1,"vbaProject.bin",Jn.VBA)),Et(i,l="xl/metadata."+r,_i()),a.metadata.push(l),ta(t.wbrels,-1,"metadata."+r,Jn.XLMETA),u.length>1&&(Et(i,l="xl/persons/person.xml",function(e){var t=[xt,Vt("personList",null,{xmlns:qt,"xmlns:x":ir[0]}).replace(/[\/]>/,">")];return e.forEach((function(e,r){t.push(Vt("person",null,{displayName:e,id:"{54EE7950-7262-4200-6969-"+("000000000000"+r).slice(-12)+"}",userId:e,providerId:"None"}))})),t.push("</personList>"),t.join("")}(u)),a.people.push(l),ta(t.wbrels,-1,"persons/person.xml",Jn.PEOPLE)),Et(i,"[Content_Types].xml",qn(a,t)),Et(i,"_rels/.rels",ea(t.rels)),Et(i,"xl/_rels/workbook.xml.rels",ea(t.wbrels)),delete t.revssf,delete t.ssf,i}function Zo(e,t){var r="";switch((t||{}).type||"base64"){case"buffer":case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":r=Y(e.slice(0,12));break;case"binary":r=e;break;default:throw new Error("Unrecognized type "+(t&&t.type||"undefined"))}return[r.charCodeAt(0),r.charCodeAt(1),r.charCodeAt(2),r.charCodeAt(3),r.charCodeAt(4),r.charCodeAt(5),r.charCodeAt(6),r.charCodeAt(7)]}function es(e,t){switch(t.type){case"base64":case"binary":break;case"buffer":case"array":t.type="";break;case"file":return tt(t.file,Je.write(e,{type:X?"buffer":""}));case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");default:throw new Error("Unrecognized type "+t.type)}return Je.write(e,t)}function ts(e,t){var r={},n=X?"nodebuffer":"undefined"!==typeof Uint8Array?"array":"string";if(t.compression&&(r.compression="DEFLATE"),t.password)r.type=n;else switch(t.type){case"base64":r.type="base64";break;case"binary":r.type="string";break;case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");case"buffer":case"file":r.type=n;break;default:throw new Error("Unrecognized type "+t.type)}var a=e.FullPaths?Je.write(e,{fileType:"zip",type:{nodebuffer:"buffer",string:"binary"}[r.type]||r.type,compression:!!t.compression}):e.generate(r);if("undefined"!==typeof Deno&&"string"==typeof a){if("binary"==t.type||"base64"==t.type)return a;a=new Uint8Array(ee(a))}return t.password&&"undefined"!==typeof encrypt_agile?es(encrypt_agile(a,t.password),t):"file"===t.type?tt(t.file,a):"string"==t.type?jt(a):a}function rs(e,t,r){r||(r="");var n=r+e;switch(t.type){case"base64":return K(Ut(n));case"binary":return Ut(n);case"string":return e;case"file":return tt(t.file,n,"utf8");case"buffer":return X?Q(n,"utf8"):"undefined"!==typeof TextEncoder?(new TextEncoder).encode(n):rs(n,{type:"binary"}).split("").map((function(e){return e.charCodeAt(0)}))}throw new Error("Unrecognized type "+t.type)}function ns(e,t){switch(t.type){case"string":case"base64":case"binary":for(var r="",n=0;n<e.length;++n)r+=String.fromCharCode(e[n]);return"base64"==t.type?K(r):"string"==t.type?jt(r):r;case"file":return tt(t.file,e);case"buffer":return e;default:throw new Error("Unrecognized type "+t.type)}}function as(e,t){B(),Kl(e);var r=vt(t||{});if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),"array"==r.type){r.type="binary";var n=as(e,r);return r.type="array",ee(n)}var a=0;if(r.sheet&&(a="number"==typeof r.sheet?r.sheet:e.SheetNames.indexOf(r.sheet),!e.SheetNames[a]))throw new Error("Sheet not found: "+r.sheet+" : "+typeof r.sheet);switch(r.bookType||"xlsb"){case"xml":case"xlml":return rs(ao(e,r),r);case"slk":case"sylk":return rs(Wa.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"htm":case"html":return rs(ko(e.Sheets[e.SheetNames[a]],r),r);case"txt":return function(e,t){switch(t.type){case"base64":return K(e);case"binary":case"string":return e;case"file":return tt(t.file,e,"binary");case"buffer":return X?Q(e,"binary"):e.split("").map((function(e){return e.charCodeAt(0)}))}throw new Error("Unrecognized type "+t.type)}(fs(e.Sheets[e.SheetNames[a]],r),r);case"csv":return rs(us(e.Sheets[e.SheetNames[a]],r),r,"\ufeff");case"dif":return rs(Va.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"dbf":return ns(Ha.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"prn":return rs(Ga.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"rtf":return rs(ei.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"eth":return rs($a.from_sheet(e.Sheets[e.SheetNames[a]],r),r);case"fods":return rs(Ro(e,r),r);case"wk1":return ns(Ka.sheet_to_wk1(e.Sheets[e.SheetNames[a]],r),r);case"wk3":return ns(Ka.book_to_wk3(e,r),r);case"biff2":r.biff||(r.biff=2);case"biff3":r.biff||(r.biff=3);case"biff4":return r.biff||(r.biff=4),ns(wo(e,r),r);case"biff5":r.biff||(r.biff=5);case"biff8":case"xla":case"xls":return r.biff||(r.biff=8),function(e,t){var r=t||{};return es(so(e,r),r)}(e,r);case"xlsx":case"xlsm":case"xlam":case"xlsb":case"numbers":case"ods":return function(e,t){var r=vt(t||{});return ts(qo(e,r),r)}(e,r);default:throw new Error("Unrecognized bookType |"+r.bookType+"|")}}function is(e){if(!e.bookType){var t=e.file.slice(e.file.lastIndexOf(".")).toLowerCase();t.match(/^\.[a-z]+$/)&&(e.bookType=t.slice(1)),e.bookType={xls:"biff8",htm:"html",slk:"sylk",socialcalc:"eth",Sh33tJS:"WTF"}[e.bookType]||e.bookType}}function ls(e,t,r,n,a,i,l,o){var s=nn(r),c=o.defval,u=o.raw||!Object.prototype.hasOwnProperty.call(o,"raw"),f=!0,h=1===a?[]:{};if(1!==a)if(Object.defineProperty)try{Object.defineProperty(h,"__rowNum__",{value:r,enumerable:!1})}catch(g){h.__rowNum__=r}else h.__rowNum__=r;if(!l||e[r])for(var d=t.s.c;d<=t.e.c;++d){var p=l?e[r][d]:e[n[d]+s];if(void 0!==p&&void 0!==p.t){var m=p.v;switch(p.t){case"z":if(null==m)break;continue;case"e":m=0==m?null:void 0;break;case"s":case"d":case"b":case"n":break;default:throw new Error("unrecognized type "+p.t)}if(null!=i[d]){if(null==m)if("e"==p.t&&null===m)h[i[d]]=null;else if(void 0!==c)h[i[d]]=c;else{if(!u||null!==m)continue;h[i[d]]=null}else h[i[d]]=u&&("n"!==p.t||"n"===p.t&&!1!==o.rawNumbers)?m:dn(p,m,o);null!=m&&(f=!1)}}else{if(void 0===c)continue;null!=i[d]&&(h[i[d]]=c)}}return{row:h,isempty:f}}function os(e,t){if(null==e||null==e["!ref"])return[];var r={t:"n",v:0},n=0,a=1,i=[],l=0,o="",s={s:{r:0,c:0},e:{r:0,c:0}},c=t||{},u=null!=c.range?c.range:e["!ref"];switch(1===c.header?n=1:"A"===c.header?n=2:Array.isArray(c.header)?n=3:null==c.header&&(n=0),typeof u){case"string":s=fn(u);break;case"number":(s=fn(e["!ref"])).s.r=u;break;default:s=u}n>0&&(a=0);var f=nn(s.s.r),h=[],d=[],p=0,m=0,g=Array.isArray(e),v=s.s.r,b=0,y={};g&&!e[v]&&(e[v]=[]);var w=c.skipHidden&&e["!cols"]||[],T=c.skipHidden&&e["!rows"]||[];for(b=s.s.c;b<=s.e.c;++b)if(!(w[b]||{}).hidden)switch(h[b]=ln(b),r=g?e[v][b]:e[h[b]+f],n){case 1:i[b]=b-s.s.c;break;case 2:i[b]=h[b];break;case 3:i[b]=c.header[b-s.s.c];break;default:if(null==r&&(r={w:"__EMPTY",t:"s"}),o=l=dn(r,null,c),m=y[l]||0){do{o=l+"_"+m++}while(y[o]);y[l]=m,y[o]=1}else y[l]=1;i[b]=o}for(v=s.s.r+a;v<=s.e.r;++v)if(!(T[v]||{}).hidden){var E=ls(e,s,v,h,n,i,g,c);(!1===E.isempty||(1===n?!1!==c.blankrows:c.blankrows))&&(d[p++]=E.row)}return d.length=p,d}var ss=/"/g;function cs(e,t,r,n,a,i,l,o){for(var s=!0,c=[],u="",f=nn(r),h=t.s.c;h<=t.e.c;++h)if(n[h]){var d=o.dense?(e[r]||[])[h]:e[n[h]+f];if(null==d)u="";else if(null!=d.v){s=!1,u=""+(o.rawNumbers&&"n"==d.t?d.v:dn(d,null,o));for(var p=0,m=0;p!==u.length;++p)if((m=u.charCodeAt(p))===a||m===i||34===m||o.forceQuotes){u='"'+u.replace(ss,'""')+'"';break}"ID"==u&&(u='"ID"')}else null==d.f||d.F?u="":(s=!1,(u="="+d.f).indexOf(",")>=0&&(u='"'+u.replace(ss,'""')+'"'));c.push(u)}return!1===o.blankrows&&s?null:c.join(l)}function us(e,t){var r=[],n=null==t?{}:t;if(null==e||null==e["!ref"])return"";var a=fn(e["!ref"]),i=void 0!==n.FS?n.FS:",",l=i.charCodeAt(0),o=void 0!==n.RS?n.RS:"\n",s=o.charCodeAt(0),c=new RegExp(("|"==i?"\\|":i)+"+$"),u="",f=[];n.dense=Array.isArray(e);for(var h=n.skipHidden&&e["!cols"]||[],d=n.skipHidden&&e["!rows"]||[],p=a.s.c;p<=a.e.c;++p)(h[p]||{}).hidden||(f[p]=ln(p));for(var m=0,g=a.s.r;g<=a.e.r;++g)(d[g]||{}).hidden||null!=(u=cs(e,a,g,f,l,s,i,n))&&(n.strip&&(u=u.replace(c,"")),(u||!1!==n.blankrows)&&r.push((m++?o:"")+u));return delete n.dense,r.join("")}function fs(e,t){t||(t={}),t.FS="\t",t.RS="\n";var r=us(e,t);if("undefined"==typeof H||"string"==t.type)return r;var n=H.utils.encode(1200,r,"str");return String.fromCharCode(255)+String.fromCharCode(254)+n}function hs(e,t,r){var n,a=r||{},i=+!a.skipHeader,l=e||{},o=0,s=0;if(l&&null!=a.origin)if("number"==typeof a.origin)o=a.origin;else{var c="string"==typeof a.origin?on(a.origin):a.origin;o=c.r,s=c.c}var u={s:{c:0,r:0},e:{c:s,r:o+t.length-1+i}};if(l["!ref"]){var f=fn(l["!ref"]);u.e.c=Math.max(u.e.c,f.e.c),u.e.r=Math.max(u.e.r,f.e.r),-1==o&&(o=f.e.r+1,u.e.r=o+t.length-1+i)}else-1==o&&(o=0,u.e.r=t.length-1+i);var h=a.header||[],d=0;t.forEach((function(e,t){rt(e).forEach((function(r){-1==(d=h.indexOf(r))&&(h[d=h.length]=r);var c=e[r],u="z",f="",p=sn({c:s+d,r:o+t+i});n=ds(l,p),!c||"object"!==typeof c||c instanceof Date?("number"==typeof c?u="n":"boolean"==typeof c?u="b":"string"==typeof c?u="s":c instanceof Date?(u="d",a.cellDates||(u="n",c=ot(c)),f=a.dateNF||pe[14]):null===c&&a.nullError&&(u="e",c=0),n?(n.t=u,n.v=c,delete n.w,delete n.R,f&&(n.z=f)):l[p]=n={t:u,v:c},f&&(n.z=f)):l[p]=c}))})),u.e.c=Math.max(u.e.c,s+h.length-1);var p=nn(o);if(i)for(d=0;d<h.length;++d)l[ln(d+s)+p]={t:"s",v:h[d]};return l["!ref"]=un(u),l}function ds(e,t,r){if("string"==typeof t){if(Array.isArray(e)){var n=on(t);return e[n.r]||(e[n.r]=[]),e[n.r][n.c]||(e[n.r][n.c]={t:"z"})}return e[t]||(e[t]={t:"z"})}return ds(e,sn("number"!=typeof t?t:{r:t,c:r||0}))}function ps(){return{SheetNames:[],Sheets:{}}}function ms(e,t,r,n){var a=1;if(!r)for(;a<=65535&&-1!=e.SheetNames.indexOf(r="Sheet"+a);++a,r=void 0);if(!r||e.SheetNames.length>=65535)throw new Error("Too many worksheets");if(n&&e.SheetNames.indexOf(r)>=0){var i=r.match(/(^.*?)(\d+)$/);a=i&&+i[2]||0;var l=i&&i[1]||r;for(++a;a<=65535&&-1!=e.SheetNames.indexOf(r=l+a);++a);}if(Gl(r),e.SheetNames.indexOf(r)>=0)throw new Error("Worksheet with name |"+r+"| already exists!");return e.SheetNames.push(r),e.Sheets[r]=t,r}function gs(e,t,r){return t?(e.l={Target:t},r&&(e.l.Tooltip=r)):delete e.l,e}var vs={encode_col:ln,encode_row:nn,encode_cell:sn,encode_range:un,decode_col:an,decode_row:rn,split_cell:function(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")},decode_cell:on,decode_range:cn,format_cell:dn,sheet_add_aoa:mn,sheet_add_json:hs,sheet_add_dom:_o,aoa_to_sheet:gn,json_to_sheet:function(e,t){return hs(null,e,t)},table_to_sheet:Co,table_to_book:function(e,t){return pn(Co(e,t),t)},sheet_to_csv:us,sheet_to_txt:fs,sheet_to_json:os,sheet_to_html:ko,sheet_to_formulae:function(e){var t,r="",n="";if(null==e||null==e["!ref"])return[];var a,i=fn(e["!ref"]),l="",o=[],s=[],c=Array.isArray(e);for(a=i.s.c;a<=i.e.c;++a)o[a]=ln(a);for(var u=i.s.r;u<=i.e.r;++u)for(l=nn(u),a=i.s.c;a<=i.e.c;++a)if(r=o[a]+l,n="",void 0!==(t=c?(e[u]||[])[a]:e[r])){if(null!=t.F){if(r=t.F,!t.f)continue;n=t.f,-1==r.indexOf(":")&&(r=r+":"+r)}if(null!=t.f)n=t.f;else{if("z"==t.t)continue;if("n"==t.t&&null!=t.v)n=""+t.v;else if("b"==t.t)n=t.v?"TRUE":"FALSE";else if(void 0!==t.w)n="'"+t.w;else{if(void 0===t.v)continue;n="s"==t.t?"'"+t.v:""+t.v}}s[s.length]=r+"="+n}return s},sheet_to_row_object_array:os,sheet_get_cell:ds,book_new:ps,book_append_sheet:ms,book_set_sheet_visibility:function(e,t,r){e.Workbook||(e.Workbook={}),e.Workbook.Sheets||(e.Workbook.Sheets=[]);var n=function(e,t){if("number"==typeof t){if(t>=0&&e.SheetNames.length>t)return t;throw new Error("Cannot find sheet # "+t)}if("string"==typeof t){var r=e.SheetNames.indexOf(t);if(r>-1)return r;throw new Error("Cannot find sheet name |"+t+"|")}throw new Error("Cannot find sheet |"+t+"|")}(e,t);switch(e.Workbook.Sheets[n]||(e.Workbook.Sheets[n]={}),r){case 0:case 1:case 2:break;default:throw new Error("Bad sheet visibility setting "+r)}e.Workbook.Sheets[n].Hidden=r},cell_set_number_format:function(e,t){return e.z=t,e},cell_set_hyperlink:gs,cell_set_internal_link:function(e,t,r){return gs(e,"#"+t,r)},cell_add_comment:function(e,t,r){e.c||(e.c=[]),e.c.push({t:t,a:r||"SheetJS"})},sheet_set_array_formula:function(e,t,r,n){for(var a="string"!=typeof t?t:fn(t),i="string"==typeof t?t:un(t),l=a.s.r;l<=a.e.r;++l)for(var o=a.s.c;o<=a.e.c;++o){var s=ds(e,l,o);s.t="n",s.F=i,delete s.v,l==a.s.r&&o==a.s.c&&(s.f=r,n&&(s.D=!0))}return e},consts:{SHEET_VISIBLE:0,SHEET_HIDDEN:1,SHEET_VERY_HIDDEN:2}};I.version;const bs=e=>{let{activeProvider:t,onProviderChange:r}=e;const n=[{id:"gcp",name:"GCP",color:"from-blue-500 to-blue-600",icon:O,iconColor:"text-blue-500",enabled:!0},{id:"aws",name:"AWS",color:"from-orange-500 to-orange-600",icon:R,iconColor:"text-orange-500",enabled:!0},{id:"azure",name:"Azure",color:"from-blue-600 to-blue-700",icon:P,iconColor:"text-blue-600",enabled:!0}];return(0,_.jsx)("div",{className:"fixed left-0 top-1/4 flex flex-col items-start p-4 space-y-2",children:(0,_.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-2 w-16",children:n.map((e=>{const n=e.icon,a=!e.enabled;return(0,_.jsxs)("div",{className:"relative group",children:[(0,_.jsxs)("button",{onClick:()=>e.enabled&&r(e.id),disabled:a,className:"w-full p-2 rounded-lg flex flex-col items-center justify-center transition-all duration-200 "+(t===e.id?`bg-gradient-to-r ${e.color} text-white shadow-md`:`text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 ${e.iconColor} ${a?"opacity-50 cursor-not-allowed":""}`),children:[(0,_.jsx)(n,{className:"w-6 h-6"}),(0,_.jsx)("span",{className:"text-xs font-medium mt-1",children:e.name})]}),a&&(0,_.jsx)("div",{className:"absolute left-full ml-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap",children:"Coming soon"})]},e.id)}))})})},ys=e=>{if(!e)return!1;return[/API has not been used in project .* before or it is disabled/,/Error 403: .* API has not been enabled/,/API .* is not enabled/].some((t=>t.test(e)))},ws=()=>{const[e,t]=(0,n.useState)("gcp"),[r,a]=(0,n.useState)((()=>{if("undefined"!==typeof window){const e=localStorage.getItem("darkMode");return e?JSON.parse(e):window.matchMedia("(prefers-color-scheme: dark)").matches}return!1})),[i,l]=(0,n.useState)(null),[k,C]=(0,n.useState)([]),[A,O]=(0,n.useState)(null),[R,P]=(0,n.useState)(!1),[I,D]=(0,n.useState)(null),[L,M]=(0,n.useState)(null),[F,j]=(0,n.useState)({gcp:{org:!1,projects:!1},aws:{org:!1,projects:!1},azure:{org:!1,projects:!1}}),[U,B]=(0,n.useState)({gcp:!1,aws:!1,azure:!1}),[z,H]=(0,n.useState)({gcp:!1,aws:!1,azure:!1}),W=U[e],V=z[e];(0,n.useEffect)((()=>{console.log(`Provider changed to: ${e}`),console.log("Loading states:",{orgLoading:U[e],projectsLoading:z[e]}),console.log("Active requests:",F)}),[e,U,z,F]);const[$,G]=(0,n.useState)(null),[K,Y]=(0,n.useState)({gcp:null,aws:null,azure:null}),[X,Q]=(0,n.useState)({gcp:[],aws:[],azure:[]}),[q,J]=(0,n.useState)([]),[Z,ee]=(0,n.useState)(""),[te,re]=(0,n.useState)(null),[ne,ae]=(0,n.useState)({}),[ie,le]=(0,n.useState)(0),[oe,se]=(0,n.useState)(null),[ce,ue]=(0,n.useState)(""),[fe,he]=(0,n.useState)(null),[de,pe]=(0,n.useState)(!1),[me,ge]=(0,n.useState)(null),[ve,be]=(0,n.useState)({}),[ye,we]=(0,n.useState)(!1),[Te,Ee]=(0,n.useState)({ok:!0,alarm:!0,error:!0,info:!0,skipped:!0}),[Se,xe]=(0,n.useState)("none"),[ke,_e]=(0,n.useState)(""),[Ce,Ae]=(0,n.useState)(!1),[Ne,Oe]=(0,n.useState)([]),[Re,Pe]=(0,n.useState)(!1),[Ie,De]=(0,n.useState)({current:0,total:0,inProgress:!1,currentProject:"",completedProjects:[]}),[Le,Me]=(0,n.useState)({title:"Benchmark Results",controls:[]}),Fe={ok:"Passed",alarm:"Failed",error:"Error",info:"Info",skipped:"Skipped"},[je,Ue]=(0,n.useState)({ok:0,alarm:0,error:0,info:0,skipped:0}),Be={gcp:[{id:"cft_scorecard_v1",name:"CFT Scorecard v1"},{id:"cis_v120",name:"CIS v1.2.0"},{id:"cis_v130",name:"CIS v1.3.0"},{id:"cis_v200",name:"CIS v2.0.0"},{id:"cis_v300",name:"CIS v3.0.0"},{id:"hipaa",name:"HIPAA"},{id:"nist_800_53_rev_5",name:"NIST 800-53 Rev 5"},{id:"nist_csf_v10",name:"NIST CSF v1.0"},{id:"pci_dss_v321",name:"PCI DSS v3.2.1"},{id:"soc_2_2017",name:"SOC 2 2017"},{id:"all_controls",name:"All Controls"},{id:"forseti_security_v226",name:"Forseti Security v2.26"}],aws:[{id:"acsc_essential_eight",name:"ACSC Essential Eight"},{id:"all_controls",name:"All Controls"},{id:"audit_manager_control_tower",name:"Audit Manager Control Tower"},{id:"cis_compute_service_v100",name:"CIS Compute Service v1.0.0"},{id:"cis_controls_v8_ig1",name:"CIS Controls v8 IG1"},{id:"cis_v120",name:"CIS v1.2.0"},{id:"cis_v130",name:"CIS v1.3.0"},{id:"cis_v200",name:"CIS v2.0.0"},{id:"cis_v140",name:"CIS v1.4.0"},{id:"cis_v150",name:"CIS v1.5.0"},{id:"cis_v300",name:"CIS v3.0.0"},{id:"cis_v400",name:"CIS v4.0.0"},{id:"cisa_cyber_essentials",name:"CISA Cyber Essentials"},{id:"conformance_pack",name:"Conformance Pack"},{id:"fedramp_low_rev_4",name:"FedRAMP Low Rev4"},{id:"fedramp_moderate_rev_4",name:"FedRAMP Moderate Rev4"},{id:"ffiec",name:"FFIEC"},{id:"foundational_security",name:"Foundational Security"},{id:"gdpr",name:"GDPR"},{id:"gxp_21_cfr_part_11",name:"GxP 21 CFR Part 11"},{id:"gxp_eu_annex_11",name:"GxP EU Annex 11"},{id:"hipaa_final_omnibus_security_rule_2013",name:"HIPAA Final Omnibus 2013"},{id:"hipaa_security_rule_2003",name:"HIPAA Security Rule 2003"},{id:"nist_800_53_rev_4",name:"NIST 800-53 Rev4"},{id:"nist_800_53_rev_5",name:"NIST 800-53 Rev5"},{id:"nist_800_171_rev_2",name:"NIST 800-171 Rev2"},{id:"nist_800_172",name:"NIST 800-172"},{id:"nist_csf",name:"NIST CSF"},{id:"pci_dss_v321",name:"PCI DSS v3.2.1"},{id:"rbi_cyber_security",name:"RBI Cyber Security"},{id:"rbi_itf_nbfc",name:"RBI ITF NBFC"},{id:"soc_2",name:"SOC 2"}],azure:[{id:"all_controls",name:"All Controls"},{id:"cis_v130",name:"CIS v1.3.0"},{id:"cis_v140",name:"CIS v1.4.0"},{id:"cis_v150",name:"CIS v1.5.0"},{id:"cis_v200",name:"CIS v2.0.0"},{id:"cis_v210",name:"CIS v2.1.0"},{id:"cis_v300",name:"CIS v3.0.0"},{id:"fedramp_high",name:"FedRAMP High"},{id:"hipaa_hitrust_v92",name:"HIPAA HITRUST v9.2"},{id:"nist_sp_800_53_rev_5",name:"NIST SP 800-53 Rev 5"},{id:"nist_sp_800_171_rev_2",name:"NIST SP 800-171 Rev 2"},{id:"pci_dss_v321",name:"PCI DSS v3.2.1"},{id:"rbi_itf_nbfc_v2017",name:"RBI ITF NBFC v2017"},{id:"regulatory_compliance",name:"Regulatory Compliance"}]};(0,n.useEffect)((()=>{const t=new AbortController,r=t.signal;return(async()=>{if(B((t=>({...t,[e]:!0}))),j((t=>({...t,[e]:{...t[e],org:!0}}))),K[e])return M(K[e]),G(null),B((t=>({...t,[e]:!1}))),void j((t=>({...t,[e]:{...t[e],org:!1}})));if(J([]),ee(""),"azure"!==e)try{const t="aws"===e?"/api/aws/get-aws-org-id":"/api/gcp/get-org-id",n=await fetch(t,{signal:r});if(r.aborted)return;if(!n.ok)throw new Error(`HTTP error! status: ${n.status}`);const a=await n.json();if(r.aborted)return;if("success"!==a.status)throw new Error(a.error||"Failed to fetch organization ID");{const t=a.organization_id||a.account_id;M(t),Y((r=>({...r,[e]:t}))),G(null)}}catch(t){if("AbortError"===t.name)return void console.log("Fetch aborted");G(t.message),M(null)}finally{r.aborted||(B((t=>({...t,[e]:!1}))),j((t=>({...t,[e]:{...t[e],org:!1}}))))}else try{const e=await fetch("/api/azure/get-tenant",{headers:{"Content-Type":"application/json",Accept:"application/json"},signal:r});if(r.aborted)return;if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);const t=await e.json();if(r.aborted)return;if(console.log("Backend Response:",t),"success"!==t.status)throw new Error(t.error||"Failed to fetch tenant ID");{const e=t.tenant.tenant_id;M(e),Y((t=>({...t,azure:e}))),G(null)}}catch(t){if("AbortError"===t.name)return void console.log("Fetch aborted");G(t.message),M(null)}finally{r.aborted||(B((t=>({...t,[e]:!1}))),j((t=>({...t,[e]:{...t[e],org:!1}}))))}})(),()=>{t.abort()}}),[e]),(0,n.useEffect)((()=>{if(!L)return;const t=new AbortController,r=t.signal;return(async()=>{if(H((t=>({...t,[e]:!0}))),j((t=>({...t,[e]:{...t[e],projects:!0}}))),X[e]&&X[e].length>0)return J(X[e]),ee(X[e][0].project_id||X[e][0].subscription_id||"all_projects"),re(null),H((t=>({...t,[e]:!1}))),void j((t=>({...t,[e]:{...t[e],projects:!1}})));try{let t,n;re(null),"azure"===e?(t="/api/azure/get-subscriptions",n={tenant_id:L}):"aws"===e?(t="/api/aws/get-accounts",n={organization_id:L}):(t="/api/gcp/get-projects",n={organization_id:L});const a=await fetch(t,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(n),signal:r});if(r.aborted)return;if(!a.ok)throw new Error(`HTTP error! status: ${a.status}`);const i=await a.json();if(r.aborted)return;if(console.log("API Response:",i),"success"!==i.status)throw new Error(i.error||"Failed to fetch data");{const t=i.projects||i.subscriptions||[],r=[{project_id:"all_projects",name:`All ${"aws"===e?"Accounts":"Projects"} (Aggregated)`,project_number:"N/A"},...t];J(r),Q((t=>({...t,[e]:r}))),t.length>0&&ee("all_projects")}}catch(t){if("AbortError"===t.name)return void console.log("Fetch aborted");console.error("Error fetching projects/subscriptions:",t),re(t.message),J([])}finally{r.aborted||(H((t=>({...t,[e]:!1}))),j((t=>({...t,[e]:{...t[e],projects:!1}}))))}})(),()=>{t.abort()}}),[L,e]),(0,n.useEffect)((()=>{B((t=>({...t,[e]:F[e].org}))),H((t=>({...t,[e]:F[e].projects})))}),[e,F]);const ze=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(null===e||void 0===e||null===(t=e.benchmark_results)||void 0===t||!t.groups)throw new Error("Invalid response format: missing benchmark_results or groups");l(e);const n=[],a=e=>{e.forEach((e=>{e.controls&&Array.isArray(e.controls)&&(e.controls.forEach((e=>{e.results&&Array.isArray(e.results)&&(e.results=e.results.map((e=>{var t;const r=null===(t=e.dimensions)||void 0===t?void 0:t.find((e=>["project_id","account_id","subscription_id"].includes(e.key)));return r&&(e.tags=e.tags||{},e.tags.project_id=r.value),e})))})),n.push(...e.controls)),e.groups&&Array.isArray(e.groups)&&a(e.groups)}))};a(e.benchmark_results.groups);return n.map((e=>{const t=r?{...e,projectId:r}:e;let n=t.results||[];Array.isArray(n)&&(n=n.map((e=>"error"===e.status&&e.reason&&ys(e.reason)?{...e,status:"skipped"}:e)));const a={ok:0,alarm:0,error:0,info:0,skipped:0};n.forEach((e=>{const t=e.status in a?e.status:"error";a[t]++}));const i=e.control_id.split(".")[0]||"Uncategorized";return{...t,results:n,summary:a,status:t.status||"info",category:i}}))};(0,n.useEffect)((()=>{const e=window.document.documentElement;r?e.classList.add("dark"):e.classList.remove("dark")}),[r]);const He=e=>{let{status:t,className:r=""}=e;const n=`${r}`;switch(null===t||void 0===t?void 0:t.toLowerCase()){case"ok":return(0,_.jsx)(o,{className:`${n} text-green-500`});case"alarm":return(0,_.jsx)(s,{className:`${n} text-red-500`});case"error":return(0,_.jsx)(s,{className:`${n} text-gray-500`});case"info":return(0,_.jsx)(c,{className:`${n} text-blue-500`});case"skipped":return(0,_.jsx)(u,{className:`${n} text-gray-500`});default:return null}},We=k.filter((e=>{var t,r;const n=ke.toLowerCase(),a=""===ke||(null===(t=e.title)||void 0===t?void 0:t.toLowerCase().includes(n))||(null===(r=e.control_id)||void 0===r?void 0:r.toLowerCase().includes(n)),i=Object.keys(Te).some((t=>{var r;return Te[t]&&((null===(r=e.summary)||void 0===r?void 0:r[t])||0)>0}));return a&&i}));return(0,_.jsxs)("div",{className:"flex min-h-screen",children:[(0,_.jsx)(bs,{activeProvider:e,onProviderChange:t}),(0,_.jsx)("div",{className:"flex-1",children:(0,_.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-200",children:[(W||V)&&(0,_.jsxs)("div",{className:"fixed top-0 left-0 right-0 bg-indigo-500 text-white py-2 px-4 flex items-center justify-center z-50 shadow-md",children:[(0,_.jsx)(f,{className:"w-5 h-5 animate-spin mr-2"}),(0,_.jsxs)("span",{children:["Loading ",e.toUpperCase()," ",W?"organization":"",W&&V?" and ":"",V?"aws"===e?"accounts":"azure"===e?"subscriptions":"projects":"","..."]})]}),(0,_.jsxs)("div",{className:"p-6 max-w-7xl mx-auto",children:[(0,_.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,_.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Compliance Benchmark Dashboard"}),(0,_.jsx)("div",{className:"flex items-center gap-4",children:(0,_.jsx)("button",{onClick:()=>{const e=!r;a(e),"undefined"!==typeof window&&localStorage.setItem("darkMode",JSON.stringify(e))},className:"p-2 rounded-lg bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200","aria-label":r?"Switch to light mode":"Switch to dark mode",children:r?(0,_.jsx)(h,{className:"w-5 h-5 text-yellow-500"}):(0,_.jsx)(d,{className:"w-5 h-5 text-gray-700"})})})]}),(0,_.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6 transition-all duration-300 hover:shadow-xl",children:[(0,_.jsxs)("h2",{className:"text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center gap-2",children:[(0,_.jsx)(p,{className:"w-5 h-5 text-indigo-500"}),"Benchmark Configuration"]}),(0,_.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,_.jsx)("div",{className:"bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-600 rounded-lg p-4 transition-all duration-200 hover:shadow-md",children:(0,_.jsxs)("div",{className:"flex items-center gap-3",children:[(0,_.jsx)(m,{className:"w-5 h-5 text-indigo-500"}),(0,_.jsxs)("div",{children:[(0,_.jsx)("div",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"azure"===e?"Tenant ID":"Organization ID"}),(0,_.jsx)("div",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:W?(0,_.jsxs)("div",{className:"flex items-center gap-2",children:[(0,_.jsx)(f,{className:"w-4 h-4 animate-spin"}),(0,_.jsx)("span",{children:"Loading..."})]}):$?(0,_.jsx)("span",{className:"text-red-500 text-sm",children:$}):(0,_.jsxs)("div",{className:"flex items-center",children:[(0,_.jsx)("span",{className:"truncate max-w-xs",children:L||"N/A"}),L&&(0,_.jsx)("button",{className:"ml-2 text-indigo-500 hover:text-indigo-600",onClick:()=>navigator.clipboard.writeText(L),children:(0,_.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,_.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"})})})]})})]})]})}),(0,_.jsx)("div",{className:"bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-600 rounded-lg p-4 transition-all duration-200 hover:shadow-md",children:(0,_.jsxs)("div",{className:"flex items-center gap-3",children:[(0,_.jsx)(g,{className:"w-5 h-5 text-indigo-500"}),(0,_.jsxs)("div",{className:"w-full",children:[(0,_.jsx)("div",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"aws"===e?"AWS Account":"azure"===e?"Azure Subscription":"GCP Project"}),V?(0,_.jsxs)("div",{className:"flex items-center gap-2 py-2 animate-pulse",children:[(0,_.jsx)(f,{className:"w-5 h-5 animate-spin text-indigo-500"}),(0,_.jsxs)("span",{className:"text-indigo-500 font-medium",children:["Loading ","azure"===e?"subscriptions":"aws"===e?"accounts":"projects","..."]})]}):te?(0,_.jsx)("span",{className:"text-red-500 text-sm",children:te}):q.length>0?(0,_.jsxs)("div",{className:"relative",children:[(0,_.jsxs)("select",{value:Z,onChange:e=>ee(e.target.value),className:"w-full appearance-none bg-transparent text-lg font-semibold text-gray-900 dark:text-white focus:outline-none pr-8",children:[(0,_.jsxs)("option",{value:"",className:"dark:bg-gray-800",children:["Select a ","azure"===e?"subscription":"project"]}),Ne.length>0&&(0,_.jsx)("optgroup",{label:"Favorites",className:"dark:bg-gray-800",children:q.filter((e=>Ne.includes(e.project_id||e.subscription_id))).map((e=>(0,_.jsxs)("option",{value:e.project_id||e.subscription_id,className:"dark:bg-gray-800",children:["\u2605 ",e.name]},`fav-${e.project_id||e.subscription_id}`)))}),(0,_.jsx)("optgroup",{label:"All "+("azure"===e?"Subscriptions":"Projects"),className:"dark:bg-gray-800",children:q.map((e=>(0,_.jsx)("option",{value:e.project_id||e.subscription_id,className:"dark:bg-gray-800",children:e.name},e.project_id||e.subscription_id)))})]}),(0,_.jsx)("div",{className:"absolute inset-y-0 right-0 flex items-center pointer-events-none",children:(0,_.jsx)(v,{className:"w-4 h-4 text-gray-500"})})]}):(0,_.jsx)("div",{className:"text-gray-900 dark:text-white",children:"aws"===e?"No accounts found":"azure"===e?"No subscriptions found":"No projects found"})]})]})}),(0,_.jsx)("div",{className:"bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-600 rounded-lg p-4 transition-all duration-200 hover:shadow-md",children:(0,_.jsxs)("div",{className:"flex items-center gap-3",children:[(0,_.jsx)(b,{className:"w-5 h-5 text-indigo-500"}),(0,_.jsxs)("div",{className:"w-full",children:[(0,_.jsx)("div",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Compliance Benchmark"}),(0,_.jsxs)("div",{className:"flex gap-2",children:[(0,_.jsxs)("div",{className:"relative flex-grow",children:[(0,_.jsxs)("select",{value:ce,onChange:e=>ue(e.target.value),className:"w-full appearance-none bg-transparent text-lg font-semibold text-gray-900 dark:text-white focus:outline-none pr-8",children:[(0,_.jsx)("option",{value:"",children:"Select a benchmark"}),Be[e].map((e=>(0,_.jsx)("option",{value:e.id,children:e.name},e.id)))]}),(0,_.jsx)("div",{className:"absolute inset-y-0 right-0 flex items-center pointer-events-none",children:(0,_.jsx)(v,{className:"w-4 h-4 text-gray-500"})})]}),ce&&(0,_.jsx)("button",{onClick:()=>{ce&&i&&P(!0)},disabled:de,className:"px-3 py-1 text-sm bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-200 dark:hover:bg-gray-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 transition-colors duration-200",children:de?(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)(f,{className:"w-4 h-4 animate-spin"}),"Loading..."]}):"Show Controls"})]})]})]})})]}),oe&&(0,_.jsxs)("div",{className:"mt-4 flex items-center text-sm text-gray-500 dark:text-gray-400",children:[(0,_.jsx)(y,{className:"w-4 h-4 mr-1"}),"Last benchmark run: ",oe.toLocaleString()]}),(0,_.jsx)("div",{className:"mt-6 flex justify-end",children:(0,_.jsx)("button",{onClick:async()=>{if(!Z||!ce)return void ge("Please select both a project/account/subscription and a compliance benchmark");l(null),he(null),ge(null),pe(!0);let t=0;const r=setInterval((()=>{t+=5,t>95?clearInterval(r):le(t)}),300);try{let t,i;"azure"===e?(t="/api/azure/run-azure-benchmark",i={benchmark:ce,subscription_id:"all_projects"===Z?"azure":Z}):"aws"===e?(t="/api/aws/run-aws-benchmark",i={benchmark:ce,account_id:"all_projects"===Z?"aws":Z}):(t="/api/gcp/run-benchmark",i={project_id:"all_projects"===Z?"gcp":Z,benchmark:ce});if("aws"===e&&"nist_800_53_rev_5"===ce&&"all_projects"===Z){var n;const a=await fetch(t,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(i)});if(!a.ok)throw new Error(`HTTP error! status: ${a.status}`);const l=a.body.getReader(),o=new TextDecoder("utf-8");let s="",c=!0,u=null;for(;;){const{done:e,value:t}=await l.read();if(e)break;const n=o.decode(t,{stream:!0});if(c){const e=n.split("\n",2);e.length>1?(u=JSON.parse(e[0]),s+=e[1]):s+=n,c=!1,clearInterval(r),le(10)}else if(s+=n,u&&u.total_size){const e=Math.floor(s.length/u.total_size*90)+10;le(Math.min(e,99))}}const f=JSON.parse(s),h=ze(f);he({...f,benchmark_results:h}),Me({title:(null===(n=Be[e].find((e=>e.id===ce)))||void 0===n?void 0:n.name)||"Benchmark Results",controls:h});const d=h.reduce(((e,t)=>{var r,n,a,i,l;return{ok:e.ok+((null===(r=t.summary)||void 0===r?void 0:r.ok)||0),alarm:e.alarm+((null===(n=t.summary)||void 0===n?void 0:n.alarm)||0),error:e.error+((null===(a=t.summary)||void 0===a?void 0:a.error)||0),info:e.info+((null===(i=t.summary)||void 0===i?void 0:i.info)||0),skipped:e.skipped+((null===(l=t.summary)||void 0===l?void 0:l.skipped)||0)}}),{ok:0,alarm:0,error:0,info:0,skipped:0});Ue(d),C(h),se(new Date)}else{var a;const r=await fetch(t,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(i)});if(!r.ok)throw new Error(`HTTP error! status: ${r.status}`);const n=await r.json(),l=ze(n);he({...n,benchmark_results:l}),Me({title:(null===(a=Be[e].find((e=>e.id===ce)))||void 0===a?void 0:a.name)||"Benchmark Results",controls:l});const o=l.reduce(((e,t)=>{var r,n,a,i,l;return{ok:e.ok+((null===(r=t.summary)||void 0===r?void 0:r.ok)||0),alarm:e.alarm+((null===(n=t.summary)||void 0===n?void 0:n.alarm)||0),error:e.error+((null===(a=t.summary)||void 0===a?void 0:a.error)||0),info:e.info+((null===(i=t.summary)||void 0===i?void 0:i.info)||0),skipped:e.skipped+((null===(l=t.summary)||void 0===l?void 0:l.skipped)||0)}}),{ok:0,alarm:0,error:0,info:0,skipped:0});Ue(o),C(l),se(new Date)}}catch(i){console.error("Benchmark error:",i),ge(`Benchmark Error: ${i.message}`),l(null),Me({title:"Benchmark Results",controls:[]}),Ue({ok:0,alarm:0,error:0,info:0,skipped:0}),C([])}finally{clearInterval(r),le(100),setTimeout((()=>le(0)),1e3),pe(!1),De({current:0,total:0,inProgress:!1,currentProject:"",completedProjects:[]})}},disabled:!Z||!ce||de,className:"px-4 py-2 bg-gradient-to-br from-indigo-600 to-indigo-500 text-white rounded-md hover:from-indigo-700 hover:to-indigo-600 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center gap-2 transition-all duration-200 shadow-lg hover:shadow-xl",children:de?(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)(f,{className:"w-5 h-5 animate-spin"}),"Running Benchmark..."]}):(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)(w,{className:"w-5 h-5"}),"Run Benchmark"]})})}),me&&(0,_.jsxs)("div",{className:"mt-4 p-4 bg-red-50 dark:bg-red-900/20 text-red-500 rounded-md flex items-start gap-3 animate-fadeIn",children:[(0,_.jsx)(s,{className:"w-5 h-5 mt-0.5 flex-shrink-0"}),(0,_.jsxs)("div",{children:[(0,_.jsx)("p",{className:"font-medium",children:"Error:"}),(0,_.jsx)("p",{children:me})]})]})]}),de&&(0,_.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 mb-6 overflow-hidden",children:(0,_.jsx)("div",{className:"bg-gradient-to-r from-indigo-500 to-indigo-400 h-2.5 rounded-full transition-all duration-300 ease-in-out",style:{width:`${ie}%`}})}),(0,_.jsx)(N,{data:i,isOpen:R,onClose:()=>P(!1)}),(null===fe||void 0===fe?void 0:fe.benchmark_results)&&(0,_.jsxs)("div",{className:"space-y-6",children:[(0,_.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8",children:[(0,_.jsxs)("div",{children:[(0,_.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:Le.title}),(0,_.jsxs)("p",{className:"text-gray-500 dark:text-gray-400 mt-1",children:["Last updated: ",(new Date).toLocaleDateString()]})]}),(0,_.jsxs)("div",{className:"flex gap-3",children:[(0,_.jsxs)("button",{onClick:()=>{if(!fe||!fe.benchmark_results)return void console.error("No benchmark results to export");const t=fe.benchmark_results.flatMap((e=>e.results&&0!==e.results.length?e.results.map((t=>{var r;const n=null===(r=t.dimensions)||void 0===r?void 0:r.find((e=>["project_id","account_id","subscription_id"].includes(e.key)));return{"Control ID":e.control_id,"Control Title":e.title,Status:t.status,Category:e.category,Resource:t.resource||"N/A",Reason:t.reason||"N/A","Project/Account/Subscription":(null===n||void 0===n?void 0:n.value)||"N/A"}})):[{"Control ID":e.control_id,"Control Title":e.title,Status:e.status,Category:e.category,Resource:"N/A",Reason:e.run_error||"No results","Project/Account/Subscription":"N/A"}])),r=vs.json_to_sheet(t),n=vs.book_new();vs.book_append_sheet(n,r,"Benchmark Results");!function(e,t,r){var n=r||{};n.type="file",n.file=t,is(n),as(e,n)}(n,`benchmark-results-${e}-${ce}-${(new Date).toISOString().split("T")[0]}.xlsx`)},disabled:!fe||de,className:"px-4 py-2 text-sm font-medium rounded-md shadow-sm flex items-center gap-2 "+(!fe||de?"bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed":"bg-green-600 hover:bg-green-700 text-white"),children:[(0,_.jsx)(T,{className:"w-4 h-4"}),"Export Results"]}),(0,_.jsxs)("div",{className:"relative",children:[(0,_.jsxs)("button",{onClick:()=>we(!ye),className:"px-4 py-2 text-sm font-medium text-white bg-gradient-to-br from-indigo-600 to-indigo-500 rounded-md shadow-sm hover:from-indigo-700 hover:to-indigo-600 flex items-center gap-2",children:[(0,_.jsx)(E,{className:"w-4 h-4"}),"Filters",ye?(0,_.jsx)(S,{className:"w-4 h-4"}):(0,_.jsx)(v,{className:"w-4 h-4"})]}),ye&&(0,_.jsxs)("div",{className:"absolute right-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-md shadow-lg py-2 z-50 border border-gray-200 dark:border-gray-700",children:[(0,_.jsx)("div",{className:"px-4 py-2 border-b border-gray-200 dark:border-gray-700",children:(0,_.jsx)("h3",{className:"text-sm font-medium text-gray-700 dark:text-gray-200",children:"Filter by Status"})}),(0,_.jsx)("div",{className:"max-h-60 overflow-y-auto",children:Object.entries(Te).map((e=>{let[t,r]=e;return(0,_.jsxs)("label",{className:"flex items-center justify-between px-4 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer",children:[(0,_.jsxs)("div",{className:"flex items-center gap-2",children:[(0,_.jsx)(He,{status:t,className:"w-4 h-4"}),(0,_.jsx)("span",{className:"text-gray-700 dark:text-white",children:Fe[t]})]}),(0,_.jsx)("input",{type:"checkbox",checked:r,onChange:()=>(e=>{Ee((t=>({...t,[e]:!t[e]})))})(t),className:"rounded text-indigo-600 focus:ring-indigo-500 dark:accent-indigo-500"})]},t)}))}),(0,_.jsx)("div",{className:"px-4 py-2 border-t border-gray-200 dark:border-gray-700",children:(0,_.jsxs)("div",{className:"flex justify-between",children:[(0,_.jsx)("button",{onClick:()=>Ee({ok:!0,alarm:!0,error:!0,info:!0,skipped:!0}),className:"text-xs text-indigo-600 dark:text-indigo-400 hover:underline",children:"Select All"}),(0,_.jsx)("button",{onClick:()=>Ee({ok:!1,alarm:!1,error:!1,info:!1,skipped:!1}),className:"text-xs text-indigo-600 dark:text-indigo-400 hover:underline",children:"Clear All"})]})})]})]})]})]}),(0,_.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 mb-8",children:["ok","alarm","error","info","skipped"].map((e=>(0,_.jsxs)("div",{className:"bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-700 rounded-lg shadow-sm p-6 flex items-center gap-4 hover:shadow-md transition-shadow duration-200",children:[(0,_.jsx)(He,{status:e,className:"w-8 h-8"}),(0,_.jsxs)("div",{children:[(0,_.jsx)("div",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:je[e]||0}),(0,_.jsx)("div",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:Fe[e]})]})]},e)))}),(0,_.jsx)("div",{className:"space-y-4",children:We.map((t=>(0,_.jsxs)("div",{className:"bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-700 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200",children:[(0,_.jsxs)("button",{onClick:()=>{return e=t.control_id,void be((t=>({...t,[e]:!t[e]})));var e},className:"w-full px-6 py-4 flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700 text-left focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 rounded-lg",children:[(0,_.jsxs)("div",{className:"flex items-center gap-3",children:[ve[t.control_id]?(0,_.jsx)(v,{className:"w-5 h-5 text-gray-400"}):(0,_.jsx)(x,{className:"w-5 h-5 text-gray-400"}),(0,_.jsxs)("div",{children:[(0,_.jsx)("span",{className:"text-lg font-medium text-gray-900 dark:text-white",children:t.title}),(0,_.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Control ID: ",t.control_id]})]})]}),(0,_.jsx)("div",{className:"flex items-center gap-4",children:Object.entries(t.summary||{}).map((e=>{let[t,r]=e;return r>0&&(0,_.jsxs)("div",{className:"flex items-center gap-1 bg-gray-50 dark:bg-gray-700 px-3 py-1 rounded-full",children:[(0,_.jsx)(He,{status:t,className:"w-5 h-5"}),(0,_.jsx)("span",{className:"text-sm font-medium text-gray-600 dark:text-gray-300",children:r})]},t)}))})]}),ve[t.control_id]&&(0,_.jsx)("div",{className:"px-6 py-4 border-t border-gray-100 dark:border-gray-700",children:t.results?(0,_.jsx)("div",{className:"space-y-4",children:t.results.map(((t,r)=>(0,_.jsx)("div",{className:"p-4 bg-gray-50 dark:bg-gray-700 rounded-lg",children:(0,_.jsxs)("div",{className:"flex items-start gap-3",children:[(0,_.jsx)(He,{status:t.status,className:"w-5 h-5 mt-1"}),(0,_.jsxs)("div",{children:[(0,_.jsx)("div",{className:"text-sm text-gray-900 dark:text-white font-medium",children:t.reason}),t.resource&&(0,_.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1",children:["Resource: ",(0,_.jsx)("code",{className:"px-2 py-1 bg-gray-100 dark:bg-gray-600 rounded",children:t.resource})]}),Array.isArray(t.dimensions)&&t.dimensions.length>0&&t.dimensions.map(((t,r)=>{const n={gcp:"Project Id:",aws:"Account Id:",azure:"Subscription Id:"}[e]||"Dimension:";return(0,_.jsxs)("div",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1",children:[n," ",(0,_.jsx)("code",{children:t.value})]},r)})),"skipped"===t.status&&ys(t.reason)&&(0,_.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1 italic",children:"(API not enabled for this service)"})]})]})},r)))}):t.run_error?(0,_.jsx)("div",{className:"text-sm p-4 rounded-lg "+(ys(t.run_error)?"text-gray-500 bg-gray-50 dark:bg-gray-700 dark:text-gray-400":"text-red-500 bg-red-50 dark:bg-red-900/20"),children:ys(t.run_error)?(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)("span",{className:"font-medium",children:"Skipped:"})," ",t.run_error.split("\nDetails:")[0],(0,_.jsx)("div",{className:"mt-1 italic",children:"(API not enabled for this service)"})]}):(0,_.jsxs)(_.Fragment,{children:["Error: ",t.run_error.split("\nDetails:")[0]]})}):(0,_.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:"No results available"})})]},t.control_id)))})]})]})]})})]})},Ts=()=>{const[e,t]=(0,n.useState)(""),[r,a]=(0,n.useState)(""),[i,l]=(0,n.useState)(!1),[s,c]=(0,n.useState)(!1),[u,h]=(0,n.useState)(null),[d,p]=(0,n.useState)(!1),[m,g]=(0,n.useState)(""),[v,b]=(0,n.useState)("gcp"),y=[{id:"gcp",name:"GCP",color:"from-blue-500 to-blue-600",icon:O,iconColor:"text-blue-500",generateEndpoint:"/api/gcp/generate",testEndpoint:"/api/gcp/testcontrol"},{id:"aws",name:"AWS",color:"from-orange-500 to-orange-600",icon:R,iconColor:"text-orange-500",generateEndpoint:"/api/aws/generate",testEndpoint:"/api/aws/testcontrol"},{id:"azure",name:"Azure",color:"from-blue-600 to-blue-700",icon:P,iconColor:"text-blue-600",generateEndpoint:"/api/azure/generate",testEndpoint:"/api/azure/testcontrol"}],w=y.find((e=>e.id===v))||y[0];return(0,_.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6",children:[(0,_.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,_.jsx)("h2",{className:"text-xl font-bold text-gray-900 dark:text-white",children:"Generate Control"}),(0,_.jsx)("div",{className:"flex space-x-2 bg-gray-100 dark:bg-gray-700 rounded-lg p-1",children:y.map((e=>{const t=e.icon,r=v===e.id;return(0,_.jsxs)("button",{onClick:()=>b(e.id),className:"px-3 py-2 rounded-md flex items-center gap-2 transition-all duration-200 "+(r?`bg-gradient-to-r ${e.color} text-white shadow-sm`:`text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 ${e.iconColor}`),children:[(0,_.jsx)(t,{className:"w-4 h-4"}),(0,_.jsx)("span",{className:"text-xs font-medium",children:e.name})]},e.id)}))})]}),(0,_.jsxs)("div",{className:"mb-4",children:[(0,_.jsx)("label",{htmlFor:"prompt",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Prompt:"}),(0,_.jsx)("textarea",{id:"prompt",value:e,onChange:e=>t(e.target.value),className:"mt-1 block w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",placeholder:`Enter your prompt to generate a ${v.toUpperCase()} control...`,rows:"4"})]}),(0,_.jsxs)("div",{className:"flex gap-2",children:[(0,_.jsx)("button",{onClick:async()=>{if(e){l(!0),h(null),a(""),p(!1),g("");try{const t=await fetch(w.generateEndpoint,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({prompt:e,provider:v})});if(!t.ok){const e=await t.json();throw new Error(e.error||`HTTP error! status: ${t.status}`)}const r=await t.json();a(r.response)}catch(t){console.error("Benchmark error:",t),h(t.message)}finally{l(!1)}}else h("Please enter a prompt.")},disabled:i||s,className:"px-4 py-2 text-white rounded-md hover:opacity-90 disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center gap-2 "+(i?"bg-gray-400":`bg-gradient-to-r ${w.color}`),children:i?(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)(f,{className:"w-4 h-4 animate-spin"}),"Generating..."]}):(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)(w.icon,{className:"w-4 h-4"}),"Generate ",w.name," Control"]})}),(0,_.jsx)("button",{onClick:async()=>{c(!0),h(null),p(!1),g("");try{const e=await fetch(w.testEndpoint,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({provider:v})});if(!e.ok){const t=await e.json();throw new Error(t.error||`HTTP error! Status: ${e.status}`)}const t=await e.json();g(JSON.stringify(t,null,2)),t&&Array.isArray(t)&&t.every((e=>"ok"===e.status))?p(!0):p(!1)}catch(e){h(e.message),p(!1)}finally{c(!1)}},disabled:i||s||!r,className:`px-4 py-2 text-white rounded-md flex items-center gap-2\n                    ${r&&!s?"bg-green-600 hover:bg-green-700":"bg-gray-400 cursor-not-allowed"}\n                    `,children:s?(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)(f,{className:"w-4 h-4 animate-spin"}),"Testing..."]}):(0,_.jsxs)(_.Fragment,{children:[d&&(0,_.jsx)(o,{className:"w-4 h-4"}),"Test Control"]})})]}),u&&(0,_.jsx)("div",{className:"mt-4 p-4 bg-red-50 dark:bg-red-900/20 text-red-500 rounded-md",children:u}),r&&(0,_.jsxs)("div",{className:"mt-4",children:[(0,_.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:["Generated ",w.name," Control:"]}),(0,_.jsx)("pre",{className:"bg-gray-100 dark:bg-gray-900 p-4 rounded-md overflow-x-auto text-sm text-gray-700 dark:text-gray-300",children:(0,_.jsx)("code",{children:r})})]}),m&&(0,_.jsxs)("div",{className:"mt-4",children:[(0,_.jsx)("h3",{className:"text-lg font-semibold mb-2 "+(d?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400"),children:"Test Results:"}),(0,_.jsx)("pre",{className:"bg-gray-100 dark:bg-gray-900 p-4 rounded-md overflow-x-auto text-sm text-gray-700 dark:text-gray-300",children:(0,_.jsx)("code",{children:m})})]})]})};const Es=function(){const[e,t]=(0,n.useState)("dashboard");return(0,_.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200",children:(0,_.jsxs)("div",{className:"p-6 max-w-7xl mx-auto",children:[(0,_.jsx)("div",{className:"mb-4 border-b border-gray-200 dark:border-gray-700",children:(0,_.jsxs)("ul",{className:"flex flex-wrap -mb-px text-sm font-medium text-center text-gray-500 dark:text-gray-400",children:[(0,_.jsx)("li",{className:"mr-2",children:(0,_.jsx)("button",{className:"inline-block p-4 border-b-2 rounded-t-lg "+("dashboard"===e?"border-blue-600 text-blue-600 dark:text-blue-500 dark:border-blue-500":"hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300"),onClick:()=>t("dashboard"),children:"Benchmark Dashboard"})}),(0,_.jsx)("li",{className:"mr-2",children:(0,_.jsx)("button",{className:"inline-block p-4 border-b-2 rounded-t-lg "+("generate"===e?"border-blue-600 text-blue-600 dark:text-blue-500 dark:border-blue-500":"hover:text-gray-600 hover:border-gray-300 dark:hover:text-gray-300"),onClick:()=>t("generate"),children:"Generate Control"})})]})}),"dashboard"===e&&(0,_.jsx)(ws,{}),"generate"===e&&(0,_.jsx)(Ts,{})]})})},Ss=e=>{e&&e instanceof Function&&r.e(685).then(r.bind(r,685)).then((t=>{let{getCLS:r,getFID:n,getFCP:a,getLCP:i,getTTFB:l}=t;r(e),n(e),a(e),i(e),l(e)}))};a.createRoot(document.getElementById("root")).render((0,_.jsx)(n.StrictMode,{children:(0,_.jsx)(Es,{})})),Ss()})();