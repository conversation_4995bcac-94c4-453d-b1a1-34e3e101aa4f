#!/bin/sh
echo "=== Cloud Run Nginx Frontend Startup ==="
echo "Environment variables:"
echo "BACKEND_GCP_URL: ${BACKEND_GCP_URL}"
echo "BACKEND_AWS_URL: ${BACKEND_AWS_URL}"
echo "BACKEND_AZURE_URL: ${BACKEND_AZURE_URL}"

# Replace backend URLs in nginx config
if [ -f /etc/nginx/nginx-cloudrun.conf ]; then
    cp /etc/nginx/nginx-cloudrun.conf /etc/nginx/nginx.conf
fi

# Use envsubst to replace environment variables in nginx config
envsubst '$BACKEND_GCP_URL $BACKEND_AWS_URL $BACKEND_AZURE_URL' < /etc/nginx/nginx.conf > /tmp/nginx.conf
mv /tmp/nginx.conf /etc/nginx/nginx.conf

echo "Nginx config after replacement:"
grep -E "backend_url|proxy_pass" /etc/nginx/nginx.conf

# Copy simple dashboard
if [ -f /app/simple-dashboard.html ]; then
    cp /app/simple-dashboard.html /usr/share/nginx/html/
fi

# Start nginx
exec nginx -g 'daemon off;'