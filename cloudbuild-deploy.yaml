# Cloud Build Configuration for Complete Cloud Run Deployment
# Builds and deploys all services with incremental testing
# CI/CD Pipeline with comprehensive error handling and validation

steps:
  # ============================================
  # PHASE 1: BUILD ALL IMAGES
  # ============================================
  
  # Step 1: Build base image
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-base'
    args:
      - 'build'
      - '--platform=linux/amd64'
      - '-t'
      - 'gcr.io/$PROJECT_ID/steampipe-base:latest'
      - '-t'
      - 'gcr.io/$PROJECT_ID/steampipe-base:$SHORT_SHA'
      - '-f'
      - 'Base_image_steampipe/Dockerfile'
      - '.'
    env:
      - 'DOCKER_BUILDKIT=1'

  # Test base image build
  - name: 'gcr.io/cloud-builders/docker'
    id: 'test-base'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        echo "Testing base image..."
        docker run --rm gcr.io/$PROJECT_ID/steampipe-base:latest steampipe --version || exit 1
        docker run --rm gcr.io/$PROJECT_ID/steampipe-base:latest powerpipe --version || exit 1
        echo "✅ Base image test passed"
    waitFor: ['build-base']

  # Push base image after test
  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-base'
    args:
      - 'push'
      - '--all-tags'
      - 'gcr.io/$PROJECT_ID/steampipe-base'
    waitFor: ['test-base']

  # Step 2: Build Admin API
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-admin-api'
    args:
      - 'build'
      - '--platform=linux/amd64'
      - '-t'
      - 'gcr.io/$PROJECT_ID/admin-api:latest'
      - '-t'
      - 'gcr.io/$PROJECT_ID/admin-api:$SHORT_SHA'
      - '-f'
      - 'custom/services/admin/Dockerfile'
      - '.'
    waitFor: ['-']

  # Step 3: Build compliance services (after base is pushed)
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-gcp-compliance'
    args:
      - 'build'
      - '--platform=linux/amd64'
      - '--build-arg'
      - 'BASE_IMAGE=gcr.io/$PROJECT_ID/steampipe-base:latest'
      - '-t'
      - 'gcr.io/$PROJECT_ID/steampipe-gcp-compliance:latest'
      - '-t'
      - 'gcr.io/$PROJECT_ID/steampipe-gcp-compliance:$SHORT_SHA'
      - '-f'
      - 'custom/services/gcp/Dockerfile'
      - '.'
    waitFor: ['push-base']

  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-aws-compliance'
    args:
      - 'build'
      - '--platform=linux/amd64'
      - '--build-arg'
      - 'BASE_IMAGE=gcr.io/$PROJECT_ID/steampipe-base:latest'
      - '-t'
      - 'gcr.io/$PROJECT_ID/steampipe-aws-compliance:latest'
      - '-t'
      - 'gcr.io/$PROJECT_ID/steampipe-aws-compliance:$SHORT_SHA'
      - '-f'
      - 'custom/services/aws/Dockerfile'
      - '.'
    waitFor: ['push-base']

  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-azure-compliance'
    args:
      - 'build'
      - '--platform=linux/amd64'
      - '--build-arg'
      - 'BASE_IMAGE=gcr.io/$PROJECT_ID/steampipe-base:latest'
      - '-t'
      - 'gcr.io/$PROJECT_ID/steampipe-azure-compliance:latest'
      - '-t'
      - 'gcr.io/$PROJECT_ID/steampipe-azure-compliance:$SHORT_SHA'
      - '-f'
      - 'custom/services/azure/Dockerfile'
      - '.'
    waitFor: ['push-base']

  # Step 4: Build frontend services (parallel)
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-api-gateway'
    args:
      - 'build'
      - '--platform=linux/amd64'
      - '-t'
      - 'gcr.io/$PROJECT_ID/api-gateway:latest'
      - '-t'
      - 'gcr.io/$PROJECT_ID/api-gateway:$SHORT_SHA'
      - '-f'
      - 'api-gateway/Dockerfile'
      - 'api-gateway/'
    waitFor: ['-']


  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-dashboard-ui'
    args:
      - 'build'
      - '--platform=linux/amd64'
      - '-t'
      - 'gcr.io/$PROJECT_ID/dashboard-ui:latest'
      - '-t'
      - 'gcr.io/$PROJECT_ID/dashboard-ui:$SHORT_SHA'
      - '-f'
      - 'custom/dashboard/steampipe-secops-dashboard/frontend/Dockerfile'
      - 'custom/dashboard/steampipe-secops-dashboard/frontend/'
    waitFor: ['-']

  # ============================================
  # PHASE 2: PUSH ALL IMAGES
  # ============================================
  
  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-admin-api'
    args: ['push', '--all-tags', 'gcr.io/$PROJECT_ID/admin-api']
    waitFor: ['build-admin-api']

  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-gcp-compliance'
    args: ['push', '--all-tags', 'gcr.io/$PROJECT_ID/steampipe-gcp-compliance']
    waitFor: ['build-gcp-compliance']

  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-aws-compliance'
    args: ['push', '--all-tags', 'gcr.io/$PROJECT_ID/steampipe-aws-compliance']
    waitFor: ['build-aws-compliance']

  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-azure-compliance'
    args: ['push', '--all-tags', 'gcr.io/$PROJECT_ID/steampipe-azure-compliance']
    waitFor: ['build-azure-compliance']

  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-api-gateway'
    args: ['push', '--all-tags', 'gcr.io/$PROJECT_ID/api-gateway']
    waitFor: ['build-api-gateway']


  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-dashboard-ui'
    args: ['push', '--all-tags', 'gcr.io/$PROJECT_ID/dashboard-ui']
    waitFor: ['build-dashboard-ui']

  # ============================================
  # PHASE 3: DEPLOY BACKEND SERVICES
  # ============================================

  # Deploy Admin API first
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'deploy-admin-api'
    args:
      - 'run'
      - 'deploy'
      - 'admin-api'
      - '--image=gcr.io/$PROJECT_ID/admin-api:$SHORT_SHA'
      - '--region=${_REGION}'
      - '--platform=managed'
      - '--allow-unauthenticated'
      - '--memory=2Gi'
      - '--cpu=2'
      - '--port=8085'
      - '--set-env-vars=PROJECT_ID=$PROJECT_ID,DOMAIN=${_DOMAIN}'
      - '--min-instances=1'
      - '--max-instances=10'
      - '--timeout=300s'
    waitFor: ['push-admin-api']

  # Test Admin API deployment
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'test-admin-api'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        echo "Testing Admin API deployment..."
        URL=$(gcloud run services describe admin-api --region=${_REGION} --format='value(status.url)')
        curl -f $URL/health || exit 1
        echo "✅ Admin API health check passed"
    waitFor: ['deploy-admin-api']

  # Deploy GCP Compliance Service
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'deploy-gcp-compliance'
    args:
      - 'run'
      - 'deploy'
      - 'steampipe-gcp-compliance'
      - '--image=gcr.io/$PROJECT_ID/steampipe-gcp-compliance:$SHORT_SHA'
      - '--region=${_REGION}'
      - '--platform=managed'
      - '--no-allow-unauthenticated'
      - '--memory=8Gi'
      - '--cpu=4'
      - '--port=8080'
      - '--set-env-vars'
      - |
        AUTH_TYPE=secret_manager,
        PROJECT_ID=$PROJECT_ID,
        DOMAIN=${_DOMAIN},
        STEAMPIPE_UPDATE_CHECK=false,
        POWERPIPE_UPDATE_CHECK=false,
        STEAMPIPE_DATABASE_PASSWORD=steampipe,
        STEAMPIPE_INIT_TIMEOUT=300,
        STEAMPIPE_TIMEOUT=600s,
        SERVICE_TYPE=gcp
      - '--min-instances=1'
      - '--max-instances=5'
      - '--timeout=3600s'
      - '--concurrency=5'
      - '--service-account=steampipe-sa@$PROJECT_ID.iam.gserviceaccount.com'
    waitFor: ['push-gcp-compliance']

  # Deploy AWS Compliance Service
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'deploy-aws-compliance'
    args:
      - 'run'
      - 'deploy'
      - 'steampipe-aws-compliance'
      - '--image=gcr.io/$PROJECT_ID/steampipe-aws-compliance:$SHORT_SHA'
      - '--region=${_REGION}'
      - '--platform=managed'
      - '--no-allow-unauthenticated'
      - '--memory=8Gi'
      - '--cpu=4'
      - '--port=8082'
      - '--set-env-vars'
      - |
        AUTH_TYPE=secret_manager,
        PROJECT_ID=$PROJECT_ID,
        DOMAIN=${_DOMAIN},
        STEAMPIPE_UPDATE_CHECK=false,
        POWERPIPE_UPDATE_CHECK=false,
        STEAMPIPE_DATABASE_PASSWORD=steampipe,
        STEAMPIPE_INIT_TIMEOUT=300,
        STEAMPIPE_TIMEOUT=600s,
        SERVICE_TYPE=aws,
        AWS_REGION=us-east-1
      - '--min-instances=1'
      - '--max-instances=5'
      - '--timeout=3600s'
      - '--concurrency=5'
      - '--service-account=steampipe-sa@$PROJECT_ID.iam.gserviceaccount.com'
    waitFor: ['push-aws-compliance']

  # Deploy Azure Compliance Service
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'deploy-azure-compliance'
    args:
      - 'run'
      - 'deploy'
      - 'steampipe-azure-compliance'
      - '--image=gcr.io/$PROJECT_ID/steampipe-azure-compliance:$SHORT_SHA'
      - '--region=${_REGION}'
      - '--platform=managed'
      - '--no-allow-unauthenticated'
      - '--memory=8Gi'
      - '--cpu=4'
      - '--port=8083'
      - '--set-env-vars'
      - |
        AUTH_TYPE=secret_manager,
        PROJECT_ID=$PROJECT_ID,
        DOMAIN=${_DOMAIN},
        STEAMPIPE_UPDATE_CHECK=false,
        POWERPIPE_UPDATE_CHECK=false,
        STEAMPIPE_DATABASE_PASSWORD=steampipe,
        STEAMPIPE_INIT_TIMEOUT=300,
        STEAMPIPE_TIMEOUT=600s,
        SERVICE_TYPE=azure
      - '--min-instances=1'
      - '--max-instances=5'
      - '--timeout=3600s'
      - '--concurrency=5'
      - '--service-account=steampipe-sa@$PROJECT_ID.iam.gserviceaccount.com'
    waitFor: ['push-azure-compliance']

  # ============================================
  # PHASE 4: GET BACKEND SERVICE URLS
  # ============================================
  
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'get-backend-urls'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        # Get backend service URLs
        GCP_URL=$(gcloud run services describe steampipe-gcp-compliance --region=${_REGION} --format='value(status.url)')
        AWS_URL=$(gcloud run services describe steampipe-aws-compliance --region=${_REGION} --format='value(status.url)')
        AZURE_URL=$(gcloud run services describe steampipe-azure-compliance --region=${_REGION} --format='value(status.url)')
        ADMIN_URL=$(gcloud run services describe admin-api --region=${_REGION} --format='value(status.url)')
        
        # Save URLs for next steps
        echo "$GCP_URL" > /workspace/gcp_url.txt
        echo "$AWS_URL" > /workspace/aws_url.txt
        echo "$AZURE_URL" > /workspace/azure_url.txt
        echo "$ADMIN_URL" > /workspace/admin_url.txt
        
        echo "Backend URLs retrieved:"
        echo "GCP: $GCP_URL"
        echo "AWS: $AWS_URL"
        echo "Azure: $AZURE_URL"
        echo "Admin: $ADMIN_URL"
    waitFor: ['deploy-gcp-compliance', 'deploy-aws-compliance', 'deploy-azure-compliance', 'test-admin-api']

  # ============================================
  # PHASE 5: DEPLOY API GATEWAY WITH BACKEND URLS
  # ============================================
  
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'deploy-api-gateway'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        # Read backend URLs
        GCP_URL=$(cat /workspace/gcp_url.txt)
        AWS_URL=$(cat /workspace/aws_url.txt)
        AZURE_URL=$(cat /workspace/azure_url.txt)
        
        # Deploy API Gateway with backend URLs
        gcloud run deploy api-gateway \
          --image=gcr.io/$PROJECT_ID/api-gateway:$SHORT_SHA \
          --region=${_REGION} \
          --platform=managed \
          --allow-unauthenticated \
          --memory=2Gi \
          --cpu=2 \
          --port=8081 \
          --set-env-vars="GCP_BACKEND_URL=$GCP_URL,AWS_BACKEND_URL=$AWS_URL,AZURE_BACKEND_URL=$AZURE_URL" \
          --min-instances=1 \
          --max-instances=10 \
          --timeout=300s
    waitFor: ['push-api-gateway', 'get-backend-urls']

  # Test API Gateway
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'test-api-gateway'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        echo "Testing API Gateway deployment..."
        URL=$(gcloud run services describe api-gateway --region=${_REGION} --format='value(status.url)')
        echo "$URL" > /workspace/gateway_url.txt
        
        # Test health endpoint
        curl -f $URL/health || exit 1
        echo "✅ API Gateway health check passed"
        
        # Test routing to backends
        echo "Testing backend routing..."
        curl -f $URL/gcp/health || echo "⚠️  GCP backend not ready yet"
        curl -f $URL/aws/health || echo "⚠️  AWS backend not ready yet"
        curl -f $URL/azure/health || echo "⚠️  Azure backend not ready yet"
    waitFor: ['deploy-api-gateway']

  # ============================================
  # PHASE 6: DEPLOY FRONTEND SERVICES
  # ============================================


  # Deploy Dashboard UI
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'deploy-dashboard-ui'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        # Read gateway URL
        GATEWAY_URL=$(cat /workspace/gateway_url.txt)
        
        # Deploy Dashboard UI
        gcloud run deploy dashboard-ui \
          --image=gcr.io/$PROJECT_ID/dashboard-ui:$SHORT_SHA \
          --region=${_REGION} \
          --platform=managed \
          --allow-unauthenticated \
          --memory=1Gi \
          --cpu=1 \
          --port=8081 \
          --set-env-vars="REACT_APP_API_GATEWAY_URL=$GATEWAY_URL,REACT_APP_PROJECT_ID=$PROJECT_ID,REACT_APP_DOMAIN=${_DOMAIN}" \
          --min-instances=1 \
          --max-instances=10 \
          --timeout=300s
    waitFor: ['push-dashboard-ui', 'test-api-gateway']

  # ============================================
  # PHASE 7: FINAL TESTING AND VALIDATION
  # ============================================

  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'final-validation'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        echo "============================================"
        echo "🚀 DEPLOYMENT COMPLETE - FINAL VALIDATION 🚀"
        echo "============================================"
        
        # Get all service URLs
        ADMIN_API_URL=$(gcloud run services describe admin-api --region=${_REGION} --format='value(status.url)')
        DASHBOARD_UI_URL=$(gcloud run services describe dashboard-ui --region=${_REGION} --format='value(status.url)')
        GATEWAY_URL=$(gcloud run services describe api-gateway --region=${_REGION} --format='value(status.url)')
        
        echo ""
        echo "📌 PUBLIC ENDPOINTS:"
        echo "Admin API: $ADMIN_API_URL"
        echo "Dashboard UI: $DASHBOARD_UI_URL"
        echo "API Gateway: $GATEWAY_URL"
        echo ""
        
        # Test all public endpoints
        echo "🔍 Testing public endpoints..."
        curl -f $ADMIN_API_URL/health || echo "❌ Admin API not responding"
        curl -f $DASHBOARD_UI_URL || echo "❌ Dashboard UI not responding"
        curl -f $GATEWAY_URL/health || echo "❌ API Gateway not responding"
        
        echo ""
        echo "📊 SERVICE STATUS:"
        gcloud run services list --platform=managed --region=${_REGION} --filter="metadata.name:(steampipe OR admin OR dashboard OR gateway)" --format="table(metadata.name,status.conditions[0].type,status.conditions[0].status)"
        
        echo ""
        echo "🔗 Cloud Console Links:"
        echo "Services: https://console.cloud.google.com/run?project=$PROJECT_ID"
        echo "Logs: https://console.cloud.google.com/logs?project=$PROJECT_ID"
        echo ""
        echo "✅ Deployment completed successfully!"
    waitFor: ['deploy-dashboard-ui']

# Substitutions
substitutions:
  _REGION: 'us-central1'
  _DOMAIN: 'steampipe-compliance.com'

# Build options
options:
  machineType: 'E2_HIGHCPU_8'
  logging: CLOUD_LOGGING_ONLY
  env:
    - 'DOCKER_BUILDKIT=1'
  dynamicSubstitutions: true

# Timeout for the entire build and deployment
timeout: 3600s