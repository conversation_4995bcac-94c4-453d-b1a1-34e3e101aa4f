## Description

Once a VPC peering connection is established, routing tables must be updated to enable any connections between the peered VPCs. These routes can be as specific as desired, even allowing for the peering of a VPC to only a single host on the other side of the connection.

Being highly selective in peering routing tables is a very effective way to minimize the impact of a breach, as resources outside of these routes are inaccessible to the peered VPC.

## Remediation

Remove and add route table entries to ensure that the least number of subnets or hosts required to accomplish the purpose of peering are routable.

### From Command Line

1. For each `<route-table-id>` that contains routes that are non-compliant with your routing policy (granting more access than desired), delete the non-compliant route:

```bash
aws ec2 delete-route --route-table-id <route-table-id> --destination-cidr-block <non-compliant-destination-cidr>
```

2. Create a new compliant route:

```bash
aws ec2 create-route --route-table-id <route-table-id> --destination-cidr-block <compliant-destination-cidr> --vpc-peering-connection-id <peering-connection-id>
```
