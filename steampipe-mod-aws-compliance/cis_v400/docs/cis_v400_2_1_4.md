## Description

Amazon S3 provides `Block public access (bucket settings)` and `Block public access (account settings)` to help you manage public access to Amazon S3 resources. By default, S3 buckets and objects are created with public access disabled. However, an IAM principal with sufficient S3 permissions can enable public access at the bucket and/or object level. While enabled, `Block public access (bucket settings)` prevents an individual bucket and its contained objects from becoming publicly accessible. Similarly, `Block public access (account settings)` prevents all buckets and their contained objects from becoming publicly accessible across the entire account.

Amazon S3 `Block public access (bucket settings)` prevents the accidental or malicious public exposure of data contained within the respective bucket(s).

Amazon S3 `Block public access (account settings)` prevents the accidental or malicious public exposure of data contained within all buckets of the respective AWS account.

Whether to block public access to all or some buckets is an organizational decision that should be based on data sensitivity, least privilege, and use case.

## Remediation

**If utilizing Block Public Access (bucket settings)**

### From Console

1. Login to the AWS Management Console and open the Amazon S3 console using [https://console.aws.amazon.com/s3/](https://console.aws.amazon.com/s3/).
2. Select the check box next to a bucket.
3. Click 'Edit public access settings'.
4. Click 'Block all public access'.
5. Repeat for all the buckets in your AWS account that contain sensitive data.

### From Command Line

1. List all of the S3 buckets:

```bash
aws s3 ls
```

2. Enable Block Public Access on a specific bucket:

```bash
aws s3api put-public-access-block --bucket <bucket-name> --public-access-block-configuration "BlockPublicAcls=true,IgnorePublicAcls=true,BlockPublicPolicy=true,RestrictPublicBuckets=true"
```

**If utilizing Block Public Access (account settings)**

### From Console

If the output reads `true` for the separate configuration settings, then Block Public Access is enabled on the account.

1. Login to the AWS Management Console and open the Amazon S3 console using [https://console.aws.amazon.com/s3/](https://console.aws.amazon.com/s3/).
2. Click `Block Public Access (account settings)`.
3. Click `Edit` to change the block public access settings for all the buckets in your AWS account.
4. Update the settings and click `Save`. For details about each setting, pause on the `i` icons.
5. When you're asked for confirmation, enter `confirm`. Then click `Confirm` to save your changes.

### From Command Line

To enable Block Public Access for this account, run the following command:

```bash
aws s3control put-public-access-block
--public-access-block-configuration BlockPublicAcls=true, IgnorePublicAcls=true, BlockPublicPolicy=true, RestrictPublicBuckets=true
--account-id <account-id>
```
