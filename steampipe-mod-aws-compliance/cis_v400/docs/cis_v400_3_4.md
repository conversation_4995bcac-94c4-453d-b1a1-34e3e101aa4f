## Description

Server access logging generates a log that contains access records for each request made to your S3 bucket. An access log record contains details about the request, such as the request type, the resources specified in the request worked, and the time and date the request was processed. It is recommended that server access logging be enabled on the CloudTrail S3 bucket.

By enabling server access logging on target S3 buckets, it is possible to capture all events that may affect objects within any target bucket. Configuring the logs to be placed in a separate bucket allows access to log information that can be useful in security and incident response workflows.

## Remediation

Perform the following to enable server access logging:

### From Console

1. Sign in to the AWS Management Console and open the S3 console at [https://console.aws.amazon.com/s3](https://console.aws.amazon.com/s3).
2. Under `All Buckets` click on the target S3 bucket.
3. Click on `Properties` in the top right of the console.
4. Under `Bucket: <bucket-name>`, click `Logging`.
5. Configure bucket logging:
 - Check the `Enabled` box.
 - Select a Target Bucket from the list.
 - Enter a Target Prefix.
6. Click `Save`.

### From Command Line

1. Get the name of the S3 bucket that CloudTrail is logging to:

```bash
aws cloudtrail describe-trails --region <region-name> --query trailList[*].S3BucketName
```

2. Copy and add the target bucket name at `<bucket-name>`, the prefix for the log file at `<log-file-prefix>`, and optionally add an email address in the following template, then save it as `<file-name>.json`:

```json
{
  "LoggingEnabled": {
    "TargetBucket": "<bucket-name>",
    "TargetPrefix": "<log-file-prefix>",
    "TargetGrants": [
      {
        "Grantee": {
          "Type": "AmazonCustomerByEmail",
          "EmailAddress": "<email-address>"
        },
        "Permission": "FULL_CONTROL"
      }
    ]
  }
}
```

3. Run the `put-bucket-logging` command with bucket name and `<file-name>.json` as input; for more information, refer to [put-bucket-logging](https://docs.aws.amazon.com/cli/latest/reference/s3api/put-bucket-logging.html):

```bash
aws s3api put-bucket-logging --bucket <bucket-name> --bucket-logging-status file://<file-name>.json
```

### Default Value

Logging is disabled.
