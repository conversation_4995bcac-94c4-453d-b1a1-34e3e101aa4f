## Description

Security groups provide stateful filtering of ingress and egress network traffic to AWS resources. It is recommended that no security group allows unrestricted ingress access to remote server administration ports, such as SSH on port `22` and RDP on port `3389`, using either the TCP (6), UDP (17), or ALL (-1) protocols.

Public access to remote server administration ports, such as 22 (when used for SSH, not SFTP) and 3389, increases the attack surface of resources and unnecessarily raises the risk of resource compromise.

## Remediation

Perform the following to implement the prescribed state:

1. <PERSON>gin to the AWS VPC Console at [https://console.aws.amazon.com/vpc/home](https://console.aws.amazon.com/vpc/home).
2. In the left pane, click `Security Groups`.
3. For each security group, perform the following:
 - Select the security group.
 - Click the `Inbound Rules` tab.
 - Click the `Edit inbound rules` button.
 - Identify the rules to be edited or removed.
 - Either A) update the Source field to a range other than 0.0.0.0/0, or B) click `Delete` to remove the offending inbound rule.
 - Click `Save rules`.
