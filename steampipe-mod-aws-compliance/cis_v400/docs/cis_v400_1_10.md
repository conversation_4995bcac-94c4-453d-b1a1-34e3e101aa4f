## Description

Multi-Factor Authentication (MFA) adds an extra layer of authentication assurance beyond traditional credentials. With MFA enabled, when a user signs in to the AWS Console, they will be prompted for their user name and password as well as for an authentication code from their physical or virtual MFA token. It is recommended that MFA be enabled for all accounts that have a console password.

Enabling MFA provides increased security for console access as it requires the authenticating principal to possess a device that displays a time-sensitive key and have knowledge of a credential.

## Remediation

Perform the following to enable MFA:

### From Console

1. Sign in to the AWS Management Console and open the IAM console at 'https://console.aws.amazon.com/iam/'.
2. In the left pane, select `Users`.
3. In the `User Name` list, choose the name of the intended MFA user.
4. Choose the `Security Credentials` tab, and then choose `Manage MFA Device`.
5. In the `Manage MFA Device wizard`, choose `Virtual MFA` device, and then choose `Continue`.

 IAM generates and displays configuration information for the virtual MFA device, including a QR code graphic. The graphic is a representation of the 'secret configuration key' that is available for manual entry on devices that do not support QR codes.

6. Open your virtual MFA application. (For a list of apps that you can use for hosting virtual MFA devices, see Virtual MFA Applications at [https://aws.amazon.com/iam/details/mfa/#Virtual_MFA_Applications)](https://aws.amazon.com/iam/details/mfa/#Virtual_MFA_Applications)) If the virtual MFA application supports multiple accounts (multiple virtual MFA devices), choose the option to create a new account (a new virtual MFA device).
7. Determine whether the MFA app supports QR codes, and then do one of the following:

 - Use the app to scan the QR code. For example, you might choose the camera icon or choose an option similar to Scan code, and then use the device's camera to scan the code.
 - In the Manage MFA Device wizard, choose Show secret key for manual configuration, and then type the secret configuration key into your MFA application.

 When you are finished, the virtual MFA device starts generating one-time passwords.

8. In the `Manage MFA Device wizard`, in the `MFA Code 1 box`, type the `one-time password` that currently appears in the virtual MFA device. Wait up to 30 seconds for the device to generate a new one-time password. Then type the second `one-time password` into the `MFA Code 2 box`.

9. Click `Assign MFA`.
