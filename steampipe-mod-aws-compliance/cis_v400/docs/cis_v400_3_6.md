## Description

AWS Key Management Service (KMS) allows customers to rotate the backing key, which is key material stored within the KMS that is tied to the key ID of the customer-created customer master key (CMK). The backing key is used to perform cryptographic operations such as encryption and decryption. Automated key rotation currently retains all prior backing keys so that decryption of encrypted data can occur transparently. It is recommended that CMK key rotation be enabled for symmetric keys. Key rotation cannot be enabled for any asymmetric CMK.

Rotating encryption keys helps reduce the potential impact of a compromised key, as data encrypted with a new key cannot be accessed with a previous key that may have been exposed. Keys should be rotated every year or upon an event that could result in the compromise of that key.

## Remediation

### From Console

1. Sign in to the AWS Management Console and open the KMS console at: [https://console.aws.amazon.com/kms](https://console.aws.amazon.com/kms).
2. In the left navigation pane, click `Customer-managed keys`.
3. Select a key with `Key spec = SYMMETRIC_DEFAULT` that does not have automatic rotation enabled.
4. Select the `Key rotation` tab.
5. Check the `Automatically rotate this KMS key every year` box.
6. Click `Save`.
7. Repeat steps 3–6 for all customer-managed CMKs that do not have automatic rotation enabled.

### From Command Line

1. Run the following command to enable key rotation:

```bash
aws kms enable-key-rotation --key-id <kms-key-id>
```
