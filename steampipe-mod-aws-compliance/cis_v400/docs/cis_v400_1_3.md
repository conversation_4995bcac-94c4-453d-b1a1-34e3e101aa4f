## Description

The AWS support portal allows account owners to establish security questions that can be used to authenticate individuals calling AWS customer service for support. It is recommended that security questions be established.

When creating a new AWS account, a default super user is automatically created. This account is referred to as the 'root user' or 'root' account. It is recommended that the use of this account be limited and highly controlled. During events in which the 'root' password is no longer accessible or the MFA token associated with 'root' is lost/destroyed it is possible, through authentication using secret questions and associated answers, to recover 'root' user login access.

## Remediation

### From Console

1. <PERSON>gin to the AWS Account as the 'root' user.
2. Click on the _<Root\_Account\_Name>_ from the top right of the console.
3. From the drop-down menu Click _My Account_.
4. Scroll down to the `Configure Security Questions` section.
5. <PERSON>lick on `Edit`.
6. Click on each `Question`.
 - From the drop-down select an appropriate question
 - Click on the `Answer` section
 - Enter an appropriate answer
 - Follow process for all 3 questions
7. Click `Update` when complete.
8. Save Questions and Answers and place in a secure physical location.
