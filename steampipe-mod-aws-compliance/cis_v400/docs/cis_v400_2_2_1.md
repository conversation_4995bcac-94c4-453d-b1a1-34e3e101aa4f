## Description

Amazon RDS encrypted DB instances use the industry-standard AES-256 encryption algorithm to encrypt your data on the server that hosts your Amazon RDS DB instances. After your data is encrypted, Amazon RDS handles the authentication of access and the decryption of your data transparently, with minimal impact on performance.

Databases are likely to hold sensitive and critical data; therefore, it is highly recommended to implement encryption to protect your data from unauthorized access or disclosure. With RDS encryption enabled, the data stored on the instance's underlying storage, the automated backups, read replicas, and snapshots are all encrypted.

## Remediation

### From Console

1. Login to the AWS Management Console and open the RDS dashboard at [https://console.aws.amazon.com/rds/](https://console.aws.amazon.com/rds/).
2. In the left navigation panel, click on `Databases`.
3. Select the Database instance that needs to be encrypted.
4. Click the `Actions` button placed at the top right and select `Take Snapshot`.
5. On the Take Snapshot page, enter the name of the database for which you want to take a snapshot in the `Snapshot Name` field and click on `Take Snapshot`.
6. Select the newly created snapshot, click the `Action` button placed at the top right, and select `Copy snapshot` from the Action menu.
7. On the Make Copy of DB Snapshot page, perform the following:
- In the `New DB Snapshot Identifier` field, enter a name for the new snapshot.
- Check `Copy Tags`. The new snapshot must have the same tags as the source snapshot.
- Select `Yes` from the `Enable Encryption` dropdown list to enable encryption. You can choose to use the AWS default encryption key or a custom key from the Master Key dropdown list.
8. Click `Copy Snapshot` to create an encrypted copy of the selected instance's snapshot.
9. Select the new Snapshot Encrypted Copy and click the `Action` button located at the top right. Then, select the `Restore Snapshot` option from the Action menu. This will restore the encrypted snapshot to a new database instance.
10. On the Restore DB Instance page, enter a unique name for the new database instance in the DB Instance Identifier field.
11. Review the instance configuration details and click `Restore DB Instance`.
12. As the new instance provisioning process is completed, you can update the application configuration to refer to the endpoint of the new encrypted database instance. Once the database endpoint is changed at the application level, you can remove the unencrypted instance.

### From Command Line

1. Run the `describe-db-instances` command to list the names of all RDS database instances in the selected AWS region. The command output should return database instance identifiers:

```bash
aws rds describe-db-instances --region <region-name> --query 'DBInstances[*].DBInstanceIdentifier'
```

2. Run the `create-db-snapshot` command to create a snapshot for a selected database instance. The command output will return the `new snapshot` with name DB Snapshot Name:

```bash
aws rds create-db-snapshot --region <region-name> --db-snapshot-identifier <db-snapshot-name> --db-instance-identifier <db-name>
```

3. Now run the `list-aliases` command to list the KMS key aliases available in a specified region. The command output should return each `key alias currently available`. For our RDS encryption activation process, locate the ID of the AWS default KMS key:

```bash
aws kms list-aliases --region <region-name>
```

4. Run the `copy-db-snapshot` command using the default KMS key ID for the RDS instances returned earlier to create an encrypted copy of the database instance snapshot. The command output will return the `encrypted instance snapshot configuration`:

```bash
aws rds copy-db-snapshot --region <region-name> --source-db-snapshot-identifier <db-snapshot-name> --target-db-snapshot-identifier <db-snapshot-name-encrypted> --copy-tags --kms-key-id <kms-id-for-rds>
```

5. Run the `restore-db-instance-from-db-snapshot` command to restore the encrypted snapshot created in the previous step to a new database instance. If successful, the command output should return the configuration of the new encrypted database instance:

```bash
aws rds restore-db-instance-from-db-snapshot --region <region-name> --db-instance-identifier <db-name-encrypted> --db-snapshot-identifier <db-snapshot-name-encrypted>
```

6. Run the `describe-db-instances` command to list all RDS database names available in the selected AWS region. The output will return the database instance identifier names. Select the encrypted database name that we just created, `db-name-encrypted`:

```bash
aws rds describe-db-instances --region <region-name> --query 'DBInstances[*].DBInstanceIdentifier'
```

7. Run the `describe-db-instances` command again using the RDS instance identifier returned earlier to determine if the selected database instance is encrypted. The command output should indicate that the encryption status is `True`:

```bash
aws rds describe-db-instances --region <region-name> --db-instance-identifier <db-name-encrypted> --query 'DBInstances[*].StorageEncrypted'
```
