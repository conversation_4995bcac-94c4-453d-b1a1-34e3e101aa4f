## Description

EFS data should be encrypted at rest using AWS KMS (Key Management Service).

Data should be encrypted at rest to reduce the risk of a data breach via direct access to the storage device.

## Remediation

**It is important to note that EFS file system data-at-rest encryption must be turned on when creating the file system. If an EFS file system has been created without data-at-rest encryption enabled, then you must create another EFS file system with the correct configuration and transfer the data.**

**Steps to create an EFS file system with data encrypted at rest:**

### From Console

1. Login to the AWS Management Console and Navigate to the `Elastic File System (EFS)` dashboard.
2. Select `File Systems` from the left navigation panel.
3. Click the `Create File System` button from the dashboard top menu to start the file system setup process.
4. On the `Configure file system access` configuration page, perform the following actions:
- Choose an appropriate VPC from the VPC dropdown list.
- Within the `Create mount targets` section, check the boxes for all of the Availability Zones (AZs) within the selected VPC. These will be your mount targets.
- Click `Next step` to continue.
5. Perform the following on the `Configure optional settings` page:
- Create `tags` to describe your new file system.
- Choose `performance mode` based on your requirements.
- Check the `Enable encryption` box and choose `aws/elasticfilesystem` from the `Select KMS master key` dropdown list to enable encryption for the new file system, using the default master key provided and managed by AWS KMS.
- Click `Next step` to continue.
6. Review the file system configuration details on the `review and create` page and then click `Create File System` to create your new AWS EFS file system.
7. Copy the data from the old unencrypted EFS file system onto the newly created encrypted file system.
8. Remove the unencrypted file system as soon as your data migration to the newly created encrypted file system is completed.
9. Change the AWS region from the navigation bar and repeat the entire process for the other AWS regions.

### From CLI

1. Run the `describe-file-systems` command to view the configuration information for the selected unencrypted file system identified in the Audit steps:

```bash
aws efs describe-file-systems --region <region> --file-system-id <file-system-id>
```

2. The command output should return the configuration information.
3. To provision a new AWS EFS file system, you need to generate a universally unique identifier (UUID) to create the token required by the `create-file-system` command. To create the required token, you can use a randomly generated UUID from "https://www.uuidgenerator.net".
4. Run the `create-file-system` command using the unique token created at the previous step:

```bash
aws efs create-file-system --region <region> --creation-token <uuid> --performance-mode generalPurpose --encrypted
```

5. The command output should return the new file system configuration metadata.
6. Run the `create-mount-target` command using the EFS file system ID returned from step 4 as the identifier and the ID of the Availability Zone (AZ) that will represent the mount target:

```bash
aws efs create-mount-target --region <region> --file-system-id <file-system-id> --subnet-id <subnet-id>
```

7. The command output should return the new mount target metadata.
8. Now you can mount your file system from an EC2 instance.
9. Copy the data from the old unencrypted EFS file system to the newly created encrypted file system.
10. Remove the unencrypted file system as soon as your data migration to the newly created encrypted file system is completed:

```bash
aws efs delete-file-system --region <region> --file-system-id <unencrypted-file-system-id>
```

11. Change the AWS region by updating the --region and repeat the entire process for the other AWS regions.

### Default Value

EFS file system data is encrypted at rest by default when creating a file system through the Console. However, encryption at rest is not enabled by default when creating a new file system using the AWS CLI, API, or SDKs.
