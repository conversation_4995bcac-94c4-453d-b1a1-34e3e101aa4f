## Description

AWS CloudTrail is a web service that records AWS API calls for your account and delivers log files to you. The recorded information includes the identity of the API caller, the time of the API call, the source IP address of the API caller, the request parameters, and the response elements returned by the AWS service. CloudTrail provides a history of AWS API calls for an account, including API calls made via the Management Console, SDKs, command line tools, and higher-level AWS services (such as CloudFormation).

The AWS API call history produced by CloudTrail enables security analysis, resource change tracking, and compliance auditing. Additionally,

- ensuring that a multi-region trail exists will help detect unexpected activity occurring in otherwise unused regions

- ensuring that a multi-region trail exists will ensure that `Global Service Logging` is enabled for a trail by default to capture recordings of events generated on AWS global services

- for a multi-region trail, ensuring that management events are configured for all types of Read/Writes ensures the recording of management operations that are performed on all resources in an AWS account

## Remediation

Perform the following to enable global (Multi-region) CloudTrail logging:

### From Console

1. Sign in to the AWS Management Console and open the IAM console at [https://console.aws.amazon.com/cloudtrail](https://console.aws.amazon.com/cloudtrail).
2. Click on `Trails` in the left navigation pane.
3. Click `Get Started Now` if it is presented, then:
 - Click `Add new trail`.
 - Enter a trail name in the `Trail name` box.
 - A trail created in the console is a multi-region trail by default.
 - Specify an S3 bucket name in the `S3 bucket` box.
 - Specify the AWS KMS alias under the `Log file SSE-KMS encryption` section, or create a new key.
 - Click `Next`.
4. Ensure the `Management events` check box is selected.
5. Ensure both `Read` and `Write` are checked under API activity.
6. Click `Next`.
7. Review your trail settings and click `Create trail`.

### From Command Line

Create a multi-region trail:

```bash
aws cloudtrail create-trail --name <trail-name> --bucket-name <s3-bucket-for-cloudtrail> --is-multi-region-trail
```

Enable multi-region on an existing trail:

```bash
aws cloudtrail update-trail --name <trail-name> --is-multi-region-trail
```

**Note:** Creating a CloudTrail trail via the CLI without providing any overriding options configures all `read` and `write` `Management Events` to be logged by default.

### Default Value

Not Enabled.
