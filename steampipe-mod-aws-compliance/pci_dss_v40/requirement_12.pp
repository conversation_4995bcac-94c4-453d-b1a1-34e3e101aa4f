locals {
  pci_dss_v40_requirement_12_common_tags = merge(local.pci_dss_v40_common_tags, {
    control_set = "12"
  })
}

benchmark "pci_dss_v40_requirement_12" {
  title       = "Requirement 12: Support Information Security with Organizational Policies and Programs"
  description = "The organization's overall information security policy sets the tone for the whole entity and informs personnel what is expected of them. All personnel should be aware of the sensitivity of cardholder data and their responsibilities for protecting it."
  children = [
    benchmark.pci_dss_v40_requirement_12_10
  ]

  tags = local.pci_dss_v40_requirement_12_common_tags
}

benchmark "pci_dss_v40_requirement_12_10" {
  title       = "12.10: Implement an Incident Response Plan"

  children = [
    benchmark.pci_dss_v40_requirement_12_10_5
  ]

  tags = merge(local.pci_dss_v40_requirement_12_common_tags, {
    pci_dss_v40_item_id = "12.10"
  })
}

benchmark "pci_dss_v40_requirement_12_10_5" {
  title       = "12.10.5: The security incident response plan includes monitoring and responding to alerts from security monitoring systems"
  description = "Responding to alerts generated by security monitoring systems that are explicitly designed to focus on potential risk to data is critical to prevent a breach and therefore, this must be included in the incident-response processes."

  children = [
    control.cloudformation_stack_notifications_enabled,
    control.cloudtrail_trail_integrated_with_logs,
    control.cloudwatch_alarm_action_enabled,
    control.log_metric_filter_console_authentication_failure,
    control.log_metric_filter_console_login_mfa,
    control.log_metric_filter_disable_or_delete_cmk,
    control.log_metric_filter_root_login,
    control.s3_bucket_event_notifications_enabled,
    control.sns_topic_notification_delivery_status_enabled
  ]

  tags = merge(local.pci_dss_v40_requirement_12_common_tags, {
    pci_dss_v40_item_id = "12.10.5"
  })
} 