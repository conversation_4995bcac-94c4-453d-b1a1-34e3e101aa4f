## Description

When enabling the Metadata Service on AWS EC2 instances, users have the option of using either Instance Metadata Service Version 1 (IMDSv1; a request/response method) or Instance Metadata Service Version 2 (IMDSv2; a session-oriented method).

Instance metadata is data about your instance that you can use to configure or manage the running instance. Instance metadata is divided into [categories](https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/ec2-instance-metadata.html), such as host name, events, and security groups.

When enabling the Metadata Service on AWS EC2 instances, users have the option of using either Instance Metadata Service Version 1 (IMDSv1; a request/response method) or Instance Metadata Service Version 2 (IMDSv2; a session-oriented method). With IMDSv2, every request is now protected by session authentication. A session begins and ends a series of requests that software running on an EC2 instance uses to access the locally stored EC2 instance metadata and credentials.

Allowing Version 1 of the service may open EC2 instances to Server-Side Request Forgery (SSRF) attacks, so Amazon recommends utilizing Version 2 for better instance security.

## Remediation

From Console:

1. Sign in to the AWS Management Console and navigate to the EC2 dashboard at [https://console.aws.amazon.com/ec2/](https://console.aws.amazon.com/ec2/).
2. In the left navigation panel, under the `INSTANCES` section, choose `Instances`.
3. Select the EC2 instance that you want to examine.
4. Choose `Actions > Instance Settings > Modify instance metadata options`.
5. Set `Instance metadata service` to `Enable`.
6. Set `IMDSv2` to `Required`.
7. Repeat steps 1-6 to perform the remediation process for other EC2 instances in all applicable AWS region(s).

### From Command Line:

1. Run the `describe-instances` command, applying the appropriate filters to list the IDs of all existing EC2 instances currently available in the selected region:

```bash
aws ec2 describe-instances --region <region-name> --output table --query "Reservations[*].Instances[*].InstanceId"
```

2. The command output should return a table with the requested instance IDs.
3. Run the `modify-instance-metadata-options` command with an instance ID obtained from the previous step to update the Instance Metadata Version:

```bash
aws ec2 modify-instance-metadata-options --instance-id <instance-id> --http-tokens required --region <region-name>
```

4. Repeat steps 1-3 to perform the remediation process for other EC2 instances in the same AWS region.
5. Change the region by updating `--region` and repeat the process for other regions.
