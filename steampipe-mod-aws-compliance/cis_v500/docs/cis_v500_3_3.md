## Description

AWS Config is a web service that performs configuration management of supported AWS resources within your account and delivers log files to you. The recorded information includes the configuration items (AWS resources), relationships between configuration items (AWS resources), and any configuration changes between resources. It is recommended that AWS Config be enabled in all regions.

The AWS configuration item history captured by AWS Config enables security analysis, resource change tracking, and compliance auditing.

## Remediation

To implement AWS Config configuration:

### From Console

1. Select the region you want to focus on in the top right of the console.
2. Click `Services`.
3. Click `Config`.
4. If a Config Recorder is enabled in this region, navigate to the Settings page from the navigation menu on the left-hand side. If a Config Recorder is not yet enabled in this region, select "Get Started".
5. Select "Record all resources supported in this region".
6. Choose to include global resources (IAM resources).
7. Specify an S3 bucket in the same account or in another managed AWS account.
8. Create an SNS Topic from the same AWS account or another managed AWS account.

### From Command Line

1. Ensure there is an appropriate S3 bucket, SNS topic, and IAM role per the [AWS Config Service prerequisites](http://docs.aws.amazon.com/config/latest/developerguide/gs-cli-prereq.html).
2. Run this command to create a new configuration recorder:

```bash
aws configservice put-configuration-recorder --configuration-recorder name=<config-recorder-name>,roleARN=arn:aws:iam::<account-id>:role/<iam-role> --recording-group allSupported=true,includeGlobalResourceTypes=true
```

3. Create a delivery channel configuration file locally which specifies the channel attributes, populated from the prerequisites set up previously:

```json
{
  "name": "<delivery-channel-name>",
  "s3BucketName": "<bucket-name>",
  "snsTopicARN": "arn:aws:sns:<region>:<account-id>:<sns-topic>",
  "configSnapshotDeliveryProperties": {
    "deliveryFrequency": "Twelve_Hours"
  }
}
```

4. Run this command to create a new delivery channel, referencing the json configuration file made in the previous step:

```bash
aws configservice put-delivery-channel --delivery-channel file://<delivery-channel-file>.json
```

5. Start the configuration recorder by running the following command:

```bash
aws configservice start-configuration-recorder --configuration-recorder-name <config-recorder-name>
```
