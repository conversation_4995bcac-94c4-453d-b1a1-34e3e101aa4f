#!/bin/bash

# Deploy All Services Script
# Usage: ./deploy-all-services.sh [image-tag] [service-name]
# If no tag is provided, uses 'latest'
# If service-name is provided, only deploys that service

set -e

# Configuration
PROJECT_ID="vratant-test-prj"
REGION="us-central1"
CUSTOMER_NAME="ironfort"
TAG="${1:-latest}"
SPECIFIC_SERVICE="${2:-}"

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Deploying All Services for ${CUSTOMER_NAME} ===${NC}"
echo "Project: $PROJECT_ID"
echo "Region: $REGION"
echo "Image Tag: $TAG"
echo ""

# Always use Container Registry (where Cloud Build pushes images)
REGISTRY="gcr.io/$PROJECT_ID"
echo "Using Container Registry: $REGISTRY"

# Service account setup
SA_NAME="${CUSTOMER_NAME}-compliance-sa"
SA_EMAIL="${SA_NAME}@${PROJECT_ID}.iam.gserviceaccount.com"

echo -e "\n${BLUE}Setting up service account...${NC}"
gcloud iam service-accounts create ${SA_NAME} \
  --display-name="${CUSTOMER_NAME} Compliance Service Account" \
  --project=${PROJECT_ID} 2>/dev/null || echo "Service account already exists"

# Grant permissions
echo "Granting permissions..."
# Grant Secret Manager Admin (needed to create/manage secrets)
gcloud projects add-iam-policy-binding ${PROJECT_ID} \
  --member="serviceAccount:${SA_EMAIL}" \
  --role="roles/secretmanager.admin" \
  --condition=None --quiet 2>/dev/null || true

# Also keep secretAccessor for backward compatibility
gcloud projects add-iam-policy-binding ${PROJECT_ID} \
  --member="serviceAccount:${SA_EMAIL}" \
  --role="roles/secretmanager.secretAccessor" \
  --condition=None --quiet 2>/dev/null || true

gcloud projects add-iam-policy-binding ${PROJECT_ID} \
  --member="serviceAccount:${SA_EMAIL}" \
  --role="roles/logging.logWriter" \
  --condition=None --quiet 2>/dev/null || true

# Function to deploy a service with health check
deploy_service_with_health() {
    local service_name=$1
    local image_name=$2
    local port=$3
    local memory=$4
    local cpu=$5
    local extra_args=$6
    
    echo -e "\n${BLUE}Deploying ${service_name}...${NC}"
    
    gcloud run deploy ${CUSTOMER_NAME}-${service_name} \
      --image=${REGISTRY}/${image_name}:${TAG} \
      --region=${REGION} \
      --platform=managed \
      --allow-unauthenticated \
      --memory=${memory} \
      --cpu=${cpu} \
      --timeout=3600 \
      --port=${port} \
      --service-account=${SA_EMAIL} \
      --min-instances=1 \
      --max-instances=10 \
      --set-env-vars="AUTH_TYPE=secret_manager,CLIENT_NAME=${CUSTOMER_NAME},GCP_PROJECT_ID=${PROJECT_ID},STEAMPIPE_UPDATE_CHECK=false,POWERPIPE_UPDATE_CHECK=false" \
      ${extra_args}
    
    # Get service URL
    local url=$(gcloud run services describe ${CUSTOMER_NAME}-${service_name} --region=${REGION} --format='value(status.url)')
    echo -e "${GREEN}✓ ${service_name} deployed: ${url}${NC}"
    
    # Test health endpoint
    echo "Testing health endpoint..."
    local health_status=$(curl -s -o /dev/null -w "%{http_code}" ${url}/health || echo "000")
    if [ "$health_status" == "200" ]; then
        echo -e "${GREEN}✓ Health check passed${NC}"
    else
        echo -e "${RED}✗ Health check failed (HTTP ${health_status})${NC}"
    fi
    
    return 0
}

# Function to deploy UI services (no health check)
deploy_ui_service() {
    local service_name=$1
    local image_name=$2
    local port=$3
    local memory=$4
    local cpu=$5
    local env_vars=$6
    
    echo -e "\n${BLUE}Deploying ${service_name}...${NC}"
    
    gcloud run deploy ${CUSTOMER_NAME}-${service_name} \
      --image=${REGISTRY}/${image_name}:${TAG} \
      --region=${REGION} \
      --platform=managed \
      --allow-unauthenticated \
      --memory=${memory} \
      --cpu=${cpu} \
      --timeout=300 \
      --port=${port} \
      --min-instances=1 \
      --max-instances=5 \
      --set-env-vars="${env_vars}"
    
    # Get service URL
    local url=$(gcloud run services describe ${CUSTOMER_NAME}-${service_name} --region=${REGION} --format='value(status.url)')
    echo -e "${GREEN}✓ ${service_name} deployed: ${url}${NC}"
    
    return 0
}

# Check if we're deploying a specific service
if [ -n "$SPECIFIC_SERVICE" ]; then
    echo -e "${BLUE}Deploying only ${SPECIFIC_SERVICE}...${NC}"
    
    case "$SPECIFIC_SERVICE" in
        "admin-ui")
            # Get URLs for admin-ui deployment
            ADMIN_API_URL=$(gcloud run services describe ${CUSTOMER_NAME}-admin-api --region=${REGION} --format='value(status.url)')
            GCP_URL=$(gcloud run services describe ${CUSTOMER_NAME}-gcp-compliance --region=${REGION} --format='value(status.url)')
            AWS_URL=$(gcloud run services describe ${CUSTOMER_NAME}-aws-compliance --region=${REGION} --format='value(status.url)')
            AZURE_URL=$(gcloud run services describe ${CUSTOMER_NAME}-azure-compliance --region=${REGION} --format='value(status.url)')
            
            deploy_ui_service "admin-ui" "admin-ui" "8081" "512Mi" "1" "REACT_APP_API_BASE_URL=${ADMIN_API_URL},BACKEND_GCP_URL=${GCP_URL},BACKEND_AWS_URL=${AWS_URL},BACKEND_AZURE_URL=${AZURE_URL}"
            
            ADMIN_UI_URL=$(gcloud run services describe ${CUSTOMER_NAME}-admin-ui --region=${REGION} --format='value(status.url)')
            echo -e "\n${GREEN}Admin UI deployed: ${ADMIN_UI_URL}${NC}"
            exit 0
            ;;
        *)
            echo -e "${RED}Unknown service: ${SPECIFIC_SERVICE}${NC}"
            exit 1
            ;;
    esac
fi

# Deploy Admin API (2GB memory for handling multiple operations)
deploy_service_with_health "admin-api" "admin-api" "8084" "2Gi" "1"

# Get Admin API URL for UI services
ADMIN_API_URL=$(gcloud run services describe ${CUSTOMER_NAME}-admin-api --region=${REGION} --format='value(status.url)')

# Deploy compliance services (8GB memory for Steampipe/Powerpipe operations)
deploy_service_with_health "gcp-compliance" "steampipe-gcp-compliance" "8080" "8Gi" "2"
deploy_service_with_health "aws-compliance" "steampipe-aws-compliance" "8082" "8Gi" "2"
deploy_service_with_health "azure-compliance" "steampipe-azure-compliance" "8083" "8Gi" "2"

# Get compliance service URLs
GCP_URL=$(gcloud run services describe ${CUSTOMER_NAME}-gcp-compliance --region=${REGION} --format='value(status.url)')
AWS_URL=$(gcloud run services describe ${CUSTOMER_NAME}-aws-compliance --region=${REGION} --format='value(status.url)')
AZURE_URL=$(gcloud run services describe ${CUSTOMER_NAME}-azure-compliance --region=${REGION} --format='value(status.url)')

# Deploy Admin UI (with Admin API URL and Backend URLs)
deploy_ui_service "admin-ui" "admin-ui" "8081" "512Mi" "1" "REACT_APP_API_BASE_URL=${ADMIN_API_URL},BACKEND_GCP_URL=${GCP_URL},BACKEND_AWS_URL=${AWS_URL},BACKEND_AZURE_URL=${AZURE_URL}"
ADMIN_UI_URL=$(gcloud run services describe ${CUSTOMER_NAME}-admin-ui --region=${REGION} --format='value(status.url)')

# Deploy API Gateway with correct environment variables
echo -e "\n${BLUE}Deploying api-gateway...${NC}"
gcloud run deploy ${CUSTOMER_NAME}-api-gateway \
  --image=${REGISTRY}/api-gateway:${TAG} \
  --region=${REGION} \
  --platform=managed \
  --allow-unauthenticated \
  --memory=512Mi \
  --cpu=1 \
  --timeout=300 \
  --port=8081 \
  --min-instances=1 \
  --max-instances=5 \
  --set-env-vars="GCP_BACKEND_URL=${GCP_URL},AWS_BACKEND_URL=${AWS_URL},AZURE_BACKEND_URL=${AZURE_URL},ADMIN_BACKEND_URL=${ADMIN_API_URL}"

API_GATEWAY_URL=$(gcloud run services describe ${CUSTOMER_NAME}-api-gateway --region=${REGION} --format='value(status.url)')
echo -e "${GREEN}✓ api-gateway deployed: ${API_GATEWAY_URL}${NC}"

# Test API Gateway health
echo "Testing API Gateway health endpoint..."
API_HEALTH=$(curl -s -o /dev/null -w "%{http_code}" ${API_GATEWAY_URL}/health || echo "000")
if [ "$API_HEALTH" == "200" ]; then
    echo -e "${GREEN}✓ API Gateway health check passed${NC}"
else
    echo -e "${RED}✗ API Gateway health check failed (HTTP ${API_HEALTH})${NC}"
fi

# Deploy Dashboard UI
deploy_ui_service "dashboard-ui" "dashboard-ui" "8081" "512Mi" "1" "BACKEND_GCP_URL=${GCP_URL},BACKEND_AWS_URL=${AWS_URL},BACKEND_AZURE_URL=${AZURE_URL}"

DASHBOARD_URL=$(gcloud run services describe ${CUSTOMER_NAME}-dashboard-ui --region=${REGION} --format='value(status.url)')

# Summary
echo -e "\n${GREEN}=== Deployment Complete! ===${NC}"
echo -e "\nService URLs:"
echo -e "  Admin UI:     ${ADMIN_UI_URL}"
echo -e "  Dashboard UI: ${DASHBOARD_URL}"
echo -e "  API Gateway:  ${API_GATEWAY_URL}"
echo -e "\nCompliance Services:"
echo -e "  GCP:   ${GCP_URL}"
echo -e "  AWS:   ${AWS_URL}"
echo -e "  Azure: ${AZURE_URL}"
echo -e "\nAdmin API: ${ADMIN_API_URL}"

# Verify all services are using Container Registry
echo -e "\n${BLUE}Verifying deployment...${NC}"
REGISTRY_CHECK=$(gcloud run services list --region=${REGION} --format="table(name,spec.template.spec.containers[0].image)" | grep ${CUSTOMER_NAME} | grep -v "gcr.io" || true)
if [ -z "$REGISTRY_CHECK" ]; then
    echo -e "${GREEN}✓ All services using Container Registry${NC}"
else
    echo -e "${RED}✗ Some services not using Container Registry:${NC}"
    echo "$REGISTRY_CHECK"
fi

echo -e "\n${BLUE}Next Steps:${NC}"
echo "1. Access Admin UI to add customer credentials: ${ADMIN_UI_URL}"
echo "2. Access Dashboard UI to run compliance scans: ${DASHBOARD_URL}"
echo "3. Use API Gateway for programmatic access: ${API_GATEWAY_URL}"

# Save URLs to file
cat > ${CUSTOMER_NAME}-service-urls.txt << EOF
Deployment URLs for ${CUSTOMER_NAME} (Tag: ${TAG})
Generated: $(date)

Admin UI:     ${ADMIN_UI_URL}
Dashboard UI: ${DASHBOARD_URL}
API Gateway:  ${API_GATEWAY_URL}

Compliance Services:
  GCP:   ${GCP_URL}
  AWS:   ${AWS_URL}
  Azure: ${AZURE_URL}

Admin API: ${ADMIN_API_URL}

All services deployed from: ${REGISTRY}
EOF

echo -e "\nService URLs saved to: ${CUSTOMER_NAME}-service-urls.txt"