# Steampipe Compliance Dashboard - ADC Version

## Overview

This is the new ADC (Application Default Credentials) version of the Steampipe Compliance Dashboard. It has been completely rewritten to:

1. **Use ADC instead of customer IDs** - No more customer management needed
2. **Fix the benchmark_results/groups error** - <PERSON><PERSON><PERSON> handles API response formats
3. **Provide a clean React-based UI** - Modern, responsive interface
4. **Support all three cloud providers** - GCP, AWS, and Azure

## Key Changes

### 1. Authentication
- **OLD**: Required customer IDs and secret references
- **NEW**: Uses Application Default Credentials (ADC) automatically

### 2. API Calls
- **OLD**: Required `customer_id` in requests
- **NEW**: No customer ID needed, uses ADC context

### 3. Response Handling
- **OLD**: Expected specific format that caused "missing benchmark_results or groups" error
- **NEW**: Handles multiple response formats gracefully

## File Locations

- **New Dashboard**: `custom/dashboard/steampipe-secops-dashboard/frontend/dashboard.html`
- **Old Dashboard**: `simple-dashboard.html` (deprecated)

## Running Locally

1. **Start the backend services**:
   ```bash
   # GCP service on port 8080
   # AWS service on port 8082  
   # Azure service on port 8083
   ```

2. **Open the dashboard**:
   ```bash
   # Option 1: Direct file access
   open custom/dashboard/steampipe-secops-dashboard/frontend/dashboard.html
   
   # Option 2: Serve with Python
   ./test-new-dashboard.sh
   # Then open http://localhost:8000/dashboard.html
   ```

## Deployment

### For Cloud Run

1. **Update the Dockerfile** to serve the new dashboard:
   ```dockerfile
   COPY custom/dashboard/steampipe-secops-dashboard/frontend/dashboard.html /usr/share/nginx/html/index.html
   ```

2. **Configure nginx** to serve the dashboard and proxy API calls

3. **Set proper CORS headers** in the backend services

### Environment Variables

The dashboard automatically detects the environment:
- **Local**: Uses localhost:808X endpoints
- **Production**: Uses subdomain-based endpoints (e.g., gcp-yourdomain.com)

## Features

### 1. Service Status Indicators
- Green: Service is online
- Red: Service is offline
- Orange: Checking status

### 2. Benchmark Execution
- Click "Run" to start any benchmark
- No authentication configuration needed
- Automatic job tracking

### 3. Job History
- View recent jobs for each provider
- Click "View Results" to see detailed output
- Auto-refresh every 30 seconds

### 4. Results Viewer
- Modal popup for viewing job results
- Pretty-printed JSON output
- Click outside to close

## Troubleshooting

### "Service Offline" Status
- Ensure backend services are running
- Check correct ports (8080, 8082, 8083)
- Verify ADC is configured:
  - GCP: `gcloud auth application-default login`
  - AWS: AWS CLI configured or IAM role
  - Azure: `az login` or managed identity

### Benchmark Execution Fails
- Check service logs for authentication errors
- Ensure ADC has proper permissions
- Verify Steampipe plugins are installed

### No Results Showing
- Jobs may still be running (check status)
- Click "Refresh" to update job list
- Check backend logs for errors

## Migration from Old Dashboard

If you were using the old dashboard with customer IDs:

1. **No data migration needed** - ADC uses current auth context
2. **Update any automation** - Remove customer_id parameters
3. **Update documentation** - Reference this guide

## Security Benefits

1. **No secrets in dashboard** - ADC handles authentication
2. **No customer management** - Simplified security model
3. **Automatic credential rotation** - When using managed identities

## Next Steps

1. Test the dashboard with your ADC setup
2. Deploy to your environment
3. Remove old customer management code
4. Update team documentation

---

**Note**: This dashboard requires the backend services to be configured for ADC. Ensure all services are updated to support ADC before using this dashboard.