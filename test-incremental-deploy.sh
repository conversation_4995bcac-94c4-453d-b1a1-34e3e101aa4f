#!/bin/bash
# Test Cloud Build deployment incrementally
# This script tests each phase of the deployment process

set -e

PROJECT_ID=${PROJECT_ID:-"vratant-test-prj"}
REGION=${REGION:-"us-central1"}
DOMAIN=${DOMAIN:-"steampipe-compliance.com"}

echo "🚀 Testing Cloud Run deployment incrementally..."
echo "Project: $PROJECT_ID"
echo "Region: $REGION"
echo "Domain: $DOMAIN"
echo ""

# Function to prompt for continuation
confirm() {
    read -p "Continue with $1? (y/n) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Aborted."
        exit 1
    fi
}

# Phase 1: Test base image build only
echo "🏗️  PHASE 1: Building base image only..."
confirm "base image build"

gcloud builds submit \
    --config=cloudbuild-deploy.yaml \
    --substitutions=_DOMAIN=$DOMAIN,_REGION=$REGION,SHORT_SHA=test-$(date +%s) \
    --timeout=30m \
    --tag-filter="build-base,test-base,push-base" \
    . || {
    echo "❌ Base image build failed"
    exit 1
}

echo "✅ Base image built and pushed successfully"
echo ""

# Phase 2: Build all images without deploying
echo "🏗️  PHASE 2: Building all service images..."
confirm "building all images"

# Create a temporary build-only config
cat > cloudbuild-build-only.yaml <<EOF
steps:
  # Build base image
  - name: 'gcr.io/cloud-builders/docker'
    id: 'pull-base'
    args: ['pull', 'gcr.io/$PROJECT_ID/steampipe-base:latest']
  
  # Build all services in parallel
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-admin-api'
    args:
      - 'build'
      - '-t'
      - 'gcr.io/$PROJECT_ID/admin-api:build-test'
      - '-f'
      - 'custom/services/admin/Dockerfile'
      - '.'
    waitFor: ['-']

  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-gcp'
    args:
      - 'build'
      - '-t'
      - 'gcr.io/$PROJECT_ID/steampipe-gcp-compliance:build-test'
      - '-f'
      - 'custom/services/gcp/Dockerfile'
      - '.'
    waitFor: ['pull-base']

  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-aws'
    args:
      - 'build'
      - '-t'
      - 'gcr.io/$PROJECT_ID/steampipe-aws-compliance:build-test'
      - '-f'
      - 'custom/services/aws/Dockerfile'
      - '.'
    waitFor: ['pull-base']

  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-azure'
    args:
      - 'build'
      - '-t'
      - 'gcr.io/$PROJECT_ID/steampipe-azure-compliance:build-test'
      - '-f'
      - 'custom/services/azure/Dockerfile'
      - '.'
    waitFor: ['pull-base']

  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-gateway'
    args:
      - 'build'
      - '-t'
      - 'gcr.io/$PROJECT_ID/api-gateway:build-test'
      - '-f'
      - 'api-gateway/Dockerfile'
      - 'api-gateway/'
    waitFor: ['-']

  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-dashboard'
    args:
      - 'build'
      - '-t'
      - 'gcr.io/$PROJECT_ID/dashboard-ui:build-test'
      - '-f'
      - 'custom/dashboard/steampipe-secops-dashboard/frontend/Dockerfile'
      - 'custom/dashboard/steampipe-secops-dashboard/frontend/'
    waitFor: ['-']

  # Summary
  - name: 'gcr.io/cloud-builders/gcloud'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        echo "✅ All images built successfully!"
        echo "Images:"
        docker images | grep build-test
    waitFor: ['build-admin-api', 'build-gcp', 'build-aws', 'build-azure', 'build-gateway', 'build-dashboard']

timeout: 1800s
options:
  machineType: 'E2_HIGHCPU_8'
EOF

gcloud builds submit \
    --config=cloudbuild-build-only.yaml \
    . || {
    echo "❌ Service image builds failed"
    exit 1
}

echo "✅ All service images built successfully"
echo ""

# Phase 3: Deploy one backend service
echo "🚀 PHASE 3: Deploying single backend service (GCP)..."
confirm "deploying GCP compliance service"

gcloud run deploy steampipe-gcp-compliance-test \
    --image=gcr.io/$PROJECT_ID/steampipe-gcp-compliance:build-test \
    --region=$REGION \
    --platform=managed \
    --no-allow-unauthenticated \
    --memory=4Gi \
    --cpu=2 \
    --port=8080 \
    --set-env-vars="AUTH_TYPE=adc,PROJECT_ID=$PROJECT_ID,STEAMPIPE_UPDATE_CHECK=false,POWERPIPE_UPDATE_CHECK=false" \
    --min-instances=0 \
    --max-instances=1 \
    --timeout=300s || {
    echo "❌ GCP service deployment failed"
    exit 1
}

# Test the deployed service
echo "Testing GCP service health..."
GCP_URL=$(gcloud run services describe steampipe-gcp-compliance-test --region=$REGION --format='value(status.url)')
TOKEN=$(gcloud auth print-identity-token)

curl -H "Authorization: Bearer $TOKEN" $GCP_URL/health || {
    echo "⚠️  Service not responding yet, checking logs..."
    gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=steampipe-gcp-compliance-test" --limit=50 --format=json
}

echo ""
echo "✅ Test deployment successful!"
echo ""
echo "📋 Next steps for full deployment:"
echo "1. Fix any issues found during testing"
echo "2. Run full deployment: gcloud builds submit --config=cloudbuild-deploy.yaml --substitutions=_DOMAIN=$DOMAIN,_REGION=$REGION"
echo ""
echo "🧹 Cleanup test service:"
echo "gcloud run services delete steampipe-gcp-compliance-test --region=$REGION"

# Cleanup
rm -f cloudbuild-build-only.yaml