# Cloud Run Deployment Guide for Steampipe Compliance Platform

This guide provides step-by-step instructions for deploying the Steampipe Compliance Platform to Google Cloud Run.

## Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    PUBLIC ENDPOINTS                          │
├─────────────────────┬──────────────────┬────────────────────┤
│   Dashboard UI      │   API Gateway    │    Admin API       │
│  (React App)        │   (NGINX)        │   (Flask App)      │
├─────────────────────┴──────────────────┴────────────────────┤
│                   BACKEND SERVICES                           │
├─────────────────┬──────────────────┬───────────────────────┤
│  GCP Compliance │  AWS Compliance  │  Azure Compliance     │
│   (Internal)    │   (Internal)     │    (Internal)         │
└─────────────────┴──────────────────┴───────────────────────┘
```

## Prerequisites

1. **Google Cloud Project** with billing enabled
2. **gcloud CLI** installed and configured
3. **Docker** installed locally (for testing)
4. **GitHub repository** or Cloud Source Repository
5. **Service Account** with appropriate permissions

## Phase 1: Environment Setup

### 1.1 Enable Required APIs

```bash
# Run the setup script
./setup-cloudrun-deployment.sh
```

Or manually:

```bash
gcloud services enable \
  cloudbuild.googleapis.com \
  run.googleapis.com \
  secretmanager.googleapis.com \
  containerregistry.googleapis.com
```

### 1.2 Create Service Account

```bash
# Create service account
gcloud iam service-accounts create steampipe-sa \
  --display-name="Steampipe Service Account"

# Grant permissions
gcloud projects add-iam-policy-binding $PROJECT_ID \
  --member="serviceAccount:steampipe-sa@$PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/secretmanager.secretAccessor"
```

### 1.3 Create Secrets in Secret Manager

```bash
# GCP Service Account Key
gcloud secrets create steampipe-gcp-sa-key \
  --data-file=path/to/gcp-service-account.json

# AWS Credentials
gcloud secrets create steampipe-aws-credentials \
  --data-file=path/to/aws-credentials

# Azure Credentials  
gcloud secrets create steampipe-azure-credentials \
  --data-file=path/to/azure-credentials

# Customer-specific credentials (optional)
gcloud secrets create customer-ironfort-credentials \
  --data-file=path/to/customer-credentials.json
```

## Phase 2: Build and Push Images

### 2.1 Test Build Locally

```bash
# Test the Cloud Build configuration
./test-cloudbuild-local.sh

# Test incremental deployment
./test-incremental-deploy.sh
```

### 2.2 Run Full Build

```bash
# Build and push all images
gcloud builds submit \
  --config=cloudbuild-deploy.yaml \
  --substitutions=_DOMAIN=your-domain.com,_REGION=us-central1
```

## Phase 3: Deploy Services

The Cloud Build configuration automatically deploys all services. To deploy manually:

### 3.1 Deploy Backend Services (Internal)

```bash
# Deploy GCP Compliance
gcloud run deploy steampipe-gcp-compliance \
  --image=gcr.io/$PROJECT_ID/steampipe-gcp-compliance:latest \
  --region=us-central1 \
  --no-allow-unauthenticated \
  --memory=8Gi \
  --cpu=4 \
  --port=8080 \
  --set-env-vars="AUTH_TYPE=secret_manager,PROJECT_ID=$PROJECT_ID" \
  --service-account=steampipe-sa@$PROJECT_ID.iam.gserviceaccount.com

# Deploy AWS Compliance
gcloud run deploy steampipe-aws-compliance \
  --image=gcr.io/$PROJECT_ID/steampipe-aws-compliance:latest \
  --region=us-central1 \
  --no-allow-unauthenticated \
  --memory=8Gi \
  --cpu=4 \
  --port=8082 \
  --set-env-vars="AUTH_TYPE=secret_manager,PROJECT_ID=$PROJECT_ID" \
  --service-account=steampipe-sa@$PROJECT_ID.iam.gserviceaccount.com

# Deploy Azure Compliance
gcloud run deploy steampipe-azure-compliance \
  --image=gcr.io/$PROJECT_ID/steampipe-azure-compliance:latest \
  --region=us-central1 \
  --no-allow-unauthenticated \
  --memory=8Gi \
  --cpu=4 \
  --port=8083 \
  --set-env-vars="AUTH_TYPE=secret_manager,PROJECT_ID=$PROJECT_ID" \
  --service-account=steampipe-sa@$PROJECT_ID.iam.gserviceaccount.com
```

### 3.2 Deploy Public Services

```bash
# Get backend URLs
GCP_URL=$(gcloud run services describe steampipe-gcp-compliance --region=us-central1 --format='value(status.url)')
AWS_URL=$(gcloud run services describe steampipe-aws-compliance --region=us-central1 --format='value(status.url)')
AZURE_URL=$(gcloud run services describe steampipe-azure-compliance --region=us-central1 --format='value(status.url)')

# Deploy API Gateway
gcloud run deploy api-gateway \
  --image=gcr.io/$PROJECT_ID/api-gateway:latest \
  --region=us-central1 \
  --allow-unauthenticated \
  --memory=2Gi \
  --cpu=2 \
  --port=8081 \
  --set-env-vars="GCP_BACKEND_URL=$GCP_URL,AWS_BACKEND_URL=$AWS_URL,AZURE_BACKEND_URL=$AZURE_URL"

# Deploy Admin API
gcloud run deploy admin-api \
  --image=gcr.io/$PROJECT_ID/admin-api:latest \
  --region=us-central1 \
  --allow-unauthenticated \
  --memory=2Gi \
  --cpu=2 \
  --port=8085 \
  --set-env-vars="PROJECT_ID=$PROJECT_ID"

# Deploy Dashboard UI
GATEWAY_URL=$(gcloud run services describe api-gateway --region=us-central1 --format='value(status.url)')

gcloud run deploy dashboard-ui \
  --image=gcr.io/$PROJECT_ID/dashboard-ui:latest \
  --region=us-central1 \
  --allow-unauthenticated \
  --memory=1Gi \
  --cpu=1 \
  --port=8081 \
  --set-env-vars="REACT_APP_API_GATEWAY_URL=$GATEWAY_URL,REACT_APP_PROJECT_ID=$PROJECT_ID"
```

## Phase 4: Configure CI/CD

### 4.1 Create Build Trigger

```bash
# Create trigger configuration
./create-build-trigger.sh

# Connect repository and create trigger
gcloud builds triggers create github \
  --repo-name=steampipe-compliance-unified \
  --repo-owner=YOUR_GITHUB_ORG \
  --branch-pattern=^main$ \
  --build-config=cloudbuild-deploy.yaml \
  --substitutions=_DOMAIN=your-domain.com,_REGION=us-central1
```

## Phase 5: Configure Dashboard for Secret Manager

1. Access the Dashboard UI
2. Click the "Settings" button
3. For each cloud provider, configure:
   - **GCP Project ID**: Your Google Cloud project ID
   - **Secret Name**: The name of the secret in Secret Manager

Example configuration:
- GCP Secret: `steampipe-gcp-sa-key`
- AWS Secret: `steampipe-aws-credentials`
- Azure Secret: `steampipe-azure-credentials`

## Phase 6: Testing and Validation

### 6.1 Test Health Endpoints

```bash
# Test public endpoints
ADMIN_URL=$(gcloud run services describe admin-api --region=us-central1 --format='value(status.url)')
DASHBOARD_URL=$(gcloud run services describe dashboard-ui --region=us-central1 --format='value(status.url)')
GATEWAY_URL=$(gcloud run services describe api-gateway --region=us-central1 --format='value(status.url)')

curl $ADMIN_URL/health
curl $GATEWAY_URL/health
curl $DASHBOARD_URL
```

### 6.2 Test Compliance Scans

1. Open Dashboard UI in browser
2. Configure secret manager settings
3. Select organization/project
4. Run compliance benchmark
5. Verify results are displayed

## Troubleshooting

### Common Issues

1. **Service not responding**
   ```bash
   # Check logs
   gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=SERVICE_NAME" --limit=50
   ```

2. **Secret access denied**
   ```bash
   # Grant secret access to service account
   gcloud secrets add-iam-policy-binding SECRET_NAME \
     --member="serviceAccount:steampipe-sa@$PROJECT_ID.iam.gserviceaccount.com" \
     --role="roles/secretmanager.secretAccessor"
   ```

3. **Build failures**
   ```bash
   # Check build logs
   gcloud builds log BUILD_ID
   ```

## Security Best Practices

1. **Use Secret Manager** for all credentials
2. **Enable VPC Service Controls** for internal services
3. **Set up Cloud Armor** for DDoS protection
4. **Configure Identity-Aware Proxy** for authentication
5. **Enable audit logging** for compliance

## Monitoring and Alerting

1. **Set up monitoring**
   ```bash
   # Create uptime checks
   gcloud monitoring uptime create \
     --display-name="Dashboard UI Health" \
     --resource-type="cloud-run" \
     --service=dashboard-ui
   ```

2. **Configure alerts**
   - Service availability < 99%
   - Response time > 5 seconds
   - Error rate > 1%

## Cost Optimization

1. **Set minimum instances to 0** for dev/test environments
2. **Use Cloud Scheduler** to scale down during off-hours
3. **Configure concurrency limits** based on workload
4. **Enable request-based billing** for sporadic usage

## Next Steps

1. Configure custom domain with Cloud Load Balancer
2. Set up Cloud CDN for static assets
3. Implement backup and disaster recovery
4. Configure multi-region deployment
5. Set up continuous monitoring and alerting

## Support

For issues, check:
1. Cloud Run logs: `gcloud logging read`
2. Build history: `gcloud builds list`
3. Service status: `gcloud run services list`

---

**Last Updated**: January 2025
**Version**: 1.0