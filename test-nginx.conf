events {
    worker_connections 1024;
}

http {
    # Test auth token map
    map $uri $auth_token {
        ~^/api/gcp/  "Bearer test-gcp-token";
        ~^/api/aws/  "Bearer test-aws-token";
        ~^/api/azure/  "Bearer test-azure-token";
        default      "";
    }
    
    server {
        listen 8081;
        
        location /api/gcp/ {
            set $auth_header $auth_token;
            if ($auth_header = "") {
                set $auth_header $http_authorization;
            }
            
            # Test what values we have
            add_header X-Debug-Uri $uri always;
            add_header X-Debug-Auth-Token "$auth_token" always;
            add_header X-Debug-Auth-Header "$auth_header" always;
            
            return 200 "URI: $uri\nAuth Token: $auth_token\nAuth Header: $auth_header\n";
        }
    }
}