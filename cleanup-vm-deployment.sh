#!/bin/bash

# Cleanup script for VM deployment

set -e

# Configuration (should match deploy-vm-with-iap.sh)
PROJECT_ID="${PROJECT_ID:-vratant-test-prj}"
REGION="us-central1"
ZONE="us-central1-a"
VM_NAME="steampipe-compliance-vm"
INSTANCE_GROUP_NAME="steampipe-compliance-ig"
BACKEND_SERVICE_NAME="steampipe-compliance-backend"
URL_MAP_NAME="steampipe-compliance-lb"
HTTPS_PROXY_NAME="steampipe-compliance-https-proxy"
FORWARDING_RULE_NAME="steampipe-compliance-forwarding-rule"
SSL_CERT_NAME="steampipe-compliance-cert"
HEALTH_CHECK_NAME="steampipe-compliance-health-check"
FIREWALL_RULE_NAME="allow-health-check"
FIREWALL_IAP_RULE="allow-iap-ssh"

echo "=== Cleaning up VM Deployment ==="
echo "Project: ${PROJECT_ID}"
echo ""
echo "This will delete all resources created by deploy-vm-with-iap.sh"
read -p "Are you sure you want to continue? (yes/no): " confirm

if [ "${confirm}" != "yes" ]; then
    echo "Cleanup cancelled."
    exit 0
fi

# Delete forwarding rule
echo "Deleting forwarding rule..."
gcloud compute forwarding-rules delete ${FORWARDING_RULE_NAME} --global --quiet || echo "Forwarding rule not found"

# Delete HTTPS proxy
echo "Deleting HTTPS proxy..."
gcloud compute target-https-proxies delete ${HTTPS_PROXY_NAME} --global --quiet || echo "HTTPS proxy not found"

# Delete URL map
echo "Deleting URL map..."
gcloud compute url-maps delete ${URL_MAP_NAME} --global --quiet || echo "URL map not found"

# Delete backend service
echo "Deleting backend service..."
gcloud compute backend-services delete ${BACKEND_SERVICE_NAME} --global --quiet || echo "Backend service not found"

# Delete health check
echo "Deleting health check..."
gcloud compute health-checks delete ${HEALTH_CHECK_NAME} --global --quiet || echo "Health check not found"

# Delete SSL certificate
echo "Deleting SSL certificate..."
gcloud compute ssl-certificates delete ${SSL_CERT_NAME} --global --quiet || echo "SSL certificate not found"

# Delete instance group
echo "Deleting instance group..."
gcloud compute instance-groups unmanaged delete ${INSTANCE_GROUP_NAME} --zone=${ZONE} --quiet || echo "Instance group not found"

# Delete VM instance
echo "Deleting VM instance..."
gcloud compute instances delete ${VM_NAME} --zone=${ZONE} --quiet || echo "VM instance not found"

# Delete firewall rules
echo "Deleting firewall rules..."
gcloud compute firewall-rules delete ${FIREWALL_RULE_NAME} --quiet || echo "Firewall rule not found"
gcloud compute firewall-rules delete ${FIREWALL_IAP_RULE} --quiet || echo "IAP firewall rule not found"

echo ""
echo "=== Cleanup Complete ==="
echo "All resources have been deleted."
echo ""
echo "Note: The service account 'steampipe-sa' was not deleted."
echo "To delete it manually, run:"
echo "gcloud iam service-accounts delete steampipe-sa@${PROJECT_ID}.iam.gserviceaccount.com"