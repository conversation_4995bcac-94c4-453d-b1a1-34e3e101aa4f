#!/bin/bash
# Create Cloud Build trigger for CI/CD deployment

set -e

PROJECT_ID=${PROJECT_ID:-"vratant-test-prj"}
REPO_NAME=${REPO_NAME:-"steampipe-compliance-unified"}
TRIGGER_NAME=${TRIGGER_NAME:-"steampipe-deploy-trigger"}
BRANCH=${BRANCH:-"main"}
REGION=${REGION:-"us-central1"}

echo "🔨 Creating Cloud Build trigger..."
echo "Project: $PROJECT_ID"
echo "Repository: $REPO_NAME"
echo "Trigger: $TRIGGER_NAME"
echo "Branch: $BRANCH"
echo ""

# Create the trigger configuration
cat > trigger-config.json <<EOF
{
  "name": "$TRIGGER_NAME",
  "description": "Deploy Steampipe compliance services to Cloud Run",
  "github": {
    "owner": "YOUR_GITHUB_OWNER",
    "name": "$REPO_NAME",
    "push": {
      "branch": "^${BRANCH}$"
    }
  },
  "filename": "cloudbuild-deploy.yaml",
  "substitutions": {
    "_DOMAIN": "steampipe-compliance.com",
    "_REGION": "$REGION"
  },
  "includedFiles": [
    "custom/**",
    "api-gateway/**",
    "Base_image_steampipe/**",
    "steampipe-mod-*/**",
    "cloudbuild-deploy.yaml"
  ]
}
EOF

echo "📋 Trigger configuration:"
cat trigger-config.json | jq .

echo ""
echo "To create the trigger:"
echo "1. First, connect your GitHub repository:"
echo "   gcloud builds repositories create github-$REPO_NAME \\"
echo "     --remote-uri=https://github.com/YOUR_GITHUB_OWNER/$REPO_NAME.git \\"
echo "     --connection=YOUR_CONNECTION_NAME \\"
echo "     --region=$REGION"
echo ""
echo "2. Then create the trigger:"
echo "   gcloud builds triggers create github \\"
echo "     --repo-name=github-$REPO_NAME \\"
echo "     --repo-owner=YOUR_GITHUB_OWNER \\"
echo "     --branch-pattern=^${BRANCH}$ \\"
echo "     --build-config=cloudbuild-deploy.yaml \\"
echo "     --substitutions=_DOMAIN=steampipe-compliance.com,_REGION=$REGION \\"
echo "     --include-logs-with-status"
echo ""
echo "3. Or import from file:"
echo "   gcloud builds triggers import --source=trigger-config.json"
echo ""
echo "🔗 View triggers: https://console.cloud.google.com/cloud-build/triggers?project=$PROJECT_ID"