#!/bin/bash

# Build all container images using Cloud Build
# This script builds all images and tags them with the current git commit SHA

echo "🔨 Starting Cloud Build..."
echo "Building images with commit SHA: $(git rev-parse --short HEAD)"
echo ""

# Run Cloud Build with the current commit SHA as a substitution
gcloud builds submit --config cloudbuild.yaml --substitutions=SHORT_SHA=$(git rev-parse --short HEAD)

# Check if build succeeded
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Build completed successfully!"
    echo "Images tagged with: $(git rev-parse --short HEAD)"
    echo ""
    echo "To deploy, run: ./deploy.sh"
else
    echo ""
    echo "❌ Build failed! Check the logs above for details."
    exit 1
fi