# GCP Compliance Service
ARG BASE_IMAGE=steampipe-base:latest
FROM ${BASE_IMAGE}

# Switch to root for installations
USER root

# GCloud SDK is already installed in base image

# Copy shared code first
COPY --chown=steampipe:steampipe custom/shared /app/shared

# Copy GCP-specific files
COPY --chown=steampipe:steampipe gcp/requirements-standalone.txt /app/requirements.txt
RUN pip3 install --no-cache-dir -r /app/requirements.txt

# Don't copy mod files as they conflict with the official mod
# The official mod will be installed by powerpipe
# COPY --chown=steampipe:steampipe steampipe-mod-gcp-compliance/mod.pp /app/steampipe-mod-gcp-compliance/mod.pp
# COPY --chown=steampipe:steampipe steampipe-mod-gcp-compliance/variables.pp /app/steampipe-mod-gcp-compliance/variables.pp

# Copy GCP app files
COPY --chown=steampipe:steampipe steampipe-mod-gcp-compliance/app.py /app/app.py
COPY --chown=steampipe:steampipe steampipe-mod-gcp-compliance/auth.py /app/auth.py
COPY --chown=steampipe:steampipe steampipe-mod-gcp-compliance/run_steampipe.py /app/run_steampipe.py
COPY --chown=steampipe:steampipe steampipe-mod-gcp-compliance/generate_control.py /app/generate_control.py
COPY --chown=steampipe:steampipe steampipe-mod-gcp-compliance/steampipe-config /app/steampipe-config

# Switch back to steampipe user
USER steampipe

# Create a directory for the mod and install the official GCP compliance mod
RUN mkdir -p /app/gcp-compliance && \
    cd /app/gcp-compliance && \
    powerpipe mod init && \
    powerpipe mod install github.com/turbot/steampipe-mod-gcp-compliance@latest && \
    ls -la

# Set environment variables
ENV FLASK_APP=app.py
ENV SERVICE_TYPE=gcp
ENV PORT=8080

# Expose port
EXPOSE 8080

# Start the Flask app
CMD ["python3", "-u", "/app/app.py"]