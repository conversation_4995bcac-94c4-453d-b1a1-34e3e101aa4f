#!/bin/bash
# Test Cloud Build configuration locally
# This script validates the build steps without actually deploying

set -e

echo "🧪 Testing Cloud Build configuration locally..."

PROJECT_ID=${PROJECT_ID:-"vratant-test-prj"}
REGION=${REGION:-"us-central1"}
DOMAIN=${DOMAIN:-"localhost"}

# Function to test docker build
test_docker_build() {
    local service=$1
    local dockerfile=$2
    echo "Testing $service build..."
    
    if [ -f "$dockerfile" ]; then
        echo "✅ Dockerfile exists: $dockerfile"
        # Test dockerfile syntax
        docker build --no-cache -f "$dockerfile" . --dry-run 2>/dev/null && echo "✅ Dockerfile syntax valid" || echo "❌ Dockerfile syntax error"
    else
        echo "❌ Dockerfile not found: $dockerfile"
        return 1
    fi
}

# Function to check if required files exist
check_files() {
    echo "📁 Checking required files..."
    
    local files=(
        "Base_image_steampipe/Dockerfile"
        "custom/services/gcp/Dockerfile"
        "custom/services/aws/Dockerfile"
        "custom/services/azure/Dockerfile"
        "api-gateway/Dockerfile"
        "custom/services/admin/Dockerfile"
        "custom/dashboard/steampipe-secops-dashboard/frontend/Dockerfile"
        "cloudbuild-deploy.yaml"
    )
    
    local all_exist=true
    for file in "${files[@]}"; do
        if [ -f "$file" ]; then
            echo "✅ $file"
        else
            echo "❌ Missing: $file"
            all_exist=false
        fi
    done
    
    if [ "$all_exist" = false ]; then
        echo "❌ Some required files are missing!"
        return 1
    fi
}

# Function to validate Cloud Build YAML
validate_cloudbuild() {
    echo "📋 Validating Cloud Build configuration..."
    
    # Check if gcloud is installed
    if ! command -v gcloud &> /dev/null; then
        echo "⚠️  gcloud CLI not found. Skipping Cloud Build validation."
        return 0
    fi
    
    # Validate the YAML syntax
    gcloud builds submit --config=cloudbuild-deploy.yaml --dry-run --no-source 2>&1 | grep -q "error" && {
        echo "❌ Cloud Build YAML validation failed"
        return 1
    } || {
        echo "✅ Cloud Build YAML syntax is valid"
    }
}

# Function to test building base image locally
test_base_image() {
    echo "🏗️  Testing base image build locally..."
    
    # Try to build with a test tag
    docker build \
        --platform=linux/amd64 \
        -t test-steampipe-base:local \
        -f Base_image_steampipe/Dockerfile \
        . \
        --target=base 2>/dev/null || {
        echo "⚠️  Note: Full base image build requires time. Testing syntax only."
        docker build \
            --platform=linux/amd64 \
            -t test-steampipe-base:local \
            -f Base_image_steampipe/Dockerfile \
            . \
            --no-cache \
            --progress=plain \
            --target=base \
            --dry-run 2>/dev/null && echo "✅ Base image syntax valid" || echo "❌ Base image syntax error"
    }
}

# Main execution
echo "Starting local Cloud Build tests..."
echo "================================"

# 1. Check files
check_files || exit 1

echo ""

# 2. Test Dockerfile syntax
echo "🐳 Testing Dockerfile syntax..."
test_docker_build "Base Image" "Base_image_steampipe/Dockerfile"
test_docker_build "GCP Compliance" "custom/services/gcp/Dockerfile"
test_docker_build "AWS Compliance" "custom/services/aws/Dockerfile"
test_docker_build "Azure Compliance" "custom/services/azure/Dockerfile"
test_docker_build "API Gateway" "api-gateway/Dockerfile"

echo ""

# 3. Validate Cloud Build YAML
validate_cloudbuild

echo ""

# 4. Check environment
echo "🌍 Checking environment..."
echo "Project ID: $PROJECT_ID"
echo "Region: $REGION"
echo "Domain: $DOMAIN"

# 5. Test substitutions
echo ""
echo "🔄 Testing substitutions..."
cat cloudbuild-deploy.yaml | grep -E '(\$PROJECT_ID|\${_REGION}|\${_DOMAIN})' | head -5

echo ""
echo "================================"
echo "✅ Local validation complete!"
echo ""
echo "📋 To run the actual Cloud Build:"
echo "gcloud builds submit --config=cloudbuild-deploy.yaml --substitutions=_DOMAIN=$DOMAIN,_REGION=$REGION"
echo ""
echo "🚀 Or to test just the build phase:"
echo "gcloud builds submit --config=cloudbuild-deploy.yaml --substitutions=_DOMAIN=$DOMAIN,_REGION=$REGION --no-push"