#!/bin/bash

# Test script for VM deployment with IAP

set -e

# Configuration (should match deploy-vm-with-iap.sh)
PROJECT_ID="${PROJECT_ID:-vratant-test-prj}"
ZONE="us-central1-a"
VM_NAME="steampipe-compliance-vm"
FORWARDING_RULE_NAME="steampipe-compliance-forwarding-rule"
BACKEND_SERVICE_NAME="steampipe-compliance-backend"

echo "=== Testing VM Deployment ==="
echo "Project: ${PROJECT_ID}"

# Check if VM is running
echo ""
echo "1. Checking VM status..."
VM_STATUS=$(gcloud compute instances describe ${VM_NAME} --zone=${ZONE} --format="value(status)")
if [ "${VM_STATUS}" = "RUNNING" ]; then
    echo "✓ VM is running"
else
    echo "✗ VM is not running. Status: ${VM_STATUS}"
    exit 1
fi

# Check if services are running on VM
echo ""
echo "2. Checking Docker services on VM..."
gcloud compute ssh ${VM_NAME} --zone=${ZONE} --tunnel-through-iap --command='sudo docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"' || {
    echo "✗ Failed to check Docker services"
    exit 1
}

# Check backend service health
echo ""
echo "3. Checking backend service health..."
BACKEND_HEALTH=$(gcloud compute backend-services get-health ${BACKEND_SERVICE_NAME} --global --format=json)
echo "${BACKEND_HEALTH}" | jq '.'

# Get load balancer IP
echo ""
echo "4. Getting load balancer details..."
LB_IP=$(gcloud compute forwarding-rules describe ${FORWARDING_RULE_NAME} --global --format="value(IPAddress)")
echo "Load Balancer IP: ${LB_IP}"

# Test health endpoint through load balancer (without IAP)
echo ""
echo "5. Testing health endpoint..."
echo "Note: This test will fail if IAP is properly configured (expected behavior)"
curl -k -s -o /dev/null -w "HTTP Status: %{http_code}\n" https://${LB_IP}/health || true

# Check IAP status
echo ""
echo "6. Checking IAP configuration..."
gcloud iap web get-iam-policy \
    --resource-type=backend-services \
    --service=${BACKEND_SERVICE_NAME} \
    --global || echo "IAP not configured"

# SSH into VM and test internal endpoints
echo ""
echo "7. Testing internal service endpoints..."
echo "Testing AWS compliance service..."
gcloud compute ssh ${VM_NAME} --zone=${ZONE} --tunnel-through-iap --command='curl -s http://localhost:8082/health | jq .' || echo "AWS service not responding"

echo "Testing GCP compliance service..."
gcloud compute ssh ${VM_NAME} --zone=${ZONE} --tunnel-through-iap --command='curl -s http://localhost:8080/health | jq .' || echo "GCP service not responding"

echo "Testing API Gateway..."
gcloud compute ssh ${VM_NAME} --zone=${ZONE} --tunnel-through-iap --command='curl -s http://localhost:8081/health | jq .' || echo "API Gateway not responding"

echo "Testing Dashboard UI..."
gcloud compute ssh ${VM_NAME} --zone=${ZONE} --tunnel-through-iap --command='curl -s -o /dev/null -w "%{http_code}\n" http://localhost:80/' || echo "Dashboard not responding"

# Check logs for errors
echo ""
echo "8. Checking for errors in logs (last 20 lines)..."
gcloud compute ssh ${VM_NAME} --zone=${ZONE} --tunnel-through-iap --command='sudo docker-compose -f /opt/steampipe-compliance/docker-compose.yml logs --tail=20' | grep -i error || echo "No errors found in recent logs"

# Summary
echo ""
echo "=== Test Summary ==="
echo "VM Name: ${VM_NAME}"
echo "Load Balancer IP: https://${LB_IP}/"
echo ""
echo "To access the dashboard (after configuring IAP users):"
echo "1. Add authorized users in Cloud Console > Security > Identity-Aware Proxy"
echo "2. Access: https://${LB_IP}/"
echo ""
echo "To tunnel to dashboard locally (bypass IAP for testing):"
echo "gcloud compute start-iap-tunnel ${VM_NAME} 8080 --local-host-port=localhost:8080 --zone=${ZONE}"
echo "Then access: http://localhost:8080/"
echo ""
echo "=== Test Complete ==="