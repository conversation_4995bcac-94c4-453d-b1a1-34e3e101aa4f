# 🛡️ Cloud Compliance Benchmark API Integration Guide

This guide explains how to integrate with our cloud compliance benchmark platform to run security assessments across GCP, AWS, and Azure environments.

---

## 🎯 Overview

Our platform enables programmatic compliance scanning across multiple cloud providers using a simple API-based approach:

1. **We provide** a Google Cloud Service Account key with Secret Manager access
2. **You store** your customers' cloud credentials in our Secret Manager
3. **You trigger** compliance benchmarks via API calls
4. **You retrieve** detailed compliance reports asynchronously

---

## 🔑 Step 1: Service Account Setup

### What We Provide

We'll provision a GCP Service Account with the following permissions:
- `roles/secretmanager.admin` - Full access to create, update, and manage secrets
- Limited to our Secret Manager instance only

### Authentication

```bash
# Authenticate using the provided service account key
gcloud auth activate-service-account --key-file=./your-service-account-key.json

# Set the project (we'll provide the project ID)
gcloud config set project <our-project-id>
```

---

## 📦 Step 2: Storing Customer Credentials

### Credential Naming Convention

Use this naming pattern to organize credentials:

```
<customer-name>-<cloud-provider>-<scope>
```

Examples:
- `acme-corp-gcp-project-prod-analytics`
- `acme-corp-aws-account-********9012`
- `*********************************`
- `globex-inc-aws-organization-root`
- `globex-inc-azure-tenant-wide`

### Required Credential Formats

#### 🔷 GCP Credentials
Store the entire service account JSON key file:

```bash
# For GCP, store the service account key JSON directly
gcloud secrets create acme-corp-gcp-project-prod-analytics --replication-policy="automatic"
cat acme-gcp-key.json | gcloud secrets versions add acme-corp-gcp-project-prod-analytics --data-file=-
```

**Required GCP Permissions:**
- `roles/viewer` (Viewer role)
- `roles/browser` (Browser role)

**Scope:** Per GCP project

#### 🟡 AWS Credentials
Store as JSON with access key and secret:

```bash
# Create AWS credentials JSON
cat > aws-creds.json <<EOF
{
  "access_key_id": "AKIAIOSFODNN7EXAMPLE",
  "secret_access_key": "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY",
  "region": "us-east-1"
}
EOF

# Store in Secret Manager
gcloud secrets create acme-corp-aws-account-********9012 --replication-policy="automatic"
cat aws-creds.json | gcloud secrets versions add acme-corp-aws-account-********9012 --data-file=-
rm aws-creds.json  # Clean up
```

**Required AWS Permissions:**
- For organization-wide scanning: `arn:aws:iam::aws:policy/ReadOnlyAccess` at organization level
- For single account scanning: `arn:aws:iam::aws:policy/ReadOnlyAccess` at account level

**Scope:** Per AWS account or organization

#### 🔵 Azure Credentials
Store service principal details as JSON:

```bash
# Create Azure credentials JSON
cat > azure-creds.json <<EOF
{
  "tenant_id": "********-1234-1234-1234-********9012",
  "client_id": "********-4321-4321-4321-2109********",
  "client_secret": "your-client-secret-here",
  "subscription_id": "********-5432-5432-5432-************"
}
EOF

# Store in Secret Manager
gcloud secrets create ********************************* --replication-policy="automatic"
cat azure-creds.json | gcloud secrets versions add ********************************* --data-file=-
rm azure-creds.json  # Clean up
```

**Required Azure Permissions:**
- For tenant-wide scanning: `Reader` role at Management Group level
- For single subscription scanning: `Reader` role at subscription level

**Scope:** Per Azure subscription or tenant

### 🔐 Important Notes on Encoding

- **No Base64 encoding required** - Store credentials as plain JSON
- The platform handles all necessary encoding internally
- Keep credentials in valid JSON format for easy updates

---

## 🚀 Step 3: Running Compliance Benchmarks

### API Endpoints

| Provider | Endpoint |
|----------|----------|
| GCP | `https://<api-gateway-url>/api/gcp/run-benchmark-async` |
| AWS | `https://<api-gateway-url>/api/aws/run-benchmark-async` |
| Azure | `https://<api-gateway-url>/api/azure/run-benchmark-async` |

### Example: GCP Benchmark

```bash
curl -X POST https://<api-gateway-url>/api/gcp/run-benchmark-async \
-H "Content-Type: application/json" \
-H "X-API-Key: <your-api-key>" \
-d '{
  "benchmark": "cis_v300",
  "project_id": "prod-analytics-project",
  "secret_manager_name": "acme-corp-gcp-project-prod-analytics"
}'
```

### Example: AWS Benchmark

```bash
curl -X POST https://<api-gateway-url>/api/aws/run-benchmark-async \
-H "Content-Type: application/json" \
-H "X-API-Key: <your-api-key>" \
-d '{
  "benchmark": "cis_v200",
  "account_id": "********9012",
  "secret_manager_name": "acme-corp-aws-account-********9012"
}'
```

### Example: Azure Benchmark

```bash
curl -X POST https://<api-gateway-url>/api/azure/run-benchmark-async \
-H "Content-Type: application/json" \
-H "X-API-Key: <your-api-key>" \
-d '{
  "benchmark": "cis_v200",
  "subscription_id": "********-5432-5432-5432-************",
  "secret_manager_name": "*********************************"
}'
```


## 📊 Step 4: Monitoring Job Status

### Status Check Endpoint

```bash
GET https://<api-gateway-url>/api/{provider}/job/{job_id}/status
```

### Example Response

```json
{
  "job_id": "550e8400-e29b-41d4-a716-************",
  "status": "running",
  "progress": 45,
  "message": "Scanning compute instances...",
  "started_at": "2024-07-25T10:30:00Z",
  "updated_at": "2024-07-25T10:32:15Z"
}
```

### Status Values

| Status | Description |
|--------|-------------|
| `pending` | Job queued but not started |
| `running` | Benchmark scan in progress |
| `success` | Scan completed successfully |
| `failed` | Scan failed (check error message) |

---

## 📈 Step 5: Retrieving Results

### Results Endpoint

```bash
GET https://<api-gateway-url>/api/{provider}/job/{job_id}/result
```

### Example Result Structure

```json
{
  "job_id": "550e8400-e29b-41d4-a716-************",
  "status": "success",
  "benchmark": "cis_v300",
  "summary": {
    "total_controls": 278,
    "passed": 245,
    "failed": 28,
    "skipped": 5,
    "compliance_score": 88.1
  },
  "scan_metadata": {
    "started_at": "2024-07-25T10:30:00Z",
    "completed_at": "2024-07-25T10:35:42Z",
    "duration_seconds": 342,
    "target": "prod-analytics-project"
  },
  "controls": [
    {
      "control_id": "cis_v300_1_1",
      "title": "Ensure that corporate login credentials are used",
      "severity": "high",
      "status": "passed",
      "resources_tested": 15,
      "resources_passed": 15,
      "resources_failed": 0
    },
    {
      "control_id": "cis_v300_1_2",
      "title": "Ensure that multi-factor authentication is enabled for all users",
      "severity": "critical",
      "status": "failed",
      "resources_tested": 25,
      "resources_passed": 22,
      "resources_failed": 3,
      "failed_resources": [
        "user:<EMAIL>",
        "user:<EMAIL>",
        "user:<EMAIL>"
      ],
      "remediation": "Enable MFA for all user accounts, especially privileged users."
    }
  ]
}
```

---

## 🔒 Security Best Practices

### Credential Management
- ✅ Store each customer's credentials separately
- ✅ Use descriptive naming that includes customer, provider, and scope
- ✅ Regularly rotate cloud credentials
- ✅ Delete credentials when customers offboard
- ✅ Never expose credentials in logs or error messages

### API Security
- ✅ Keep your API key confidential
- ✅ Use HTTPS for all API calls
- ✅ Implement rate limiting in your application
- ✅ Log all API activities for audit purposes

### Service Account Key Protection
- ✅ Store the service account key securely
- ✅ Limit access to authorized personnel only
- ✅ Rotate the service account key periodically
- ✅ Monitor Secret Manager access logs

---

## 🔄 Updating Credentials

To update existing credentials:

```bash
# Create new version of existing secret
cat updated-aws-creds.json | gcloud secrets versions add acme-corp-aws-account-********9012 --data-file=-

# The latest version is automatically used
# Old versions are retained for audit trail
```

---

## 🚨 Error Handling

### Common Error Responses

```json
{
  "error": "SECRET_NOT_FOUND",
  "message": "Secret 'acme-corp-aws-account-********9012' not found",
  "status": 404
}
```

```json
{
  "error": "INVALID_CREDENTIALS",
  "message": "AWS credentials are invalid or expired",
  "status": 401
}
```

```json
{
  "error": "INSUFFICIENT_PERMISSIONS",
  "message": "The provided credentials lack required permissions",
  "status": 403
}
```

---

## 📞 Support

For technical support or questions:
- Email: <EMAIL>

---

## 📋 Quick Reference

| Step | Action | Command/Endpoint |
|------|--------|-----------------|
| 1️⃣ | Authenticate | `gcloud auth activate-service-account --key-file=./key.json` |
| 2️⃣ | Store credentials | `gcloud secrets create <name> --replication-policy="automatic"` |
| 3️⃣ | Run benchmark | `POST /api/{provider}/run-benchmark-async` |
| 4️⃣ | Check status | `GET /api/{provider}/job/{job_id}/status` |
| 5️⃣ | Get results | `GET /api/{provider}/job/{job_id}/result` |

---

*Last updated: July 2024*