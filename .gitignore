# Logs
*.log
logs/

# Test outputs
test-output/
test-results/
test_*.json
test_*.txt

# Benchmark outputs
**/outputs/*.json
**/*compliance.benchmark.*.json
steampipe-mod-*/outputs/
opensource/mods/*/outputs/

# OS files
.DS_Store
Thumbs.db

# IDE files
.idea/
.vscode/
*.swp
*.swo

# Python
__pycache__/
*.py[cod]
*$py.class
.pytest_cache/
venv/
env/
.env

# Node
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# Docker volumes (local testing)
.docker-volumes/

# Credentials (NEVER commit these)
*-creds.json
*-credentials.json
service-account*.json
testcreds*.json