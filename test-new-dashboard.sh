#!/bin/bash
# Test script for the new dashboard

echo "=== Testing New Steampipe Compliance Dashboard ==="
echo ""

# Check if running locally
if [ -f "custom/dashboard/steampipe-secops-dashboard/frontend/dashboard.html" ]; then
    echo "✓ Dashboard HTML file found"
    
    # Check if services are running
    echo ""
    echo "Checking service endpoints..."
    
    # Test GCP service
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/health | grep -q "200"; then
        echo "✓ GCP service is running on port 8080"
    else
        echo "✗ GCP service is not responding on port 8080"
    fi
    
    # Test AWS service
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:8082/health | grep -q "200"; then
        echo "✓ AWS service is running on port 8082"
    else
        echo "✗ AWS service is not responding on port 8082"
    fi
    
    # Test Azure service
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:8083/health | grep -q "200"; then
        echo "✓ Azure service is running on port 8083"
    else
        echo "✗ Azure service is not responding on port 8083"
    fi
    
    echo ""
    echo "To view the dashboard:"
    echo "1. Open custom/dashboard/steampipe-secops-dashboard/frontend/dashboard.html in a web browser"
    echo "2. Or serve it with: python3 -m http.server 8000 -d custom/dashboard/steampipe-secops-dashboard/frontend/"
    echo "   Then open http://localhost:8000/dashboard.html"
    
    # Start a simple HTTP server to serve the dashboard
    echo ""
    echo "Starting HTTP server for the dashboard..."
    echo "Dashboard will be available at: http://localhost:8000/dashboard.html"
    echo "Press Ctrl+C to stop the server"
    cd custom/dashboard/steampipe-secops-dashboard/frontend/
    python3 -m http.server 8000
else
    echo "✗ Dashboard HTML file not found!"
    echo "  Expected location: custom/dashboard/steampipe-secops-dashboard/frontend/dashboard.html"
fi