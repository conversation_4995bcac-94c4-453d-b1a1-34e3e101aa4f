#!/bin/bash
# Continuous benchmark testing script
# This script will run all benchmarks for all clouds continuously

# Define endpoints and credentials
GCP_ENDPOINT="http://localhost:8080"
AWS_ENDPOINT="http://localhost:8082"
AZURE_ENDPOINT="http://localhost:8083"

GCP_SECRET="gcp-local-secops"
AWS_SECRET="aws-local-secops"
AZURE_SECRET="azure-local-secops"

# Function to run a benchmark and check for errors
run_benchmark() {
    local endpoint=$1
    local cloud=$2
    local secret=$3
    local benchmark=$4
    local identifier_key=$5
    local identifier_value=$6
    
    echo "========================================="
    echo "Running $benchmark for $cloud..."
    echo "Time: $(date)"
    
    # Build JSON payload
    local payload="{\"secret_reference\": \"$secret\", \"benchmark\": \"$benchmark\", \"$identifier_key\": \"$identifier_value\"}"
    
    # Run benchmark
    local response=$(curl -s -X POST "$endpoint/api/$cloud/benchmark" \
        -H "Content-Type: application/json" \
        -d "$payload")
    
    local job_id=$(echo $response | jq -r '.job_id' 2>/dev/null)
    
    if [ -z "$job_id" ] || [ "$job_id" = "null" ]; then
        echo "ERROR: Failed to start benchmark"
        echo "Response: $response"
        return 1
    fi
    
    echo "Job started: $job_id"
    
    # Wait a bit for job to process
    sleep 30
    
    # Check job status
    local status_response=$(curl -s "$endpoint/api/$cloud/job/$job_id/status")
    local status=$(echo $status_response | jq -r '.status' 2>/dev/null)
    local error=$(echo $status_response | jq -r '.error' 2>/dev/null)
    
    echo "Job status: $status"
    
    if [ "$error" != "null" ] && [ -n "$error" ]; then
        echo "ERROR detected in job: $error"
    fi
    
    # If completed, check result for errors
    if [ "$status" = "completed" ]; then
        local result_response=$(curl -s "$endpoint/api/$cloud/job/$job_id/result")
        local result_error=$(echo $result_response | jq -r '.result.error' 2>/dev/null)
        
        if [ "$result_error" != "null" ] && [ -n "$result_error" ]; then
            echo "ERROR in benchmark result: $result_error"
        else
            echo "Benchmark completed successfully"
            # Count controls
            local total_controls=$(echo $result_response | jq '.result.benchmark_results.groups[].controls | length' 2>/dev/null | paste -sd+ | bc)
            echo "Total controls evaluated: $total_controls"
        fi
    fi
    
    echo "========================================="
    echo ""
}

# Main testing loop
echo "Starting continuous benchmark testing at $(date)"
echo "This will run for 30 minutes..."
echo ""

# Calculate end time (30 minutes from now)
end_time=$(($(date +%s) + 1800))

# Counter for iterations
iteration=0

while [ $(date +%s) -lt $end_time ]; do
    iteration=$((iteration + 1))
    echo "########## ITERATION $iteration ##########"
    echo "Time remaining: $(( ($end_time - $(date +%s)) / 60 )) minutes"
    echo ""
    
    # Run AWS benchmarks
    run_benchmark "$AWS_ENDPOINT" "aws" "$AWS_SECRET" "cis_v300" "account_id" "all-accounts"
    run_benchmark "$AWS_ENDPOINT" "aws" "$AWS_SECRET" "cis_v200" "account_id" "************"
    run_benchmark "$AWS_ENDPOINT" "aws" "$AWS_SECRET" "cis_v150" "account_id" "all-accounts"
    run_benchmark "$AWS_ENDPOINT" "aws" "$AWS_SECRET" "foundational_security" "account_id" "all-accounts"
    run_benchmark "$AWS_ENDPOINT" "aws" "$AWS_SECRET" "nist_800_53_rev_5" "account_id" "************"
    
    # Run Azure benchmarks
    run_benchmark "$AZURE_ENDPOINT" "azure" "$AZURE_SECRET" "cis_v210" "subscription_id" "all-subscriptions"
    run_benchmark "$AZURE_ENDPOINT" "azure" "$AZURE_SECRET" "cis_v200" "subscription_id" "********-0000-0000-0000-********0000"
    run_benchmark "$AZURE_ENDPOINT" "azure" "$AZURE_SECRET" "cis_v130" "subscription_id" "all-subscriptions"
    run_benchmark "$AZURE_ENDPOINT" "azure" "$AZURE_SECRET" "hipaa_hitrust_v92" "subscription_id" "all-subscriptions"
    run_benchmark "$AZURE_ENDPOINT" "azure" "$AZURE_SECRET" "nist_sp_800_53_rev_5" "subscription_id" "********-1111-1111-1111-************"
    
    # Run GCP benchmarks
    run_benchmark "$GCP_ENDPOINT" "gcp" "$GCP_SECRET" "cis_v300" "project_id" "all-projects"
    run_benchmark "$GCP_ENDPOINT" "gcp" "$GCP_SECRET" "cis_v200" "project_id" "my-test-project"
    run_benchmark "$GCP_ENDPOINT" "gcp" "$GCP_SECRET" "cis_v120" "project_id" "all-projects"
    run_benchmark "$GCP_ENDPOINT" "gcp" "$GCP_SECRET" "nist_800_53_rev_5" "project_id" "all-projects"
    run_benchmark "$GCP_ENDPOINT" "gcp" "$GCP_SECRET" "nist_csf" "project_id" "another-project"
    
    echo "Waiting before next iteration..."
    sleep 60
done

echo ""
echo "########## TESTING COMPLETE ##########"
echo "Completed at $(date)"
echo "Total iterations: $iteration"