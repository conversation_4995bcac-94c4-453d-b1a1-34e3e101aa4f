# Git files
.git
.gitignore

# Documentation
# *.md  # Need .md files for steampipe mods
# docs/  # Need docs directory for steampipe mods
README*
LICENSE*
CHANGELOG*

# Build artifacts
build/
outputs/
*.log
*.pyc
__pycache__/
.pytest_cache/
*.egg-info/
dist/

# IDE files
.vscode/
.idea/
*.swp
*.swo
.env
.envrc

# OS files
.DS_Store
Thumbs.db
desktop.ini

# Credential files
*credentials*.json
*.key
*.pem
service-account*.json
*.tfvars
*.tfstate*

# Test files
test_*.py
*_test.py
tests/
ci-test.log
flask_test_output.log
local_test_output.log

# Deployment files
deployment/
deploy-*.sh
cloudbuild*.yaml
validate-*.sh
setup-*.sh
scripts/

# Large directories we don't need
opensource/core/
opensource/plugins/
custom/admin-ui/
custom/dashboard/
shared/

# Temporary files
tmp/
temp/
*.tmp
.cache/

# Archives
*.tar
*.tar.gz
*.zip
*.rar
*.7z

# Docker files we don't need
docker-compose.*.yaml
Dockerfile.*
.dockerignore

# Output directories
steampipe-mod-*/outputs/
# steampipe-mod-*/docs/  # Need docs/index.md for mod.pp
steampipe-mod-*/generate_prompt.txt

# Not needed compliance mods
# steampipe-mod-aws-compliance/
# steampipe-mod-azure-compliance/

# Local development
node_modules/
.npm/
.yarn/
package-lock.json
yarn.lock

# Python virtual environments
venv/
env/
ENV/
.Python

# Terraform
.terraform/
*.tfplan

# Backup files
*.bak
*.backup
*~
*.orig

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover

# Jupyter Notebook
.ipynb_checkpoints

# macOS
*.DS_Store
.AppleDouble
.LSOverride

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
*~
.directory
.Trash-*