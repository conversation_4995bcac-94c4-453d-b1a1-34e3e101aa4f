#!/bin/bash

echo "Testing Secret Reference Authentication for All Services"
echo "======================================================="

# GCP Test
echo -e "\n1. Testing GCP with secret reference..."
curl -X POST http://localhost:8080/api/gcp/test-auth \
  -H "Content-Type: application/json" \
  -d '{"secret_reference": "sa-gcp-testing-gcpm"}' \
  2>/dev/null | jq . || echo "GCP test-auth failed"

# AWS Test  
echo -e "\n2. Testing AWS with secret reference..."
curl -X POST http://localhost:8082/api/aws/test-auth \
  -H "Content-Type: application/json" \
  -d '{"secret_reference": "aws-local-secops"}' \
  2>/dev/null | jq . || echo "AWS test-auth failed"

# Azure Test
echo -e "\n3. Testing Azure with secret reference..."
curl -X POST http://localhost:8083/api/azure/test-auth \
  -H "Content-Type: application/json" \
  -d '{"secret_reference": "azure-test-secret"}' \
  2>/dev/null | jq . || echo "Azure test-auth failed"

echo -e "\n\nRunning Benchmark with 'all-accounts' Option"
echo "============================================="

# AWS Benchmark with all-accounts
echo -e "\n4. Running AWS CIS v3.0.0 benchmark for all accounts..."
curl -X POST http://localhost:8082/api/aws/run-aws-benchmark-async \
  -H "Content-Type: application/json" \
  -d '{
    "benchmark": "cis_v300",
    "account_id": "all-accounts",
    "secret_reference": "aws-local-secops"
  }' \
  2>/dev/null | jq . || echo "AWS benchmark failed"

# GCP Benchmark with all-projects
echo -e "\n5. Running GCP CIS v3.0.0 benchmark for all projects..."
curl -X POST http://localhost:8080/api/gcp/run-gcp-benchmark-async \
  -H "Content-Type: application/json" \
  -d '{
    "benchmark": "cis_v300",
    "project_id": "all-projects", 
    "secret_reference": "sa-gcp-testing-gcpm"
  }' \
  2>/dev/null | jq . || echo "GCP benchmark failed"

# Azure Benchmark with all-subscriptions
echo -e "\n6. Running Azure CIS v3.0.0 benchmark for all subscriptions..."
curl -X POST http://localhost:8083/api/azure/run-azure-benchmark-async \
  -H "Content-Type: application/json" \
  -d '{
    "benchmark": "cis_v300",
    "subscription_id": "all-subscriptions",
    "secret_reference": "azure-test-secret"  
  }' \
  2>/dev/null | jq . || echo "Azure benchmark failed"