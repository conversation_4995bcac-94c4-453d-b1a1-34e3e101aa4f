# Cloud Build Pipeline - Builds ALL Images from docker-compose.yaml
# Matches the exact structure and build configuration from docker-compose.yaml

steps:
  # Step 1: Build base image (required by compliance services)
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-steampipe-base'
    args:
      - 'build'
      - '--platform=linux/amd64'
      - '-t'
      - 'gcr.io/$PROJECT_ID/steampipe-base:latest'
      - '-t'
      - 'gcr.io/$PROJECT_ID/steampipe-base:$SHORT_SHA'
      - '-f'
      - 'Base_image_steampipe/Dockerfile'
      - '.'

  # Step 2: Push base image (required by compliance services)
  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-steampipe-base'
    args:
      - 'push'
      - '--all-tags'
      - 'gcr.io/$PROJECT_ID/steampipe-base'
    waitFor: ['build-steampipe-base']

  # Step 3: Build GCP Compliance Service (steampipe-gcp from docker-compose.yaml)
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-steampipe-gcp'
    args:
      - 'build'
      - '--platform=linux/amd64'
      - '-t'
      - 'gcr.io/$PROJECT_ID/steampipe-gcp-compliance:latest'
      - '-t'
      - 'gcr.io/$PROJECT_ID/steampipe-gcp-compliance:$SHORT_SHA'
      - '-f'
      - 'custom/services/gcp/Dockerfile'
      - '--build-arg'
      - 'BASE_IMAGE=gcr.io/$PROJECT_ID/steampipe-base:latest'
      - '.'
    waitFor: ['push-steampipe-base']

  # Step 4: Build Azure Compliance Service (steampipe-azure from docker-compose.yaml)
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-steampipe-azure'
    args:
      - 'build'
      - '--platform=linux/amd64'
      - '-t'
      - 'gcr.io/$PROJECT_ID/steampipe-azure-compliance:latest'
      - '-t'
      - 'gcr.io/$PROJECT_ID/steampipe-azure-compliance:$SHORT_SHA'
      - '-f'
      - 'custom/services/azure/Dockerfile'
      - '--build-arg'
      - 'BASE_IMAGE=gcr.io/$PROJECT_ID/steampipe-base:latest'
      - '.'
    waitFor: ['push-steampipe-base']

  # Step 5: Build AWS Compliance Service (steampipe-aws from docker-compose.yaml)
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-steampipe-aws'
    args:
      - 'build'
      - '--platform=linux/amd64'
      - '-t'
      - 'gcr.io/$PROJECT_ID/steampipe-aws-compliance:latest'
      - '-t'
      - 'gcr.io/$PROJECT_ID/steampipe-aws-compliance:$SHORT_SHA'
      - '-f'
      - 'custom/services/aws/Dockerfile'
      - '--build-arg'
      - 'BASE_IMAGE=gcr.io/$PROJECT_ID/steampipe-base:latest'
      - '.'
    waitFor: ['push-steampipe-base']

  # Step 6: Build API Gateway (api-gateway from docker-compose.yaml)
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-api-gateway'
    args:
      - 'build'
      - '--platform=linux/amd64'
      - '-t'
      - 'gcr.io/$PROJECT_ID/api-gateway:latest'
      - '-t'
      - 'gcr.io/$PROJECT_ID/api-gateway:$SHORT_SHA'
      - '-f'
      - 'Dockerfile'
      - 'api-gateway/'
    waitFor: ['-']

  # Step 7: Build Dashboard UI (dashboard-ui from docker-compose.yaml)
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-dashboard-ui'
    args:
      - 'build'
      - '--platform=linux/amd64'
      - '-t'
      - 'gcr.io/$PROJECT_ID/dashboard-ui:latest'
      - '-t'
      - 'gcr.io/$PROJECT_ID/dashboard-ui:$SHORT_SHA'
      - '-f'
      - 'Dockerfile'
      - 'custom/dashboard/steampipe-secops-dashboard/frontend/'
    waitFor: ['-']

  # Push all images in parallel
  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-steampipe-gcp'
    args: ['push', '--all-tags', 'gcr.io/$PROJECT_ID/steampipe-gcp-compliance']
    waitFor: ['build-steampipe-gcp']

  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-steampipe-azure'
    args: ['push', '--all-tags', 'gcr.io/$PROJECT_ID/steampipe-azure-compliance']
    waitFor: ['build-steampipe-azure']

  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-steampipe-aws'
    args: ['push', '--all-tags', 'gcr.io/$PROJECT_ID/steampipe-aws-compliance']
    waitFor: ['build-steampipe-aws']

  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-api-gateway'
    args: ['push', '--all-tags', 'gcr.io/$PROJECT_ID/api-gateway']
    waitFor: ['build-api-gateway']

  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-dashboard-ui'
    args: ['push', '--all-tags', 'gcr.io/$PROJECT_ID/dashboard-ui']
    waitFor: ['build-dashboard-ui']

  # Summary step
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'summary'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        echo "============================================"
        echo "✅ ALL 6 IMAGES BUILT SUCCESSFULLY! ✅"
        echo "============================================"
        echo ""
        echo "Images built and pushed (ready for VM deployment):"
        echo "1. steampipe-base:${SHORT_SHA}"
        echo "2. steampipe-gcp-compliance:${SHORT_SHA}"
        echo "3. steampipe-azure-compliance:${SHORT_SHA}"
        echo "4. steampipe-aws-compliance:${SHORT_SHA}"
        echo "5. api-gateway:${SHORT_SHA}"
        echo "6. dashboard-ui:${SHORT_SHA}"
        echo ""
        echo "Registry: gcr.io/$PROJECT_ID"
        echo ""
        echo "These images are ready for deployment using deploy-vm-with-iap.sh"
        echo "All configuration files are baked into the images."
        echo ""
        echo "To deploy to VM, run:"
        echo "./deploy-vm-with-iap.sh"
        echo ""
    waitFor: ['push-steampipe-gcp', 'push-steampipe-azure', 'push-steampipe-aws',
              'push-api-gateway', 'push-dashboard-ui']

# Timeout for the entire build
timeout: 1800s

# Build options
options:
  logging: CLOUD_LOGGING_ONLY
  machineType: 'E2_HIGHCPU_8'
  substitution_option: 'ALLOW_LOOSE'