# Cloud Build configuration
# Builds and deploys all compliance services
#
# Trigger with:
# gcloud builds submit --config=cloudbuild.yaml

options:
  machineType: 'E2_HIGHCPU_8'
  logging: CLOUD_LOGGING_ONLY

substitutions:
  _REGION: us-central1
  _DOMAIN: https://marketplace.neosec.com
  _MEMORY: 8Gi
  _ADMIN_MEMORY: 1Gi
  _MIN_INSTANCES: '1'
  _MAX_INSTANCES: '10'
  _DEPLOY_SERVICES: 'all'  # Can be: all, admin, gcp, aws, azure, or comma-separated

steps:
  # Build base image
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-base'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        docker build \
          --platform=linux/amd64 \
          -t gcr.io/${PROJECT_ID}/steampipe-base:latest \
          -t gcr.io/${PROJECT_ID}/steampipe-base:${SHORT_SHA} \
          -f Base_image_steampipe/Dockerfile \
          .

  # Build Admin API
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-admin-api'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        if [[ "${_DEPLOY_SERVICES}" == "all" || "${_DEPLOY_SERVICES}" == *"admin"* ]]; then
          docker build \
            --platform=linux/amd64 \
            -t gcr.io/${PROJECT_ID}/admin-api:latest \
            -t gcr.io/${PROJECT_ID}/admin-api:${SHORT_SHA} \
            -f custom/services/admin/Dockerfile \
            .
        fi
    waitFor: ['-']

  # Build GCP Compliance Service
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-gcp-compliance'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        if [[ "${_DEPLOY_SERVICES}" == "all" || "${_DEPLOY_SERVICES}" == *"gcp"* ]]; then
          docker build \
            --platform=linux/amd64 \
            -t gcr.io/${PROJECT_ID}/steampipe-gcp-compliance:latest \
            -t gcr.io/${PROJECT_ID}/steampipe-gcp-compliance:${SHORT_SHA} \
            -f custom/services/gcp/Dockerfile \
            --build-arg BASE_IMAGE=gcr.io/${PROJECT_ID}/steampipe-base:latest \
            .
        fi
    waitFor: ['build-base']

  # Build AWS Compliance Service
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-aws-compliance'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        if [[ "${_DEPLOY_SERVICES}" == "all" || "${_DEPLOY_SERVICES}" == *"aws"* ]]; then
          docker build \
            --platform=linux/amd64 \
            -t gcr.io/${PROJECT_ID}/steampipe-aws-compliance:latest \
            -t gcr.io/${PROJECT_ID}/steampipe-aws-compliance:${SHORT_SHA} \
            -f custom/services/aws/Dockerfile \
            --build-arg BASE_IMAGE=gcr.io/${PROJECT_ID}/steampipe-base:latest \
            .
        fi
    waitFor: ['build-base']

  # Build Azure Compliance Service
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-azure-compliance'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        if [[ "${_DEPLOY_SERVICES}" == "all" || "${_DEPLOY_SERVICES}" == *"azure"* ]]; then
          docker build \
            --platform=linux/amd64 \
            -t gcr.io/${PROJECT_ID}/steampipe-azure-compliance:latest \
            -t gcr.io/${PROJECT_ID}/steampipe-azure-compliance:${SHORT_SHA} \
            -f custom/services/azure/Dockerfile \
            --build-arg BASE_IMAGE=gcr.io/${PROJECT_ID}/steampipe-base:latest \
            .
        fi
    waitFor: ['build-base']

  # Build API Gateway
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-api-gateway'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        docker build \
          --platform=linux/amd64 \
          -t gcr.io/${PROJECT_ID}/api-gateway:latest \
          -t gcr.io/${PROJECT_ID}/api-gateway:${SHORT_SHA} \
          -f api-gateway/Dockerfile \
          api-gateway/
    waitFor: ['-']

  # Build Dashboard UI
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-dashboard-ui'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        docker build \
          --platform=linux/amd64 \
          -t gcr.io/${PROJECT_ID}/dashboard-ui:latest \
          -t gcr.io/${PROJECT_ID}/dashboard-ui:${SHORT_SHA} \
          -f custom/dashboard/steampipe-secops-dashboard/frontend/Dockerfile \
          custom/dashboard/steampipe-secops-dashboard/frontend/
    waitFor: ['-']

  # Push all images
  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-images'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        # Push base image
        docker push gcr.io/${PROJECT_ID}/steampipe-base:latest
        docker push gcr.io/${PROJECT_ID}/steampipe-base:${SHORT_SHA}
        
        # Push service images based on what was built
        if [[ "${_DEPLOY_SERVICES}" == "all" || "${_DEPLOY_SERVICES}" == *"admin"* ]]; then
          docker push gcr.io/${PROJECT_ID}/admin-api:latest
          docker push gcr.io/${PROJECT_ID}/admin-api:${SHORT_SHA}
        fi
        
        if [[ "${_DEPLOY_SERVICES}" == "all" || "${_DEPLOY_SERVICES}" == *"gcp"* ]]; then
          docker push gcr.io/${PROJECT_ID}/steampipe-gcp-compliance:latest
          docker push gcr.io/${PROJECT_ID}/steampipe-gcp-compliance:${SHORT_SHA}
        fi
        
        if [[ "${_DEPLOY_SERVICES}" == "all" || "${_DEPLOY_SERVICES}" == *"aws"* ]]; then
          docker push gcr.io/${PROJECT_ID}/steampipe-aws-compliance:latest
          docker push gcr.io/${PROJECT_ID}/steampipe-aws-compliance:${SHORT_SHA}
        fi
        
        if [[ "${_DEPLOY_SERVICES}" == "all" || "${_DEPLOY_SERVICES}" == *"azure"* ]]; then
          docker push gcr.io/${PROJECT_ID}/steampipe-azure-compliance:latest
          docker push gcr.io/${PROJECT_ID}/steampipe-azure-compliance:${SHORT_SHA}
        fi
        
        # Always push API Gateway and dashboard
        docker push gcr.io/${PROJECT_ID}/api-gateway:latest
        docker push gcr.io/${PROJECT_ID}/api-gateway:${SHORT_SHA}
        docker push gcr.io/${PROJECT_ID}/dashboard-ui:latest
        docker push gcr.io/${PROJECT_ID}/dashboard-ui:${SHORT_SHA}
    waitFor: ['build-admin-api', 'build-gcp-compliance', 'build-aws-compliance', 'build-azure-compliance', 'build-api-gateway', 'build-dashboard-ui']

  # Deploy Admin API
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'deploy-admin-api'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        if [[ "${_DEPLOY_SERVICES}" == "all" || "${_DEPLOY_SERVICES}" == *"admin"* ]]; then
          gcloud run deploy ironfort-admin-api \
            --image=gcr.io/${PROJECT_ID}/admin-api:latest \
            --region=${_REGION} \
            --platform=managed \
            --no-allow-unauthenticated \
            --memory=${_ADMIN_MEMORY} \
            --cpu=1 \
            --timeout=60 \
            --min-instances=${_MIN_INSTANCES} \
            --max-instances=${_MAX_INSTANCES} \
            --service-account=ironfort-compliance-sa@${PROJECT_ID}.iam.gserviceaccount.com \
            --set-env-vars=AUTH_TYPE=secret_manager,CLIENT_NAME=ironfort,GCP_PROJECT_ID=${PROJECT_ID},DOMAIN=${_DOMAIN},SKIP_IAP_CHECK=true
        fi
    waitFor: ['push-images']

  # Deploy GCP Compliance
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'deploy-gcp-compliance'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        if [[ "${_DEPLOY_SERVICES}" == "all" || "${_DEPLOY_SERVICES}" == *"gcp"* ]]; then
          gcloud run deploy ironfort-gcp-compliance \
            --image=gcr.io/${PROJECT_ID}/steampipe-gcp-compliance:latest \
            --region=${_REGION} \
            --platform=managed \
            --no-allow-unauthenticated \
            --memory=${_MEMORY} \
            --cpu=2 \
            --timeout=600 \
            --min-instances=${_MIN_INSTANCES} \
            --max-instances=${_MAX_INSTANCES} \
            --service-account=ironfort-compliance-sa@${PROJECT_ID}.iam.gserviceaccount.com \
            --set-env-vars=AUTH_TYPE=secret_manager,CLIENT_NAME=ironfort,DOMAIN=${_DOMAIN},GCP_PROJECT_ID=${PROJECT_ID},STEAMPIPE_UPDATE_CHECK=false,POWERPIPE_UPDATE_CHECK=false,SKIP_IAP_CHECK=true
        fi
    waitFor: ['push-images']

  # Deploy AWS Compliance
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'deploy-aws-compliance'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        if [[ "${_DEPLOY_SERVICES}" == "all" || "${_DEPLOY_SERVICES}" == *"aws"* ]]; then
          gcloud run deploy ironfort-aws-compliance \
            --image=gcr.io/${PROJECT_ID}/steampipe-aws-compliance:latest \
            --region=${_REGION} \
            --platform=managed \
            --no-allow-unauthenticated \
            --memory=${_MEMORY} \
            --cpu=2 \
            --timeout=600 \
            --min-instances=${_MIN_INSTANCES} \
            --max-instances=${_MAX_INSTANCES} \
            --service-account=ironfort-compliance-sa@${PROJECT_ID}.iam.gserviceaccount.com \
            --set-env-vars=AUTH_TYPE=secret_manager,CLIENT_NAME=ironfort,DOMAIN=${_DOMAIN},GCP_PROJECT_ID=${PROJECT_ID},STEAMPIPE_UPDATE_CHECK=false,POWERPIPE_UPDATE_CHECK=false,SKIP_IAP_CHECK=true
        fi
    waitFor: ['push-images']

  # Deploy Azure Compliance
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'deploy-azure-compliance'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        if [[ "${_DEPLOY_SERVICES}" == "all" || "${_DEPLOY_SERVICES}" == *"azure"* ]]; then
          gcloud run deploy ironfort-azure-compliance \
            --image=gcr.io/${PROJECT_ID}/steampipe-azure-compliance:latest \
            --region=${_REGION} \
            --platform=managed \
            --no-allow-unauthenticated \
            --memory=${_MEMORY} \
            --cpu=2 \
            --timeout=600 \
            --min-instances=${_MIN_INSTANCES} \
            --max-instances=${_MAX_INSTANCES} \
            --service-account=ironfort-compliance-sa@${PROJECT_ID}.iam.gserviceaccount.com \
            --set-env-vars=AUTH_TYPE=secret_manager,CLIENT_NAME=ironfort,DOMAIN=${_DOMAIN},GCP_PROJECT_ID=${PROJECT_ID},STEAMPIPE_UPDATE_CHECK=false,POWERPIPE_UPDATE_CHECK=false,SKIP_IAP_CHECK=true
        fi
    waitFor: ['push-images']

  # Deploy Dashboard UI
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'deploy-dashboard-ui'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        # Get backend service URLs dynamically
        GCP_URL=$(gcloud run services describe ironfort-gcp-compliance --region=${_REGION} --format='value(status.url)')
        AWS_URL=$(gcloud run services describe ironfort-aws-compliance --region=${_REGION} --format='value(status.url)')
        AZURE_URL=$(gcloud run services describe ironfort-azure-compliance --region=${_REGION} --format='value(status.url)')
        
        echo "Backend URLs:"
        echo "GCP: $${GCP_URL}"
        echo "AWS: $${AWS_URL}"
        echo "Azure: $${AZURE_URL}"
        
        gcloud run deploy ironfort-dashboard-ui \
          --image=gcr.io/${PROJECT_ID}/dashboard-ui:latest \
          --region=${_REGION} \
          --platform=managed \
          --allow-unauthenticated \
          --memory=2Gi \
          --cpu=1 \
          --timeout=60 \
          --min-instances=${_MIN_INSTANCES} \
          --max-instances=${_MAX_INSTANCES} \
          --service-account=ironfort-compliance-sa@${PROJECT_ID}.iam.gserviceaccount.com \
          --set-env-vars=AUTH_TYPE=secret_manager,CLIENT_NAME=ironfort,GCP_PROJECT_ID=${PROJECT_ID},DOMAIN=${_DOMAIN},REACT_APP_ENVIRONMENT=production,BACKEND_GCP_URL=$${GCP_URL},BACKEND_AWS_URL=$${AWS_URL},BACKEND_AZURE_URL=$${AZURE_URL}
    waitFor: ['deploy-admin-api', 'deploy-gcp-compliance', 'deploy-aws-compliance', 'deploy-azure-compliance']

  # Print deployment summary
  - name: 'gcr.io/cloud-builders/gcloud'
    id: 'print-summary'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        echo "=== Deployment Summary ==="
        echo ""
        
        if [[ "${_DEPLOY_SERVICES}" == "all" || "${_DEPLOY_SERVICES}" == *"admin"* ]]; then
          ADMIN_URL=$(gcloud run services describe ironfort-admin-api --region=${_REGION} --format='value(status.url)' 2>/dev/null || echo "Not deployed")
          echo "Admin API: $${ADMIN_URL}"
        fi
        
        if [[ "${_DEPLOY_SERVICES}" == "all" || "${_DEPLOY_SERVICES}" == *"gcp"* ]]; then
          GCP_URL=$(gcloud run services describe ironfort-gcp-compliance --region=${_REGION} --format='value(status.url)' 2>/dev/null || echo "Not deployed")
          echo "GCP Compliance: $${GCP_URL}"
        fi
        
        if [[ "${_DEPLOY_SERVICES}" == "all" || "${_DEPLOY_SERVICES}" == *"aws"* ]]; then
          AWS_URL=$(gcloud run services describe ironfort-aws-compliance --region=${_REGION} --format='value(status.url)' 2>/dev/null || echo "Not deployed")
          echo "AWS Compliance: $${AWS_URL}"
        fi
        
        if [[ "${_DEPLOY_SERVICES}" == "all" || "${_DEPLOY_SERVICES}" == *"azure"* ]]; then
          AZURE_URL=$(gcloud run services describe ironfort-azure-compliance --region=${_REGION} --format='value(status.url)' 2>/dev/null || echo "Not deployed")
          echo "Azure Compliance: $${AZURE_URL}"
        fi
        
        echo ""
        
        DASHBOARD_URL=$(gcloud run services describe ironfort-dashboard-ui --region=${_REGION} --format='value(status.url)' 2>/dev/null || echo "Not deployed")
        echo "Dashboard UI: $${DASHBOARD_URL}"
        
        echo ""
        echo "Memory allocation: ${_MEMORY}"
        echo "Service account: ironfort-compliance-sa@${PROJECT_ID}.iam.gserviceaccount.com"
    waitFor: ['deploy-admin-api', 'deploy-gcp-compliance', 'deploy-aws-compliance', 'deploy-azure-compliance', 'deploy-dashboard-ui']

timeout: 1800s