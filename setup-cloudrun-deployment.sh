#!/bin/bash
# Setup script for Cloud Run deployment
# Creates necessary resources and configures the environment

set -e

echo "🚀 Setting up Cloud Run deployment prerequisites..."

# Configuration
PROJECT_ID=${PROJECT_ID:-"vratant-test-prj"}
REGION=${REGION:-"us-central1"}
REPO_NAME="steampipe-compliance"
SA_NAME="steampipe-sa"

echo "📋 Configuration:"
echo "  Project: $PROJECT_ID"
echo "  Region: $REGION"
echo "  Repository: $REPO_NAME"
echo ""

# Set project
gcloud config set project $PROJECT_ID

# 1. Enable required APIs
echo "🔧 Enabling required APIs..."
gcloud services enable \
  cloudbuild.googleapis.com \
  run.googleapis.com \
  secretmanager.googleapis.com \
  cloudresourcemanager.googleapis.com \
  containerregistry.googleapis.com \
  artifactregistry.googleapis.com

# 2. Create service account if it doesn't exist
echo "👤 Setting up service account..."
if ! gcloud iam service-accounts describe $SA_NAME@$PROJECT_ID.iam.gserviceaccount.com &>/dev/null; then
  gcloud iam service-accounts create $SA_NAME \
    --display-name="Steampipe Service Account" \
    --description="Service account for Steampipe compliance services"
fi

# 3. Grant necessary roles to service account
echo "🔐 Granting IAM roles..."
gcloud projects add-iam-policy-binding $PROJECT_ID \
  --member="serviceAccount:$SA_NAME@$PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/secretmanager.secretAccessor"

gcloud projects add-iam-policy-binding $PROJECT_ID \
  --member="serviceAccount:$SA_NAME@$PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/logging.logWriter"

gcloud projects add-iam-policy-binding $PROJECT_ID \
  --member="serviceAccount:$SA_NAME@$PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/cloudtrace.agent"

# Grant Cloud Build permissions
gcloud projects add-iam-policy-binding $PROJECT_ID \
  --member="serviceAccount:$(gcloud projects describe $PROJECT_ID --format='value(projectNumber)')@cloudbuild.gserviceaccount.com" \
  --role="roles/run.admin"

gcloud projects add-iam-policy-binding $PROJECT_ID \
  --member="serviceAccount:$(gcloud projects describe $PROJECT_ID --format='value(projectNumber)')@cloudbuild.gserviceaccount.com" \
  --role="roles/iam.serviceAccountUser"

# 4. Create sample secrets (if they don't exist)
echo "🔑 Creating sample secrets in Secret Manager..."

# Check and create GCP service account secret
if ! gcloud secrets describe steampipe-gcp-sa-key &>/dev/null; then
  echo '{"type": "service_account", "project_id": "'$PROJECT_ID'"}' | \
    gcloud secrets create steampipe-gcp-sa-key --data-file=-
fi

# Check and create AWS credentials secret
if ! gcloud secrets describe steampipe-aws-credentials &>/dev/null; then
  echo '[default]
aws_access_key_id = YOUR_ACCESS_KEY
aws_secret_access_key = YOUR_SECRET_KEY' | \
    gcloud secrets create steampipe-aws-credentials --data-file=-
fi

# Check and create Azure credentials secret
if ! gcloud secrets describe steampipe-azure-credentials &>/dev/null; then
  echo '{
  "clientId": "YOUR_CLIENT_ID",
  "clientSecret": "YOUR_CLIENT_SECRET",
  "subscriptionId": "YOUR_SUBSCRIPTION_ID",
  "tenantId": "YOUR_TENANT_ID"
}' | \
    gcloud secrets create steampipe-azure-credentials --data-file=-
fi

# 5. Create Cloud Source Repository (if using)
echo "📦 Setting up source repository..."
if ! gcloud source repos describe $REPO_NAME &>/dev/null; then
  gcloud source repos create $REPO_NAME
fi

# 6. Create build trigger
echo "🔨 Creating Cloud Build trigger..."
cat > trigger-config.yaml <<EOF
name: steampipe-deploy-trigger
description: Deploy Steampipe compliance services to Cloud Run
github:
  owner: YOUR_GITHUB_OWNER
  name: steampipe-compliance-unified
  push:
    branch: ^main$
filename: cloudbuild-deploy.yaml
substitutions:
  _DOMAIN: steampipe-compliance.com
  _REGION: $REGION
EOF

echo ""
echo "✅ Prerequisites setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Update the secrets with real credentials:"
echo "   - gcloud secrets versions add steampipe-gcp-sa-key --data-file=path/to/sa-key.json"
echo "   - gcloud secrets versions add steampipe-aws-credentials --data-file=path/to/aws-creds"
echo "   - gcloud secrets versions add steampipe-azure-credentials --data-file=path/to/azure-creds"
echo ""
echo "2. Create the build trigger:"
echo "   - Go to https://console.cloud.google.com/cloud-build/triggers?project=$PROJECT_ID"
echo "   - Connect your repository and create trigger using cloudbuild-deploy.yaml"
echo ""
echo "3. Test the deployment:"
echo "   - gcloud builds submit --config=cloudbuild-deploy.yaml --substitutions=_DOMAIN=your-domain.com"
echo ""
echo "🎉 Ready to deploy!"