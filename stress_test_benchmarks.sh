#!/bin/bash
# Stress test with multiple benchmarks running simultaneously

echo "Starting stress test at $(date)"

# Function to run random benchmarks
run_random_tests() {
    local cloud=$1
    local endpoint=$2
    local secret=$3
    local identifier_key=$4
    
    # Arrays of test values
    local identifiers=("all-projects" "all-accounts" "all-subscriptions" "test-123" "prod-456" "dev-789")
    local aws_benchmarks=("cis_v300" "cis_v200" "cis_v150" "foundational_security" "nist_800_53_rev_5")
    local azure_benchmarks=("cis_v210" "cis_v200" "cis_v130" "hipaa_hitrust_v92" "nist_sp_800_53_rev_5")
    local gcp_benchmarks=("cis_v300" "cis_v200" "cis_v120" "nist_800_53_rev_5" "nist_csf_v10")
    
    # Select random values
    local identifier=${identifiers[$RANDOM % ${#identifiers[@]}]}
    
    case $cloud in
        aws)
            local benchmark=${aws_benchmarks[$RANDOM % ${#aws_benchmarks[@]}]}
            ;;
        azure)
            local benchmark=${azure_benchmarks[$RANDOM % ${#azure_benchmarks[@]}]}
            ;;
        gcp)
            local benchmark=${gcp_benchmarks[$RANDOM % ${#gcp_benchmarks[@]}]}
            ;;
    esac
    
    echo "Running $cloud/$benchmark with $identifier_key=$identifier"
    
    curl -s -X POST "$endpoint/api/$cloud/benchmark" \
        -H "Content-Type: application/json" \
        -d "{\"secret_reference\": \"$secret\", \"benchmark\": \"$benchmark\", \"$identifier_key\": \"$identifier\"}" > /dev/null &
}

# Run tests for 30 minutes
end_time=$(($(date +%s) + 1800))

while [ $(date +%s) -lt $end_time ]; do
    echo -e "\n=== Running batch at $(date) ==="
    
    # Run 3 tests for each cloud in parallel
    for i in {1..3}; do
        run_random_tests "aws" "http://localhost:8082" "aws-local-secops" "account_id"
        run_random_tests "azure" "http://localhost:8083" "azure-local-secops" "subscription_id"
        run_random_tests "gcp" "http://localhost:8080" "gcp-local-secops" "project_id"
    done
    
    # Wait a bit before next batch
    sleep 30
    
    # Check for errors periodically
    echo -e "\nChecking for errors..."
    
    # AWS errors
    aws_errors=$(curl -s http://localhost:8082/api/aws/jobs 2>/dev/null | grep -o '"error":[^,}]*' | wc -l)
    echo "AWS jobs with errors: $aws_errors"
    
    # Azure errors
    azure_errors=$(curl -s http://localhost:8083/api/azure/jobs 2>/dev/null | grep -o '"error":[^,}]*' | wc -l)
    echo "Azure jobs with errors: $azure_errors"
    
    # GCP errors
    gcp_errors=$(curl -s http://localhost:8080/api/gcp/jobs 2>/dev/null | grep -o '"error":[^,}]*' | wc -l)
    echo "GCP jobs with errors: $gcp_errors"
    
    sleep 30
done

echo -e "\nStress test completed at $(date)"