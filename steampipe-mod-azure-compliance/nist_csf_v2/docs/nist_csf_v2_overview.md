# NIST Cybersecurity Framework (CSF) 2.0 Overview

To obtain the latest version of the official guide, please visit https://www.nist.gov/cyberframework/framework.

## Overview

NIST Cybersecurity Framework (CSF) 2.0 provides a comprehensive set of best practices, standards, and recommendations to help organizations manage and reduce cybersecurity risk. The framework is composed of six core functions that organize essential cybersecurity activities at their highest level.

## Functions

### Govern (GV)

Establish, communicate, and monitor the organization's cybersecurity risk management strategy, expectations, and policy.

The Govern Function provides outcomes to inform what an organization may do to achieve and prioritize the outcomes of the other five Functions in the context of its mission and stakeholder expectations. Example outcome Categories within this Function include: Organizational Context; Risk Management Strategy; Roles, Responsibilities, and Authorities; Policy; Oversight; and Cybersecurity Supply Chain Risk Management.

### Identify (ID)

Understand the organization's current cybersecurity risks, assets, and related resources.

The Identify Function enables organizations to understand the business context, resources, and related cybersecurity risks, forming the foundation for effective risk management. Example outcome Categories within this Function include: Asset Management; Risk Assessment; and Improvement.

### Protect (PR)

Develop and implement appropriate safeguards to ensure delivery of critical services and manage cybersecurity risks.

The Protect Function supports the ability to secure assets and limit or contain the impact of potential cybersecurity events. Example outcome Categories within this Function include: Identity Management, Authentication, and Access Control; Awareness and Training; Data Security; Platform Security; and Technology Infrastructure Resilience.

### Detect (DE)

Develop and implement activities to discover the occurrence of cybersecurity events.

The Detect Function enables timely discovery and analysis of anomalies, indicators of compromise, and other potentially adverse events. Example outcome Categories within this Function include: Continuous Monitoring and Adverse Event Analysis.

### Respond (RS)

Take appropriate actions regarding detected cybersecurity incidents.

The Respond Function supports the ability to contain the effects of cybersecurity incidents and manage response activities. Example outcome Categories within this Function include: Incident Management; Incident Analysis; Incident Response Reporting and Communication; and Incident Mitigation.

### Recover (RC)

Restore assets and operations affected by cybersecurity incidents.

The Recover Function supports timely restoration of normal operations and communication during recovery efforts. Example outcome Categories within this Function include: Incident Recovery Plan Execution and Incident Recovery Communication. 