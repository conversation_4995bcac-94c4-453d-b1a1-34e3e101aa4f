## Description

Network security groups should be periodically evaluated for port misconfigurations. Where RDP is not explicitly required and narrowly configured for resources attached to a network security group, Internet-level access to Azure resources should be restricted or eliminated.

The potential security problem with using RDP over the Internet is that attackers can use various brute force techniques to gain access to Azure Virtual Machines. Once the attackers gain access, they can use a virtual machine as a launch point for compromising other machines on an Azure Virtual Network or even attack networked devices outside of Azure.

## Remediation

### From Azure Portal

1. Go to `Network security groups`.
2. Under `Settings`, click `Inbound security rules`.
3. Check the box next to any inbound security rule matching:
 - Port: `3389` or range including 3389
 - Protocol: `TCP` or `Any`
 - Source: `0.0.0.0/0`, `Internet`, or `Any`
 - Action: `Allow`
4. Click `Delete`.
5. Click `Yes`.

### From Azure CLI

For each network security group rule requiring remediation, run the following command to delete a rule:

```bash
az network nsg rule delete --resource-group <resource-group> --nsg-name <network-security-group> --name <rule>
```

### Default Value

By default, RDP access from internet is not `enabled`.
