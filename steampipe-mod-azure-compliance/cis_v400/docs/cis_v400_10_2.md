## Overview

This section covers security best practice recommendations for Azure Blob Storage. Azure Blob Storage is a core storage service type for Azure Storage Accounts. Azure Data Lake services depend on the Azure Blob Service.

**NOTE:** If your organization is using Shared Access Signature (SAS) tokens, please review the CIS Microsoft Azure Storage Services Benchmark for best practice guidance on the configuration and use of those tokens.

**Help us improve this Benchmark!**
If you notice a needed correction, want to provide feedback, or wish to contribute security best practice guidance please join our community and create a ticket, propose a change, or start a discussion so we can improve this guidance!

The CIS Microsoft Azure Community is here: [https://workbench.cisecurity.org/communities/72](https://workbench.cisecurity.org/communities/72)

-----

### Resources for Azure Blob Storage

**Azure Product Page**:
- [https://azure.microsoft.com/en-us/products/storage/blobs/](https://azure.microsoft.com/en-us/products/storage/blobs/)

**Azure Blob Storage service overview**:
- [https://learn.microsoft.com/en-us/azure/storage/blobs/storage-blobs-overview](https://learn.microsoft.com/en-us/azure/storage/blobs/storage-blobs-overview)

**Microsoft Cloud Security Baseline for Storage**:
- [https://learn.microsoft.com/en-us/security/benchmark/azure/baselines/storage-security-baseline](https://learn.microsoft.com/en-us/security/benchmark/azure/baselines/storage-security-baseline)
