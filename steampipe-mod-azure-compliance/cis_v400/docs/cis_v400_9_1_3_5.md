## Description

File Integrity Monitoring (FIM) is a feature that monitors critical system files in Windows or Linux for potential signs of attack or compromise.

FIM provides a detection mechanism for compromised files. When FIM is enabled, critical system files are monitored for changes that might indicate a threat actor is attempting to modify system files for lateral compromise within a host operating system.

## Remediation

### From Azure Portal

1. From the Azure Portal `Home` page, select `Microsoft Defender for Cloud`.
2. Under `Management` select `Environment Settings`.
3. Select a subscription.
4. Under `Settings` > `Defender Plans`, click `Settings & monitoring`.
5. Under the Component column, locate the row for `File Integrity Monitoring`.
6. Select `On`.
7. Click `Continue` in the top left.

Repeat the above for any additional subscriptions.

### Default Value

By default, File Integrity Monitoring is `Off`.
