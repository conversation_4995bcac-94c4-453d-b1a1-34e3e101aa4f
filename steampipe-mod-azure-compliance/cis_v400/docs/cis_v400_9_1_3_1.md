## Description

The Defender for Servers plan in Microsoft Defender for Cloud reduces security risk by providing actionable recommendations to improve and remediate machine security posture. Defender for Servers also helps to protect machines against real-time security threats and attacks.

Defender for Servers offers two paid plans:

**Plan 1**

The following components are enabled by default:

- Log Analytics agent (deprecated)
- Endpoint protection

Plan 1 also offers the following components, disabled by default:

- Vulnerability assessment for machines
- Guest Configuration agent (preview)

**Plan 2**

The following components are enabled by default:

- Log Analytics agent (deprecated)
- Vulnerability assessment for machines
- Endpoint protection
- Agentless scanning for machines

Plan 2 also offers the following components, disabled by default:

- Guest Configuration agent (preview)
- File Integrity Monitoring.

Enabling Defender for Servers allows for greater defense-in-depth, with threat detection provided by the Microsoft Security Response Center (MSRC).

## Remediation

### From Azure Portal

1. Go to `Microsoft Defender for Cloud`.
2. Under `Management`, select `Environment settings`.
3. Click on a subscription name.
4. Click `Defender plans` in the left pane.
5. Under `Cloud Workload Protection (CWP)`, locate `Servers` in the Plan column, set Status to `On`.
6. Select `Save`.
7. Repeat steps 1-6 for each subscription requiring remediation.

### From Azure CLI

Run the following command:

```bash
az security pricing create -n VirtualMachines --tier 'standard'
```

### From PowerShell

Run the following command:

```bash
Set-AzSecurityPricing -Name 'VirtualMachines' -PricingTier 'Standard'
```

### Default Value

By default, the Defender for Servers plan is disabled.
