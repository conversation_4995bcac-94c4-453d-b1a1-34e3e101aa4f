## Overview

**SERVICE CATEGORY BENCHMARK AVAILABLE**
- "CIS Microsoft Azure Storage Services Benchmark"

To better understand the relationship between the Foundations Benchmark and Services Benchmarks, please read the "Introduction" section of this document.

`PARTIAL RELOCATION - BE ADVISED!`

Some recommendations previously covered in the CIS Microsoft Azure Foundations Benchmark (this document) related to Storage services have been relocated to a Benchmark titled **CIS Microsoft Azure Storage Services Benchmark**. This Storage Services section now provides a **foundational set** of secure configuration recommendations for products from Azure Product Directory's "Storage" category of services.

After applying foundational recommendations, take inventory of the entire set of Storage Services in use by your organization, then look to the Benchmarks section of the CIS Microsoft Azure Community (https://workbench.cisecurity.org/communities/72) for defense-in-depth guidance for those specific services in the **CIS Microsoft Azure Storage Services Benchmark**.

Services addressed in the **CIS Microsoft Azure Storage Services Benchmark**:
- Archive Storage
- Azure Backup
- Azure Blob Storage
- Azure Confidential Ledger
- Azure Container Storage
- Azure Data Box
- Azure Data Lake Storage
- Azure Data Share
- Azure Disk Storage
- Azure Elastic SAN
- Azure Files
- Azure Managed Lustre
- Azure NetApp Files
- Azure Storage Actions
- Queue Storage
- Storage Accounts
- Storage Explorer

Azure Product Directory Reference: [https://azure.microsoft.com/en-us/products#storage](https://azure.microsoft.com/en-us/products#storage)

**FEEDBACK REQUEST:** Is there a specific service or recommendation in this section that you'd like to see addressed or improved? Let us know by making a ticket or starting a discussion in the CIS Microsoft Azure Community (https://workbench.cisecurity.org/communities/72).
