## Overview

**IMPORTANT: READ BELOW BEFORE PROCEEDING!**

-----
- If your organization pays for Microsoft Entra ID licensing (included in Microsoft 365 E3, E5, F5, or Business Premium, and EM&S E3 or E5 licenses) and **CAN** use Conditional Access, ignore the recommendations in this section and proceed to the Conditional Access section.

- If your organization is using the free tier of Entra ID (Office 365 E1, E3, or E5, and Microsoft 365 F1 or F3 licenses) and **CAN NOT** use Conditional Access, proceed with the Security Defaults guidance in this section, and ignore the recommendations in the Conditional Access section.

Conditional Access is preferred, but Security Defaults (Per-User MFA) is recommended only if Conditional Access isn't available.

Why is this **IMPORTANT**?

The Azure "Security Defaults" recommendations represent an entry-level set of recommendations (such as Multi-Factor Authentication) which will be relevant to organizations and tenants that are either just starting to use Azure, or are only utilizing a bare minimum feature set, and rely on the free license tier of Microsoft Entra ID. Security Defaults recommendations are intended to ensure that these use cases are still capable of establishing a strong baseline of secure configuration.

**If your subscription is licensed to use Microsoft Entra ID P1 or P2, it is strongly recommended that the "Security Defaults" section (this section and the recommendations therein) be bypassed in favor of the use of "Conditional Access."**

-----
**IMPORTANT: READ ABOVE BEFORE PROCEEDING!**.
