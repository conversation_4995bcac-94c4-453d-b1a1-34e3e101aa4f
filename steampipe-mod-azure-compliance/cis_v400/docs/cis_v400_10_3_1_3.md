## Description

Every secure request to an Azure Storage account must be authorized. By default, requests can be authorized with either Microsoft Entra credentials or by using the account access key for Shared Key authorization.

Microsoft Entra ID provides superior security and ease of use compared to Shared Key and is recommended by Microsoft. To require clients to use Microsoft Entra ID for authorizing requests, you can disallow requests to the storage account that are authorized with Shared Key.

## Remediation

### From Azure Portal

1. Go to `Storage accounts`.
2. Click on a storage account.
3. Under `Settings`, click `Configuration`.
4. Under `Allow storage account key access`, click the radio button next to `Disabled`.
5. Click `Save`.
6. Repeat steps 1-5 for each storage account requiring remediation.

### From Azure CLI

For each storage account requiring remediation, run the following command to disallow shared key authorization:

```bash
az storage account update --resource-group <resource-group> --name <storage-account> --allow-shared-key-access false
```

### From PowerShell

For each storage account requiring remediation, run the following command to disallow shared key authorization:

```bash
Set-AzStorageAccount -ResourceGroupName <resource-group> -Name <storage-account> -AllowSharedKeyAccess $false
```

### Default Value

The AllowSharedKeyAccess property of a storage account is not set by default and does not return a value until you explicitly set it. The storage account permits requests that are authorized with the Shared Key when the property value is **null** or when it is **true**.
