## Description

An organization's attack surface is the collection of assets with a public network identifier or URI that an external threat actor can see or access from outside your cloud. It is the set of points on the boundary of a system, a system element, system component, or an environment where an attacker can try to enter, cause an effect on, or extract data from, that system, system element, system component, or environment. The larger the attack surface, the harder it is to protect.

This tool can be configured to scan your organization's online infrastructure such as specified domains, hosts, CIDR blocks, and SSL certificates, and store them in an Inventory. Inventory items can be added, reviewed, approved, and removed, and may contain enrichments ("insights") and additional information collected from the tool's different scan engines and open-source intelligence sources.

A Defender EASM workspace will generate an Inventory of publicly exposed assets by crawling and scanning the internet using _Seeds_ you provide when setting up the tool. Seeds can be FQDNs, IP CIDR blocks, and WHOIS records.

Defender EASM will generate Insights within 24-48 hours after Seeds are provided, and these insights include vulnerability data (CVEs), ports and protocols, and weak or expired SSL certificates that could be used by an attacker for reconnaissance or exploitation.

Results are classified High/Medium/Low and some of them include proposed mitigations.

This tool can monitor the externally exposed resources of an organization, provide valuable insights, and export these findings in a variety of formats (including CSV) for use in vulnerability management operations and red/purple team exercises.

## Remediation

### From Azure Portal

1. Go to `Microsoft Defender EASM`.
2. Click `+ Create`.
3. Under `Project details`, select a subscription.
4. Select or create a resource group.
5. Under `Instance details`, enter a name for the workspace.
6. Select a region.
7. Click `Review + create`.
8. Click `Create`.
9. Once the deployment has completed, go to `Microsoft Defender EASM`.
10. Click the workspace name.
11. Configure the workspace appropriately for your environment and organization.

### Default Value

Microsoft Defender EASM is an optional, paid Azure Resource that must be created and configured inside a Subscription and Resource Group.
