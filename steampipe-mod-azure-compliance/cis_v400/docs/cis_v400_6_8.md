## Description

Microsoft Azure applies a default global banned password list to all user and admin accounts that are created and managed directly in Microsoft Entra ID.

The Microsoft Entra password policy does not apply to user accounts that are synchronized from an on-premises Active Directory environment, unless Microsoft Entra ID Connect is used and `EnforceCloudPasswordPolicyForPasswordSyncedUsers` is enabled.

Review the `Default Value` section for more detail on the password policy.

For increased password security, a custom banned password list is recommended.

Implementing a custom banned password list gives your organization further control over the password policy. Disallowing easy-to-guess passwords increases the security of your Azure resources.

## Remediation

### From Azure Portal

1. From Azure Home select the Portal Menu.
2. Select `Microsoft Entra ID`.
3. Under `Manage`, select `Security`.
4. Under `Manage`, select `Authentication methods`.
5. Under `Manage`, select `Password protection`.
6. Set the `Enforce custom list` option to `Yes`.
7. Click in the `Custom banned password list` text box.
8. Add a list of words, one per line, to prevent users from using in passwords.
9. Click `Save`.

### Default Value

By default the custom banned password list is not 'Enabled'.
Organization-specific terms can be added to the custom banned password list, such as the following examples:

- Brand names
- Product names
- Locations, such as company headquarters
- Company-specific terms
- Abbreviations that have specific company meaning
- Months and weekdays with your company's local languages

The default global banned password list is already applied to your resources which applies the following basic requirements:

**Characters allowed:**

- Uppercase characters (A - Z)
- Lowercase characters (a - z)
- Numbers (0 - 9)
- Symbols:
- @ # $ % ^ & * - _ ! + = [ ] { } | \ : ' , . ? / ` ~ " ( ) ; < >
- blank space

**Characters not allowed:**

- Unicode characters

**Password length:**

Passwords require:
- A minimum of eight characters
- A maximum of 256 characters

**Password complexity:**

Passwords require three out of four of the following categories:
- Uppercase characters
- Lowercase characters
- Numbers
- Symbols

Note: Password complexity check isn't required for Education tenants.

**Password not recently used:**

- When a user changes or resets their password, the new password can't be the same as the current or recently used passwords.
- Password isn't banned by Entra ID Password Protection.
- The password can't be on the global list of banned passwords for Azure AD Password Protection, or on the customizable list of banned passwords specific to your organization.

**Evaluation**

New passwords are evaluated for strength and complexity by validating against the combined list of terms from the global and custom banned password lists. Even if a user's password contains a banned password, the password may be accepted if the overall password is otherwise strong enough.
