## Overview

Defender for APIs in Microsoft Defender for Cloud offers full lifecycle protection, detection, and response coverage for APIs published in Azure API Management.
Defender for APIs helps you to gain visibility into business-critical APIs. You can investigate and improve your API security posture, prioritize vulnerability fixes, and quickly detect active real-time threats.
Defender for APIs requires additional configuration in the Microsoft API portal.

Note: There is a cost attached to using Defender for API.
