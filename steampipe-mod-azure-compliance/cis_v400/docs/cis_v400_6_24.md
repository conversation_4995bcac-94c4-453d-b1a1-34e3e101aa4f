## Description

Resource locking is a powerful protection mechanism that can prevent inadvertent modification or deletion of resources within Azure subscriptions and resource groups, and it is a recommended NIST configuration.

Given that the resource lock functionality is outside of standard Role-Based Access Control (RBAC), it would be prudent to create a resource lock administrator role to prevent inadvertent unlocking of resources.

## Remediation

### From Azure Portal

1. In the Azure portal, navigate to a subscription or resource group.
2. Click `Access control (IAM)`.
3. <PERSON>lick `+ Add`.
4. <PERSON>lick `Add custom role`.
5. In the `Custom role name` field enter `Resource Lock Administrator`.
6. In the `Description` field enter `Can Administer Resource Locks`.
7. For `Baseline permissions` select `Start from scratch`.
8. <PERSON>lick `Next`.
9. <PERSON>lick `Add permissions`.
10. In the `Search for a permission` box, type `Microsoft.Authorization/locks`.
11. Click the result.
12. Check the box next to `Permission`.
13. <PERSON>lick `Add`.
14. Click `Review + create`.
15. Click `Create`.
16. Click `OK`.
17. <PERSON>lick `+ Add`.
18. Click `Add role assignment`.
19. In the `Search by role name, description, permission, or ID` box, type `Resource Lock Administrator`.
20. Select the role.
21. <PERSON>lick `Next`.
22. Click `+ Select members`.
23. Select appropriate members.
24. <PERSON>lick `Select`.
25. Click `Review + assign`.
26. Click `Review + assign` again.
27. Repeat steps 1-26 for each subscription or resource group requiring remediation.

### From PowerShell

Below is a PowerShell definition for a resource lock administrator role created at an Azure Management group level

```bash
Import-Module Az.Accounts
Connect-AzAccount

$role = Get-AzRoleDefinition "User Access Administrator"
$role.Id = $null
$role.Name = "Resource Lock Administrator"
$role.Description = "Can Administer Resource Locks"
$role.Actions.Clear()
$role.Actions.Add("Microsoft.Authorization/locks/*")
$role.AssignableScopes.Clear()

* Scope at the Management group level Management group

$role.AssignableScopes.Add("/providers/Microsoft.Management/managementGroups/MG-Name")

New-AzRoleDefinition -Role $role
Get-AzureRmRoleDefinition "Resource Lock Administrator"
```

### Default Value

A role for administering resource locks does not exist by default.
