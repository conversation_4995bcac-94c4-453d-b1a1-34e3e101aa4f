## Description

Restrict security group management to administrators only.

Restricting security group management to administrators only prohibits users from making changes to security groups. This ensures that security groups are appropriately managed and their management is not delegated to non-administrators.

## Remediation

### From Azure Portal

1. From Azure Home select the Portal Menu.
2. Select `Microsoft Entra ID`.
3. Under `Manage`, select `Groups`.
4. Under `Settings`, select `General`.
5. Under `Self Service Group Management`, set `Owners can manage group membership requests in My Groups` to `No`.
6. Click `Save`.

### Default Value

By default, `Owners can manage group membership requests in My Groups` is set to `No`.
