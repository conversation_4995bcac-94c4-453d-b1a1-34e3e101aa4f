## Description

Microsoft Entra ID has native and extended identity functionality allowing you to invite people from outside your organization to be guest users in your cloud account and sign in with their own work, school, or social identities.

Guest users are typically added outside your employee on-boarding/off-boarding process and could potentially be overlooked indefinitely. To prevent this, guest users should be reviewed on a regular basis. During this audit, guest users should also be determined to not have administrative privileges.

## Remediation

### From Azure Portal

1. From Azure Home select the Portal Menu.
2. Select `Microsoft Entra ID`.
3. Under `Manage`, select `Users`.
4. Click on `Add filter`.
5. Select `User type`.
6. Select `Guest` from the Value dropdown.
7. Click `Apply`.
8. Check the box next to all `Guest` users that are no longer required or are inactive.
9. Click `Delete`.
10. Click `OK`.

### From Azure CLI

Before deleting the user, set it to inactive using the ID from the Audit Procedure to determine if there are any dependent systems.

```bash
az ad user update --id <<EMAIL>> --account-enabled {false}
```

After determining that there are no dependent systems, delete the user.

```bash
Remove-AzureADUser -ObjectId <<EMAIL>>
```

### From Azure PowerShell

Before deleting the user, set it to inactive using the ID from the Audit Procedure to determine if there are any dependent systems.

```bash
Set-AzureADUser -ObjectId "<<EMAIL>>" -AccountEnabled false
```

After determining that there are no dependent systems, delete the user.

```bash
PS C:\>Remove-AzureADUser -ObjectId <<EMAIL>>
```

### Default Value

By default no guest users are created.
