## Description

The Azure Storage setting ‘Allow Blob Anonymous Access’ (aka "allowBlobPublicAccess") controls whether anonymous access is allowed for blob data in a storage account. When this property is set to True, it enables public read access to blob data, which can be convenient for sharing data but may carry security risks. When set to False, it disallows public access to blob data, providing a more secure storage environment.

If "Allow Blob Anonymous Access" is enabled, blobs can be accessed by adding the blob name to the URL to see the contents. An attacker can enumerate a blob using methods, such as brute force, and access them.

Exfiltration of data by brute force enumeration of items from a storage account may occur if this setting is set to 'Enabled'.

## Remediation

### From Azure Portal

1. Go to `Storage Accounts`.
2. For each storage account, under `Settings`, click `Configuration`.
3. Set `Allow Blob Anonymous Access` to `Disabled`.
4. Click `Save`.

### From Powershell

For every storage account in scope, run the following:

```bash
$storageAccount = Get-AzStorageAccount -ResourceGroupName "<yourResourceGroup>" -Name "<yourStorageAccountName>"
$storageAccount.AllowBlobPublicAccess = $false
Set-AzStorageAccount -InputObject $storageAccount
```

### Default Value

Disabled
