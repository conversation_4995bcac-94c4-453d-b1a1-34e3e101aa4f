## Description

Databricks personal access tokens (PATs) provide API-based authentication for users and applications. By default, users can generate API tokens without expiration, leading to potential security risks if tokens are leaked, improperly stored, or not rotated regularly.

To mitigate these risks, administrators should:
* Restrict token creation to approved users and service principals.
* Enforce expiration policies to prevent long-lived tokens.
* Monitor token usage and revoke unused or compromised tokens.

Restricting usage and enforcing expiry for personal access tokens reduces exposure to long-lived tokens, minimizes the risk of API abuse if compromised, and aligns with security best practices through controlled issuance and enforced expiry.

## Remediation

### From Azure Portal

Disable personal access tokens:

If your workspace does not require PATs, you can disable them entirely to prevent their use.​

1. Navigate to your Azure Databricks workspace.
2. Click the `Settings` icon and select `Admin Console`.
3. Go to the `Advanced` tab.
4. Under `Personal Access Tokens`, toggle the setting to `Disabled`.

Databricks CLI:

```bash
databricks workspace-conf set-status --json '{"enableTokens": "false"}'
```

Control who can create and use personal access tokens:

Define which users or groups are authorized to create and utilize PATs.​

1. Navigate to your Azure Databricks workspace.
2. Click the `Settings` icon and select `<PERSON>min Console`.
3. Go to the `Advanced` tab.
4. Click on `Personal Access Tokens` and then `Permissions`.
5. Assign the appropriate permissions (e.g. No Permissions, Can Use, Can Manage) to users or groups.

Set maximum lifetime for new personal access tokens:

Limit the validity period of new tokens to reduce potential misuse.​

Databricks CLI:

```bash
databricks workspace-conf set-status --json '{"maxTokenLifetimeDays": "90"}'
```

Monitor and revoke personal access tokens:

Periodically review active tokens and revoke any that are unnecessary or potentially compromised.​

Databricks CLI:

```bash
databricks token list
```

```bash
databricks token delete --token-id <token-id>
```

Transition to OAuth for enhanced security:

Utilize OAuth tokens for authentication, offering improved security features over PATs.

### Default Value

By default, personal access tokens are enabled and users can create the Personal access token and their expiry time.
