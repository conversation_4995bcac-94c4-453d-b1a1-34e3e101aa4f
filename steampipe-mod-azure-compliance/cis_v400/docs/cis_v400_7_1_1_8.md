## Description

Ensure that a Microsoft Entra diagnostic setting is configured to send Microsoft Graph activity logs to a suitable destination, such as a Log Analytics workspace, storage account, or event hub. This enables centralized monitoring and analysis of all HTTP requests that the Microsoft Graph service receives and processes for a tenant.

Microsoft Graph activity logs provide visibility into HTTP requests made to the Microsoft Graph service, helping detect unauthorized access, suspicious activity, and security threats. Configuring diagnostic settings in Microsoft Entra ensures these logs are collected and sent to an appropriate destination for monitoring, analysis, and retention.

## Remediation

### From Azure Portal

1. Go to `Microsoft Entra ID`.
2. Under `Monitoring`, click `Diagnostic settings`.
3. Click `+ Add diagnostic setting`.
4. Provide a `Diagnostic setting name`.
5. Under `Logs > Categories`, check the box next to `MicrosoftGraphActivityLogs`.
6. Configure an appropriate destination for the logs.
7. Click `Save`.

### Default Value

By default, Microsoft Entra diagnostic settings do not exist.
