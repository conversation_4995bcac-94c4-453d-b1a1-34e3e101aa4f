## Description

Ensure that network flow logs are captured and fed into a central log analytics workspace.

**Retirement Notice**

On September 30, 2027, network security group (NSG) flow logs will be retired. Starting June 30, 2025, it will no longer be possible to create new NSG flow logs. Azure recommends migrating to virtual network flow logs. Review [https://azure.microsoft.com/en-gb/updates?id=Azure-NSG-flow-logs-Retirement](https://azure.microsoft.com/en-gb/updates?id=Azure-NSG-flow-logs-Retirement) for more information.

For virtual network flow logs, consider applying the recommendation `Ensure that virtual network flow logs are captured and sent to Log Analytics` in this section.

Network Flow Logs provide valuable insight into the flow of traffic around your network and feed into both Azure Monitor and Azure Sentinel (if in use), permitting the generation of visual flow diagrams to aid with analyzing for lateral movement, etc.

## Remediation

### From Azure Portal

1. Navigate to `Network Watcher`.
2. Under `Logs`, select `Flow logs`.
3. Select `+ Create`.
4. Select the desired Subscription.
5. For `Flow log type`, select `Network security group`.
6. Select `+ Select target resource`.
7. Select `Network security group`.
8. Select a network security group.
9. Click `Confirm selection`.
10. Select or create a new Storage Account.
11. If using a v2 storage account, input the retention in days to retain the log.
12. Click `Next`.
13. Under `Analytics`, for `Flow log version`, select `Version 2`.
14. Check the box next to `Enable traffic analytics`.
15. Select a processing interval.
16. Select a `Log Analytics Workspace`.
17. Select `Next`.
18. Optionally add Tags.
19. Select `Review + create`.
20. Select `Create`.

***Warning***
The remediation policy creates remediation deployment and names them by concatenating the subscription name and the resource group name. The MAXIMUM permitted length of a deployment name is 64 characters. Exceeding this will cause the remediation task to fail.

### Default Value

By default Network Security Group logs are not sent to Log Analytics.
