## Overview

**SERVICE CATEGORY BENCHMARK AVAILABLE**:
- "CIS Microsoft Azure Database Services Benchmark"

To better understand the relationship between the Foundations Benchmark and Services Benchmarks, please read the "Introduction" section of this document.

`FULL RELOCATION - BE ADVISED!`

**- ALL -** recommendations previously covered in the CIS Microsoft Azure Foundations Benchmark (this document) related to products in the Database category of services have been relocated to a Benchmark titled **CIS Microsoft Azure Database Services Benchmark**. This section now serves only as a reference.

Services addressed in the **CIS Microsoft Azure Database Services Benchmark**:
- Azure Cache for Redis
- Azure Cosmos DB
- Azure Data Factory
- Azure Database for MariaDB
- Azure Database for MySQL
- Azure Database for PostgreSQL
- Azure Database Migration Service
- Azure SQL
- Azure SQL Database
- Azure SQL Edge
- Azure SQL Managed Instance
- SQL Server on Azure Virtual Machines
- Table Storage
- Azure Managed Instance for Apache Cassandra
- Azure confidential ledger

Azure Product Directory Reference: [https://azure.microsoft.com/en-us/products#databases](https://azure.microsoft.com/en-us/products#databases)

**FEEDBACK REQUEST:** Is there a specific service or recommendation in this section that you'd like to see addressed or improved? Let us know by making a ticket or starting a discussion in the CIS Microsoft Azure Community (https://workbench.cisecurity.org/communities/72).
