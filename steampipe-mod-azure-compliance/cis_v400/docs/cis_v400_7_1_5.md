## Description

The use of Basic or Free SKUs in Azure whilst cost effective have significant limitations in terms of what can be monitored and what support can be realized from Microsoft. Typically, these SKU’s do not have a service SLA and Microsoft may refuse to provide support for them. Consequently Basic/Free SKUs should never be used for production workloads.

Typically, production workloads need to be monitored and should have an SLA with Microsoft, using Basic SKUs for any deployed product will mean that that these capabilities do not exist.

The following resource types should use standard SKUs as a minimum.
- Public IP Addresses
- Network Load Balancers
- REDIS Cache
- SQL PaaS Databases
- VPN Gateways

## Remediation

Each resource has its own process for upgrading from basic to standard SKUs that should be followed if required.

- Public IP Address: [https://learn.microsoft.com/en-us/azure/virtual-network/ip-services/public-ip-upgrade](https://learn.microsoft.com/en-us/azure/virtual-network/ip-services/public-ip-upgrade)

- Basic Load Balancer: [https://learn.microsoft.com/en-us/azure/load-balancer/load-balancer-basic-upgrade-guidance](https://learn.microsoft.com/en-us/azure/load-balancer/load-balancer-basic-upgrade-guidance)

- Azure Cache for Redis: [https://learn.microsoft.com/en-us/azure/azure-cache-for-redis/cache-how-to-scale](https://learn.microsoft.com/en-us/azure/azure-cache-for-redis/cache-how-to-scale)

- Azure SQL Database: [https://learn.microsoft.com/en-us/azure/azure-sql/database/scale-resources](https://learn.microsoft.com/en-us/azure/azure-sql/database/scale-resources)

- VPN Gateway: [https://learn.microsoft.com/en-us/azure/vpn-gateway/gateway-sku-resize](https://learn.microsoft.com/en-us/azure/vpn-gateway/gateway-sku-resize)

### Default Value

Policy should enforce standard SKUs for the following artifacts:
- Public IP Addresses
- Network Load Balancers
- REDIS Cache
- SQL PaaS Databases
- VPN Gateways.
