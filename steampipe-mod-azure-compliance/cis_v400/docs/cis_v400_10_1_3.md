## Description

Implement SMB channel encryption with AES-256-GCM for SMB file shares to ensure data confidentiality and integrity in transit. This method offers strong protection against eavesdropping and man-in-the-middle attacks, safeguarding sensitive information.

AES-256-GCM encryption enhances the security of data transmitted over SMB channels by safeguarding it from unauthorized interception and tampering.

## Remediation

### From Azure Portal

1. Go to `Storage accounts`.
2. Click the name of a storage account.
3. Under `Data storage`, click `File shares`.
4. Under `File share settings`, click the link next to `Security`.
5. If `Profile` is set to `Maximum compatibility`, click the drop-down menu and select `Maximum security` or `Custom`.
6. If selecting `Custom`, under `SMB channel encryption`, uncheck the boxes next to `AES-128-CCM` and `AES-128-GCM`.
7. Click `Save`.
8. Repeat steps 1-7 for each storage account requiring remediation.

### From Azure CLI

For each storage account requiring remediation, run the following command to set the SMB channel encryption:

```bash
az storage account file-service-properties update --resource-group <resource-group> --account-name <storage-account> --channel-encryption AES-256-GCM
```

### From PowerShell

For each storage account requiring remediation, run the following command to set the SMB channel encryption:

```bash
Update-AzStorageFileServiceProperty -ResourceGroupName <resource-group> -StorageAccountName <storage-account> -SmbChannelEncryption AES-256-GCM
```

### Default Value

By default, the following SMB channel encryption algorithms are allowed:
- AES-128-CCM
- AES-128-GCM
- AES-256-GCM.
