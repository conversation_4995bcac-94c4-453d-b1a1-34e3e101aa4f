## Description

Microsoft Defender for Containers helps improve, monitor, and maintain the security of containerized assets—including Kubernetes clusters, nodes, workloads, container registries, and images—across multi-cloud and on-premises environments.

By default, when enabling the plan through the Azure Portal, Microsoft Defender for Containers automatically configures the following components:

- **Agentless scanning for machines**
- **Defender sensor** for runtime protection
- **Azure Policy** for enforcing security best practices
- **K8S API access** for monitoring and threat detection
- **Registry access** for vulnerability assessment

**Note:** Microsoft Defender for Container Registries ('ContainerRegistry') is deprecated and has been replaced by Microsoft Defender for Containers ('Containers').

Enabling Microsoft Defender for Containers enhances defense-in-depth by providing advanced threat detection, vulnerability assessment, and security monitoring for containerized environments, leveraging insights from the Microsoft Security Response Center (MSRC).

## Remediation

### From Azure Portal

1. Go to `Microsoft Defender for Cloud`.
2. Under `Management`, click `Environment settings`.
3. Click the name of a subscription.
4. Under `Settings`, click `Defender plans`.
5. Under `Cloud Workload Protection (CWP)`, in the row for `Containers`, click `On` in the `Status` column.
6. If `Monitoring coverage` displays `Partial`, click `Settings` under `Partial`.
7. Set the status of each of the components to `On`.
8. Click `Continue`.
9. Click `Save`.
10. Repeat steps 1-9 for each subscription.

### From Azure CLI

**Note:** Microsoft Defender for Container Registries ('ContainerRegistry') is deprecated and has been replaced by Microsoft Defender for Containers ('Containers').

Run the below command to enable the Microsoft Defender for Containers plan and its components:

```bash
az security pricing create -n 'Containers' --tier 'standard' --extensions name=ContainerRegistriesVulnerabilityAssessments isEnabled=True --extensions name=AgentlessDiscoveryForKubernetes isEnabled=True --extensions name=AgentlessVmScanning isEnabled=True --extensions name=ContainerSensor isEnabled=True
```

### From PowerShell

**Note:** Microsoft Defender for Container Registries ('ContainerRegistry') is deprecated and has been replaced by Microsoft Defender for Containers ('Containers').

Run the below command to enable the Microsoft Defender for Containers plan and its components:

```bash
Set-AzSecurityPricing -Name 'Containers' -PricingTier 'Standard' -Extension '[{"name":"ContainerRegistriesVulnerabilityAssessments","isEnabled":"True"},{"name":"AgentlessDiscoveryForKubernetes","isEnabled":"True"},{"name":"AgentlessVmScanning","isEnabled":"True"},{"name":"ContainerSensor","isEnabled":"True"}]'
```

### Default Value

The Microsoft Defender for Containers plan is disabled by default.
