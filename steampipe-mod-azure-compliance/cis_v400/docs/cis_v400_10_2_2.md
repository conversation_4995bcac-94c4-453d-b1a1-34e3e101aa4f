## Description

Enabling blob versioning allows for the automatic retention of previous versions of objects. With blob versioning enabled, earlier versions of a blob are accessible for data recovery in the event of modifications or deletions.

Blob versioning safeguards data integrity and enables recovery by retaining previous versions of stored objects, facilitating quick restoration from accidental deletion, modification, or malicious activity.

## Remediation

### From Azure Portal

1. Go to `Storage accounts`.
2. Click the name of a storage account with blob storage.
3. In the `Overview` page, on the `Properties` tab, under `Blob service`, click `Disabled` next to `Versioning`.
4. Under `Tracking`, check the box next to `Enable versioning for blobs`.
5. Select the radio button next to `Keep all versions` or `Delete versions after (in days)`.
6. If selecting to delete versions, enter a number of in the box after which to delete blob versions.
7. Click `Save`.
8. Repeat steps 1-7 for each storage account with blob storage.

### From Azure CLI

For each storage account requiring remediation, run the following command to enable blob versioning:

```bash
az storage account blob-service-properties update --account-name <storage-account> --enable-versioning true
```

### From PowerShell

For each storage account requiring remediation, run the following command to enable blob versioning:

```bash
Update-AzStorageBlobServiceProperty -ResourceGroupName <resource-group> -StorageAccountName <storage-account> -IsVersioningEnabled $true
```

### Default Value

Blob versioning is disabled by default on storage accounts.
