## Overview

**SERVICE CATEGORY BENCHMARK AVAILABLE**:
- "CIS Microsoft Azure Compute Services Benchmark"

To better understand the relationship between the Foundations Benchmark and Services Benchmarks, please read the "Introduction" section of this document.

`PARTIAL RELOCATION - BE ADVISED!`

Some recommendations previously covered in the CIS Microsoft Azure Foundations Benchmark (this document) related to Compute services have been relocated to a Benchmark titled **CIS Microsoft Azure Compute Services Benchmark**. This Compute Services section now provides a **foundational set** of secure configuration recommendations for products from Azure Product Directory's "Compute" category of services.

After applying foundational recommendations, take inventory of the entire set of Compute Services in use by your organization, then look to the Benchmarks section of the CIS Microsoft Azure Community (https://workbench.cisecurity.org/communities/72) for defense-in-depth guidance for those specific services in the **CIS Microsoft Azure Compute Services Benchmark**.

Services addressed in the **CIS Microsoft Azure Compute Services Benchmark**:
- App Service
- Azure Container Instances
- Azure CycleCloud
- Azure Dedicated Host
- Azure Functions
- Azure Kubernetes Service (AKS)
- Azure Quantum
- Azure Service Fabric
- Azure Spot Virtual Machines
- Azure Spring Apps
- Azure Virtual Desktop
- Azure VM Image Builder
- Azure VMware Solution
- Batch
- Cloud Services
- Linux Virtual Machines
- SQL Server on Azure Virtual Machines
- Static Web Apps
- Virtual Machine Scale Sets
- Virtual Machines

Azure Product Directory Reference: [https://azure.microsoft.com/en-us/products#compute](https://azure.microsoft.com/en-us/products#compute)

**FEEDBACK REQUEST:** Is there a specific service or recommendation in this section that you'd like to see addressed or improved? Let us know by making a ticket or starting a discussion in the CIS Microsoft Azure Community (https://workbench.cisecurity.org/communities/72).
