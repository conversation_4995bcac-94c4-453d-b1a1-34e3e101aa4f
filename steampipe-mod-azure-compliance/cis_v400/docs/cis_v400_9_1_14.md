## Description

Enables emailing security alerts to the subscription owner or other designated security contact.

Enabling security alert emails ensures that security alert emails are sent by Microsoft. This ensures that the right people are aware of any potential security issues and can mitigate the risk.

## Remediation

### From Azure Portal

1. From Azure Home select the Portal Menu.
2. Select `Microsoft Defender for Cloud`.
3. Under `Management`, select `Environment settings`.
4. Click on the appropriate Subscription.
5. Click on `Email notifications`.
6. Under `Notification types`, check box next to `Notify about alerts with the following severity (or higher)` and select an appropriate severity level from the drop-down menu.
7. Click `Save`.
8. Repeat steps 1-7 for each Subscription requiring remediation.

### From Azure CLI

Use the below command to enable `Send email notification for high severity alerts`:

```bash
az account get-access-token --query "{subscription:subscription,accessToken:accessToken}" --out tsv | xargs -L1 bash -c 'curl -X PUT -H "Authorization: Bearer $1" -H "Content-Type: application/json" [https://management.azure.com/subscriptions/<$0>/providers/Microsoft.Security/securityContacts/default1?api-version=2017-08-01-preview](https://management.azure.com/subscriptions/<$0>/providers/Microsoft.Security/securityContacts/default1?api-version=2017-08-01-preview) -d@"input.json"'
```

Where `input.json` contains the data below, replacing `validEmailAddress` with a single email address or multiple comma-separated email addresses:

```json
{
  "id": "/subscriptions/<subscriptionId>/providers/Microsoft.Security/securityContacts/default",
  "name": "default",
  "type": "Microsoft.Security/securityContacts",
  "properties": {
    "email": "<validEmailAddress>",
    "alertNotifications": "On",
    "alertsToAdmins": "On"
  }
}
```

### Default Value

By default, subscription owners receive email notifications for high-severity alerts.
