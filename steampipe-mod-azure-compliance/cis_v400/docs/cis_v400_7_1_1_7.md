## Description

Ensure that virtual network flow logs are captured and fed into a central log analytics workspace.

Virtual network flow logs provide critical visibility into traffic patterns. Sending logs to a Log Analytics workspace enables centralized analysis, correlation, and alerting for faster threat detection and response.

## Remediation

### From Azure Portal

1. Go to `Network Watcher`.
2. Under `Logs`, click `Flow logs`.
3. <PERSON>lick `+ Create`.
4. Select a subscription.
5. Next to `Flow log type`, select `Virtual network`.
6. Click `+ Select target resource`.
7. Select `Virtual network`.
8. Select a virtual network.
9. Click `Confirm selection`.
10. Select a storage account, or create a new storage account.
11. Set the retention in days for the storage account.
12. Click `Next`.
13. Under `Analytics`, for `Flow logs version`, select `Version 2`.
14. Check the box next to `Enable traffic analytics`.
15. Select a processing interval.
16. Select a `Log Analytics Workspace`.
17. Click `Next`.
18. Optionally, add `Tags`.
19. Click `Review + create`.
20. Click `Create`.
21. Repeat steps 1-20 for each subscription or virtual network requiring remediation.
