## Description

Ensure that all Secrets in Role Based Access Control (RBAC) Azure Key Vaults have an expiration date set.

The Azure Key Vault enables users to store and keep secrets within the Microsoft Azure environment. Secrets in the Azure Key Vault are octet sequences with a maximum size of 25k bytes each. The `exp` (expiration date) attribute identifies the expiration date on or after which the secret MUST NOT be used. By default, secrets never expire. It is thus recommended to rotate secrets in the key vault and set an explicit expiration date for all secrets. This ensures that the secrets cannot be used beyond their assigned lifetimes.

## Remediation

### From Azure Portal

1. Go to `Key vaults`.
2. For each Key vault, click on `Secrets`.
3. In the main pane, ensure that the status of the secret is `Enabled`.
4. For each enabled secret, ensure that an appropriate `Expiration date` is set.

### From Azure CLI

Update the Expiration date for the secret using the below command:

```bash
az keyvault secret set-attributes --name <secret_name> --vault-name <vault_name> --expires Y-m-d'T'H:M:S'Z'
```

Note:
To view the expiration date on all secrets in a Key Vault using Microsoft API, the `List Secret` permission is required.

To update the expiration date for the secrets:

1. Go to the Key vault, click on `Access Control (IAM)`.
2. Click on `Add role assignment` and assign the role of `Key Vault Secrets Officer` to the appropriate user.

### From PowerShell

```bash
Set-AzKeyVaultSecretAttribute -VaultName <vault_name> -Name <secret_name> -Expires <date_time>
```

### Default Value

By default, secrets do not expire.
