## Description

This recommendation ensures that users accessing the Windows Azure Service Management API (i.e. Azure Powershell, Azure CLI, Azure Resource Manager API, etc.) are required to use multi-factor authentication (MFA) credentials when accessing resources through the Windows Azure Service Management API.

Administrative access to the Windows Azure Service Management API should be secured with a higher level of scrutiny to authenticating mechanisms. Enabling multi-factor authentication is recommended to reduce the potential for abuse of Administrative actions, and to prevent intruders or compromised admin credentials from changing administrative settings.

**IMPORTANT**: While this recommendation allows exceptions to specific Users or Groups, they should be very carefully tracked and reviewed for necessity on a regular interval through an Access Review process. It is important that this rule be built to include "All Users" to ensure that all users not specifically excepted will be required to use MFA to access the Azure Service Management API.

## Remediation

### From Azure Portal

1. From the Azure Admin Portal dashboard, open `Microsoft Entra ID`.
2. Click `Security` in the Entra ID blade.
3. Click `Conditional Access` in the Security blade.
4. Click `Policies` in the Conditional Access blade.
5. Click `+ New policy`.
6. Enter a name for the policy.
7. Click the blue text under `Users`.
8. Under `Include`, select `All users`.
9. Under `Exclude`, check `Users and groups`.
10. Select users or groups to be exempted from this policy (e.g. break-glass emergency accounts, and non-interactive service accounts) then click the `Select` button.
11. Click the blue text under `Target resources`.
12. Under `Include`, click the `Select apps` radio button.
13. Click the blue text under `Select`.
14. Check the box next to `Windows Azure Service Management APIs` then click the `Select` button.
15. Click the blue text under `Grant`.
16. Under `Grant access` check the box for `Require multi-factor authentication` then click the `Select` button.
17. Before creating, set `Enable policy` to `Report-only`.
18. Click `Create`.

After testing the policy in report-only mode, update the `Enable policy` setting from `Report-only` to `On`.

### Default Value

MFA is not enabled by default for administrative actions.
