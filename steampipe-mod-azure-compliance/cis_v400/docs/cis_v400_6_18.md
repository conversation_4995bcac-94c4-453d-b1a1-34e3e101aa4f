## Description

Restrict access to group web interface in the Access Panel portal.

Self-service group management enables users to create and manage security groups or Office 365 groups in Microsoft Entra ID. Unless a business requires this day-to-day delegation for some users, self-service group management should be disabled. Any user can access the Access Panel, where they can reset their passwords, view their information, etc. By default, users are also allowed to access the Group feature, which shows groups, members, related resources (SharePoint URL, Group email address, Yammer URL, and Teams URL). By setting this feature to 'Yes', users will no longer have access to the web interface, but still have access to the data using the API. This is useful to prevent non-technical users from enumerating groups-related information, but technical users will still be able to access this information using APIs.

## Remediation

### From Azure Portal

1. From Azure Home select the Portal Menu.
2. Select `Microsoft Entra ID`.
3. Under `Manage`, select `Groups`.
4. Under `Settings`, select `General`.
5. Under `Self Service Group Management`, set `Restrict user ability to access groups features in My Groups` to `Yes`.
6. Click `Save`.

### Default Value

By default, `Restrict user ability to access groups features in the Access Pane` is set to `No`.
