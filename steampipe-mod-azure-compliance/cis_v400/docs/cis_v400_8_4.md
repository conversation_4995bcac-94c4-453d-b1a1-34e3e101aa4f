## Description

Network security groups should be periodically evaluated for port misconfigurations. Where certain ports and protocols may be exposed to the Internet, they should be evaluated for necessity and restricted wherever they are not explicitly required and narrowly configured.

The potential security problem with using HTTP(S) over the Internet is that attackers can use various brute force techniques to gain access to Azure resources. Once the attackers gain access, they can use the resource as a launch point for compromising other resources within the Azure tenant.

## Remediation

### From Azure Portal

1. Go to `Virtual machines`.
2. For each VM, open the `Networking` blade.
3. Click on `Inbound port rules`.
4. Delete the rule with:
 * Port = 80/443 OR \[port range containing 80/443\]
 * Protocol = TCP OR Any
 * Source = Any (\*) OR IP Addresses(0.0.0.0/0) OR Service Tag(Internet)
 * Action = Allow

### From Azure CLI

1. Run below command to list network security groups:

```bash
az network nsg list --subscription <subscription-id> --output table
```

2. For each network security group, run below command to list the rules associated with the specified port:

```bash
az network nsg rule list --resource-group <resource-group> --nsg-name <nsg-name> --query "[?destinationPortRange=='80 or 443']"
```

3. Run the below command to delete the rule with:

 * Port = 80/443 OR \[port range containing 80/443\]
 * Protocol = TCP OR "*"
 * Source = Any (\*) OR IP Addresses(0.0.0.0/0) OR Service Tag(Internet)
 * Action = Allow

```bash
az network nsg rule delete --resource-group <resource-group> --nsg-name <nsg-name> --name <rule-name>
```
