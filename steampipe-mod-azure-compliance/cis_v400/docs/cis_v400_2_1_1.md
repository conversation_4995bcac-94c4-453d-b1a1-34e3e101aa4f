## Overview

The use of an appropriate Encryption Key Management methodology requires a carefully determined architectural choice that reflects your organization's maturity, technical capabilities, and compliance requirements.

Azure Services generally provide three options for Encryption Key Management:

1. **Microsoft Managed Keys ('MMK')** (Also known as Platform Managed Keys or PMK): The storage, creation, and maintenance of encryption keys is performed automatically by Microsoft. This option uses the Microsoft key store and automates the control and rotation of keys. Where the security and compliance frameworks implemented by your organization do not specify otherwise, Microsoft Managed Keys is generally preferred, but it should be understood that there is an implied trust that your organization must assume.
2. **Customer Managed Keys ('CMK')**: The creation and maintenance of encryption keys is the responsibility of the customer but stored in a Microsoft provided vault. This option stores keys in Azure Key Vault or Key Vault HSM, but the control and rotation of keys is performed by the customer. Encryption Key management introduces some complexity and technical debt to an environment because the creation and maintenance of keys requires technical capacity for maintaining key infrastructure in addition to scripting for automation. For environments that have specific compliance requirements for the control and rotation of keys, this option may be required.
3. **Customer Provided Keys ('CPK')**: The storage, control, and rotation of encryption keys is the responsibility of the customer. Your organization must have an independent key storage facility, maintain control and perform rotation of keys. This option introduces the most complexity and technical debt and should be implemented only for highly secure environments or systems where compliance requirements specify that key storage must be maintained by your organization.

The use of each of these methods of managing encryption keys requires careful consideration, and the scope of application should be determined prior to implementation.
