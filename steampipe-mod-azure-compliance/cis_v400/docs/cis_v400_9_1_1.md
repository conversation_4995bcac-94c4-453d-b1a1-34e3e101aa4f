## Overview

Microsoft Defender for Cloud offers foundational and advanced Cloud Security Posture Management (CSPM) solutions to protect across multi-cloud and hybrid environments. Both solutions cover PaaS as well as IaaS. CSPM provides reporting functionality on security and regulatory frameworks including NIST 800 series, ISO 27001, PCI-DSS, CIS Benchmarks and Controls, and many more. CSPM also provides the ability to create your own custom framework, but this will require significant work. Regulatory standards are reported in a compliance dashboard which offers a summarized view against deployed standards and presents the ability to download compliance reports in various formats.

CSPM has two types of implementations:

1. Foundational (Free): This implementation is free and enabled by default with a limited set of features including:
- Continuous assessment of the security configuration of cloud resources
- Security recommendations to fix misconfigurations and weaknesses
- Secure score summarizing current overall security posture

2. Full CSPM (Paid): Full CSPM is a paid product offering additional functionality including:
- Identity and role assignments discovery
- Network exposure detection
- Attack path analysis
- Cloud security explorer for risk hunting
- Agentless vulnerability scanning
- Agentless secrets scanning
- Governance rules to drive timely remediation and accountability
- Regulatory compliance and industry best practices
- Data-aware security posture
- Agentless discovery for Kubernetes
- Agentless container vulnerability assessment

It is recommended that for full CSPM a cost review is undertaken particularly if your tenant is heavy on IaaS prior to implementing and matched to security requirements.
