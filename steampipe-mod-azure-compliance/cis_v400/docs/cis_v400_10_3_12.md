## Description

Geo-redundant storage (GRS) in Azure replicates data three times within the primary region using locally redundant storage (LRS) and asynchronously copies it to a secondary region hundreds of miles away. This setup ensures high availability and resilience by providing 16 nines (99.**************%) durability over a year, safeguarding data against regional outages.

Enabling GRS protects critical data from regional failures by maintaining a copy in a geographically separate location. This significantly reduces the risk of data loss, supports business continuity, and meets high availability requirements for disaster recovery.

## Remediation

### From Azure Portal

1. Go to `Storage accounts`.
2. Click on a storage account.
3. Under `Data management`, click `Redundancy`.
4. From the `Redundancy` drop-down menu, select `Geo-redundant storage (GRS)`.
5. Click `Save`.
6. Repeat steps 1-5 for each storage account requiring remediation.

### From Azure CLI

For each storage account requiring remediation, run the following command to enable geo-redundant storage:

```bash
az storage account update --resource-group <resource-group> --name <storage-account> --sku Standard_GRS
```

### From PowerShell

For each storage account requiring remediation, run the following command to enable geo-redundant storage:

```bash
Set-AzStorageAccount -ResourceGroupName <resource-group> -Name <storage-account> -SkuName "Standard_GRS"
```

### Default Value

When creating a storage account in the Azure Portal, the default redundancy setting is geo-redundant storage (GRS). Using the Azure CLI, the default is read-access geo-redundant storage (RA-GRS). In PowerShell, a redundancy level must be explicitly specified during account creation.
