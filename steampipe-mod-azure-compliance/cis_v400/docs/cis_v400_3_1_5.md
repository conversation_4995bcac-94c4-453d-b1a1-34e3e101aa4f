## Description

Unity Catalog is a centralized governance model for managing and securing data in Azure Databricks. It provides fine-grained access control to databases, tables, and views using Microsoft Entra ID identities. Unity Catalog also enhances data lineage, audit logging, and compliance monitoring, making it a critical component for security and governance.

* Enforces centralized access control policies and reduces data security risks.
* Enables identity-based authentication via Microsoft Entra ID.
* Improves compliance with industry regulations (e.g. GDPR, HIPAA, SOC 2) by providing audit logs and access visibility.
* Prevents unauthorized data access through table-, row-, and column-level security (RLS & CLS).

## Remediation

Use the remediation procedure written in this article: [https://learn.microsoft.com/en-us/azure/databricks/data-governance/unity-catalog/get-started](https://learn.microsoft.com/en-us/azure/databricks/data-governance/unity-catalog/get-started)

### Default Value

New workspaces have Unity Catalog enabled by default. Existing workspaces may require manual enablement.
