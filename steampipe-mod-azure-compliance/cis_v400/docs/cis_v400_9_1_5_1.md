## Description

Turning on Microsoft Defender for Storage enables threat detection for Storage, providing threat intelligence, anomaly detection, and behavior analytics in the Microsoft Defender for Cloud.

Enabling Microsoft Defender for Storage allows for greater defense-in-depth, with threat detection provided by the Microsoft Security Response Center (MSRC).

## Remediation

### From Azure Portal

1. Go to `Microsoft Defender for Cloud`.
2. Under `Management`, select `Environment Settings`.
3. Click on the subscription name.
4. Select the `Defender plans` blade.
5. Set `Status` to `On` for `Storage`.
6. Select `Save`.

### From Azure CLI

Ensure the output of the below command is Standard

```bash
az security pricing create -n StorageAccounts --tier 'standard'
```

### From PowerShell

```bash
Set-AzSecurityPricing -Name 'StorageAccounts' -PricingTier 'Standard'
```

### Default Value

By default, Microsoft Defender plan is off.
