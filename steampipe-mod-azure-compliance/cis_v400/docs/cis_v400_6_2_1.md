## Description

Microsoft Entra ID Conditional Access allows an organization to configure `Named locations` and configure whether those locations are trusted or untrusted. These settings provide organizations the means to specify Geographical locations for use in conditional access policies, or define actual IP addresses and IP ranges and whether or not those IP addresses and/or ranges are trusted by the organization.

Defining trusted source IP addresses or ranges helps organizations create and enforce Conditional Access policies around those trusted or untrusted IP addresses and ranges. Users authenticating from trusted IP addresses and/or ranges may have less access restrictions or access requirements when compared to users that try to authenticate to Microsoft Entra ID from untrusted locations or untrusted source IP addresses/ranges.

## Remediation

### From Azure Portal

1. In the Azure Portal, navigate to `Microsoft Entra ID`.
2. Under `Manage`, click `Security`.
3. Under `Protect`, click `Conditional Access`.
4. Under `Manage`, click `Named locations`.
5. Within the `Named locations` blade, click on `IP ranges location`.
6. Enter a name for this location setting in the `Name` text box.
7. Click on the `+` sign.
8. Add an IP Address Range in CIDR notation inside the text box that appears.
9. Click on the `Add` button.
10. Repeat steps 7 through 9 for each IP Range that needs to be added.
11. If the information entered are trusted ranges, select the `Mark as trusted location` check box.
12. Once finished, click on `Create`.

### From PowerShell

Create a new trusted IP-based Named location policy

```bash
[System.Collections.Generic.List`1[Microsoft.Open.MSGraph.Model.IpRange]]$ipRanges = @()
$ipRanges.Add("<first IP range in CIDR notation>")
$ipRanges.Add("<second IP range in CIDR notation>")
$ipRanges.Add("<third IP range in CIDR notation>")
New-MgIdentityConditionalAccessNamedLocation -dataType "#microsoft.graph.ipNamedLocation" -DisplayName "<name of IP Named location policy>" -IsTrusted $true -IpRanges $ipRanges
```

Set an existing IP-based Named location policy to trusted

```bash
Update-MgIdentityConditionalAccessNamedLocation -PolicyId "<ID of the policy>" -dataType "#microsoft.graph.ipNamedLocation" -IsTrusted $true
```

### Default Value

By default, no locations are configured under the `Named locations` blade within the Microsoft Entra ID Conditional Access blade.
