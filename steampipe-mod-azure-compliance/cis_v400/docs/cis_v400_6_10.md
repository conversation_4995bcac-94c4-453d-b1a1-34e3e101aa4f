## Description

Ensure that users are notified on their primary and alternate emails on password resets.

User notification on password reset is a proactive way of confirming password reset activity. It helps the user to recognize unauthorized password reset activities.

## Remediation

### From Azure Portal

1. From Azure Home select the Portal Menu.
2. Select `Microsoft Entra ID`.
3. Under `Manage`, select `Users`.
4. Under `Manage`, select `Password reset`.
5. Under `Manage`, select `Notifications`.
6. Set `Notify users on password resets?` to `Yes`.
7. Click `Save`.

### Default Value

By default, `Notify users on password resets?` is set to "Yes".
