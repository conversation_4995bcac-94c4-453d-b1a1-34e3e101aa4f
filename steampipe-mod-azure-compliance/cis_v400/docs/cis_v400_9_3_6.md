## Description

The recommended way to access Key Vaults is to use the Azure Role-Based Access Control (RBAC) permissions model.

Azure RBAC is an authorization system built on Azure Resource Manager that provides fine-grained access management of Azure resources. It allows users to manage Key, Secret, and Certificate permissions. It provides one place to manage all permissions across all key vaults.

The new RBAC permissions model for Key Vaults enables a much finer grained access control for key vault secrets, keys, certificates, etc., than the vault access policy. This in turn will permit the use of privileged identity management over these roles, thus securing the key vaults with JIT Access management.

## Remediation

### From Azure Portal

Key Vaults can be configured to use `Azure role-based access control` on creation.

For existing Key Vaults:

1. From Azure Home open the Portal Menu in the top left corner.
2. Select `Key Vaults`.
3. Select a Key Vault to audit.
4. Select `Access configuration`.
5. Set the Permission model radio button to `Azure role-based access control`, taking note of the warning message.
6. Click `Save`.
7. Select `Access Control (IAM)`.
8. Select the `Role Assignments` tab.
9. Reapply permissions as needed to groups or users.

### From Azure CLI

To enable RBAC Authorization for each Key Vault, run the following Azure CLI command:

```bash
az keyvault update --resource-group <resource_group> --name <vault_name> --enable-rbac-authorization true
```

### From PowerShell

To enable RBAC authorization on each Key Vault, run the following PowerShell command:

```bash
Update-AzKeyVault -ResourceGroupName <resource_group> -VaultName <vault_name> -EnableRbacAuthorization $True
```

### Default Value

The default value for Access control in Key Vaults is Vault Policy.
