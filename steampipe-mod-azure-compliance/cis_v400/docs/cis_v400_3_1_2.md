## Description

Network Security Groups (NSGs) should be implemented to control inbound and outbound traffic to Azure Databricks subnets, ensuring only authorized communication. NSGs should be configured with deny rules to block unwanted traffic and restrict communication to essential sources only.

## Remediation

### From Azure Portal

1. Assign NSG to Databricks subnets under Networking > NSG Settings.

### Default Value

By default, Databricks subnets do not have NSGs assigned.
