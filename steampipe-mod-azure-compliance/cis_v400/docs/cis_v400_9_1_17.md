## Description

[**NOTE:** As of August 1, 2023 customers with an existing subscription to Defender for DNS can continue to use the service, but new subscribers will receive alerts about suspicious DNS activity as part of Defender for Servers P2.]

Microsoft Defender for DNS scans all network traffic exiting from within a subscription.

DNS lookups within a subscription are scanned and compared to a dynamic list of websites that might be potential security threats. These threats could be a result of a security breach within your services, thus scanning for them could prevent a potential security threat from being introduced.

## Remediation

### From Azure Portal

1. Go to `Microsoft Defender for Cloud`.
2. Under `Management`, select `Environment Settings`.
3. Click on the subscription name.
4. Select the `Defender plans` blade.
5. Select `On` under `Status` for `DNS`.
6. Select `Save`.

### From Azure CLI

Enable Standard pricing tier for DNS:

```bash
az security pricing create -n 'DNS' --tier 'Standard'
```

### From PowerShell

Enable Standard pricing tier for DNS:

```bash
Set-AzSecurityPricing -Name 'DNS' -PricingTier 'Standard'
```

### Default Value

By default, Microsoft Defender for DNS is not enabled.
