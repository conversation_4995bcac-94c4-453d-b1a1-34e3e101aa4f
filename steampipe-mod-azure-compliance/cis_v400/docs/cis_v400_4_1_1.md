## Description

Verify identities without MFA that can log in to a privileged virtual machine using separate login credentials. An adversary can leverage the access to move laterally and perform actions with the virtual machine's managed identity. Make sure the virtual machine only has necessary permissions, and revoke the admin-level permissions according to the principle of least privilege.

Integrating multi-factor authentication (MFA) as part of the organizational policy can greatly reduce the risk of an identity gaining control of valid credentials that may be used for additional tactics such as initial access, lateral movement, and collecting information. MFA can also be used to restrict access to cloud resources and APIs.

An Adversary may log into accessible cloud services within a compromised environment using Valid Accounts that are synchronized to move laterally and perform actions with the virtual machine's managed identity. The adversary may then perform management actions or access cloud-hosted resources as the logged-on managed identity.

## Remediation

### From Azure Portal

1. Log in to the Azure portal.
2. This can be remediated by enabling MFA for user, Removing user access or Reducing access of managed identities attached to virtual machines.

- Case I : Enable MFA for users having access on virtual machines.
 1. Go to `Microsoft Entra ID`.
 1. For `Per-user MFA`:
 1. Under `Manage`, click `Users`.
 1. Click `Per-user MFA`.
 1. For each user requiring remediation, check the box next to their name.
 1. Click `Enable MFA`.
 1. Click `Enable`.
 1. For `Conditional Access`:
 1. Under `Manage`, click `Security`.
 1. Under `Protect`, click `Conditional Access`.
 1. Update the Conditional Access policy requiring MFA for all users, removing each user requiring remediation from the `Exclude` list.

- Case II : Removing user access on a virtual machine.
 1. Select the `Subscription`, then click on `Access control (IAM)`.
 2. Select `Role assignments` and search for `Virtual Machine Administrator Login` or `Virtual Machine User Login` or any role that provides access to log into virtual machines.
 3. Click on `Role Name`, Select `Assignments`, and remove identities with no MFA configured.

- Case III : Reducing access of managed identities attached to virtual machines.
 1. Select the `Subscription`, then click on `Access control (IAM)`.
 2. Select `Role Assignments` from the top menu and apply filters on `Assignment type` as `Privileged administrator roles` and `Type` as `Virtual Machines`.
 3. Click on `Role Name`, Select `Assignments`, and remove identities access make sure this follows the least privileges principal.
