## Description

Restrict access to the Microsoft Entra ID administration center to administrators only.

**NOTE**: This only affects access to the Entra ID administrator's web portal. This setting does not prohibit privileged users from using other methods such as Rest API or Powershell to obtain sensitive information from Microsoft Entra ID.

The Microsoft Entra ID administrative center has sensitive data and permission settings. All non-administrators should be prohibited from accessing any Microsoft Entra ID data in the administration center to avoid exposure.

## Remediation

### From Azure Portal

1. From Azure Home select the Portal Menu.
2. Select `Microsoft Entra ID`.
3. Under `Manage`, select `Users`.
4. Under `Manage`, select `User settings`.
5. Under `Administration centre`, set `Restrict access to Microsoft Entra admin center` to `Yes`.
6. Click `Save`.

### Default Value

By default, `Restrict access to Microsoft Entra admin center` is set to `No`.
