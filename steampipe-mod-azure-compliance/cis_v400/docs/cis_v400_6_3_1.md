## Description

Microsoft Azure admin accounts should not be used for routine, non-administrative tasks.

Using admin accounts for daily operations increases the risk of accidental misconfigurations and security breaches.

## Remediation

If admin accounts are being used for daily operations, consider the following:

- Monitor and alert on unusual activity.
- Enforce the principle of least privilege.
- Revoke any unnecessary administrative access.
- Use Conditional Access to limit access to resources.
- Ensure that administrators have separate admin and user accounts.
- Use Microsoft Entra ID Protection helps organizations detect, investigate, and remediate identity-based risks.
- Use Privileged Identity Management (PIM) in Microsoft Entra ID to limit standing administrator access to privileged roles, discover who has access, and review privileged access.
