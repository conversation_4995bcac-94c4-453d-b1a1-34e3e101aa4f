## Description

Enable Network Watcher for physical regions in Azure subscriptions.

Network diagnostic and visualization tools available with Network Watcher help users understand, diagnose, and gain insights to the network in Azure.

## Remediation

Opting out of Network Watcher automatic enablement is a permanent change. Once you opt-out you cannot opt-in without contacting support.

To manually enable Network Watcher in each region where you want to use Network Watcher capabilities, follow the steps below.

### From Azure Portal

1. Use the Search bar to search for and click on the `Network Watcher` service.
2. Click `Create`.
3. Select a `Region` from the drop-down menu.
4. Click `Add`.

### From Azure CLI

```bash
az network watcher configure --locations <region> --enabled true --resource-group <resource_group>
```

### Default Value

Network Watcher is automatically enabled.
When you create or update a virtual network in your subscription, Network Watcher will be enabled automatically in your Virtual Network's region. There is no impact to your resources or associated charge for automatically enabling Network Watcher.
