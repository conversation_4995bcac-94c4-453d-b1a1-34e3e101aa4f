## Description

Networking for Azure Databricks can be set up in a few different ways. Using a customer-managed Virtual Network (VNet) (also known as VNet Injection) ensures that compute clusters and control planes are securely isolated within the organization’s network boundary. By default, Databricks creates a managed VNet, which provides limited control over network security policies, firewall configurations, and routing.

Using a customer-managed VNet ensures better control over network security and aligns with zero-trust architecture principles. It allows for:

- Restricted outbound internet access to prevent unauthorized data exfiltration.
- Integration with on-premises networks via VPN or ExpressRoute for hybrid connectivity.
- Fine-grained NSG policies to restrict access at the subnet level.
- Private Link for secure API access, avoiding public internet exposure.

## Remediation

### From Azure Portal

1. Delete the existing Databricks workspace (migration required).
2. Create a new Databricks workspace with VNet Injection:
3. Go to Azure Portal → Create Databricks Workspace.
4. Select Advanced Networking.
5. Choose Deploy into your own Virtual Network.
6. Specify a customer-managed VNet and associated subnets.
7. Enable Private Link for secure API access.

### From Azure CLI

Deploy a new Databricks workspace in a custom VNet:

```bash
az databricks workspace create --name <databricks-workspace-name> \
--resource-group <resource-group-name> \
--location <region> \
--managed-resource-group <managed-rg-name> \
--enable-no-public-ip true \
--network-security-group-rule "NoAzureServices" \
--public-network-access Disabled \
--custom-virtual-network-id /subscriptions/<subscription-id>/resourceGroups/<resource-group-name>/providers/Microsoft.Network/virtualNetworks/<vnet-name>
```

Ensure NSG Rules are correctly configured:

```bash
az network nsg rule create --resource-group <resource-group-name> \
--nsg-name <nsg-name> \
--name "DenyAllOutbound" \
--direction Outbound \
--access Deny \
--priority 4096
```

### From PowerShell

```bash
New-AzDatabricksWorkspace -ResourceGroupName <resource-group-name> -Name <databricks-workspace-name> -Location <region> -ManagedResourceGroupName <managed-rg-name> -CustomVirtualNetworkId "/subscriptions/<subscription-id>/resourceGroups/<resource-group-name>/providers/Microsoft.Network/virtualNetworks/<vnet-name>"
```

### Default Value

By default, Azure Databricks uses a Databricks-Managed VNet.
