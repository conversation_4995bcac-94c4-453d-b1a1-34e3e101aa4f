## Description

When Private endpoint is configured on a Key Vault, connections from Azure resources within the same subnet will use its private IP address. However, network traffic from the public internet can still flow connect to the Key Vault’s public endpoint (mykeyvault.vault.azure.net) using its public IP address unless Public network access is set to “Disabled”.

Setting the Public network access to “Disabled” with a Private Endpoint will remove the Vault’s public endpoint from Azure public DNS, reducing its exposure to the public internet. Network traffic will use the Vault private endpoint IP address for all requests (mykeyvault.vault.privatelink.azure.net).

Removing a point of interconnection from the internet edge to your Key Vault can strengthen the network security boundary of your system and reduce the risk of exposing the control plane or vault objects to untrusted clients.

Although Azure resources are never truly isolated from the public internet, disabling the public endpoint removes a line of sight from the public internet and increases the effort required for an attack.

## Remediation

### From Azure Portal

Key Vaults can be configured to use `Azure role-based access control` on creation.

For existing Key Vaults:

1. From Azure Home open the Portal Menu in the top left corner.
2. Select `Key Vaults`.
3. Select a Key Vault to audit.
4. Select `Networking`.
5. NEXT.

### From Azure CLI

To disable Public network access for each Key Vault, run the following Azure CLI command:

```bash
az keyvault update --resource-group <resource_group> --name <vault_name> --public-network-access Disabled
```

### From PowerShell

To enable RBAC authorization on each Key Vault, run the following PowerShell command:

```bash
Update-AzKeyVault -ResourceGroupName <resource_group> -VaultName <vault_name> -PublicNetworkAccess "Disabled"
```

### Default Value

The default value for Access control in Key Vaults is Vault Policy.
