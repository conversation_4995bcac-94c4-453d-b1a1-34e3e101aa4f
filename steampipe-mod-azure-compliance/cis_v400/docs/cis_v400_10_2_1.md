## Description

Blobs in Azure storage accounts may contain sensitive or personal data, such as ePHI or financial information. Data that is erroneously modified or deleted by an application or a user can lead to data loss or unavailability.

It is recommended that soft delete be enabled on Azure storage accounts with blob storage to allow for the preservation and recovery of data when blobs or blob snapshots are deleted.

Blobs can be deleted incorrectly. An attacker or malicious user may do this deliberately in order to cause disruption. Deleting an Azure storage blob results in immediate data loss. Enabling this configuration for Azure storage accounts ensures that even if blobs are deleted from the storage account, the blobs are recoverable for a specific period of time, which is defined in the "Retention policies," ranging from 7 to 365 days.

## Remediation

### From Azure Portal

1. Go to `Storage Accounts`.
2. For each Storage Account with blob storage, under `Data management`, go to `Data protection`.
3. Check the box next to `Enable soft delete for blobs`.
4. Set the retention period to a sufficient length for your organization.
5. Click `Save`.

### From Azure CLI

For each storage account requiring remediation, run the following command to enable soft delete for blobs:

```bash
az storage blob service-properties delete-policy update --days-retained <retention-days> --account-name <storage-account> --enable true
```

### Default Value

Soft delete for blob storage is **enabled** by default on storage accounts created via the Azure Portal, and **disabled** by default on storage accounts created via Azure CLI or PowerShell.
