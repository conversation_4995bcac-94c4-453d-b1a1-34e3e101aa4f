## Description

Turning on Microsoft Defender for Azure SQL Databases enables threat detection for Managed Instance Azure SQL databases, providing threat intelligence, anomaly detection, and behavior analytics in Microsoft Defender for Cloud.

Enabling Microsoft Defender for Azure SQL Databases allows for greater defense-in-depth, includes functionality for discovering and classifying sensitive data, surfacing and mitigating potential database vulnerabilities, and detecting anomalous activities that could indicate a threat to your database.

## Remediation

### From Azure Portal

1. Go to `Microsoft Defender for Cloud`.
2. Under `Management`, select `Environment Settings`.
3. Click on the subscription name.
4. Select the `Defender plans` blade.
5. Click `Select types >` in the row for `Databases`.
6. Set the toggle switch next to `Azure SQL Databases` to `On`.
7. Select `Continue`.
8. Select `Save`.

### From Azure CLI

Run the following command:

```bash
az security pricing create -n SqlServers --tier 'standard'
```

### From PowerShell

Run the following command:

```bash
Set-AzSecurityPricing -Name 'SqlServers' -PricingTier 'Standard'
```

### Default Value

By default, Microsoft Defender plan is off.
