## Description

Disallowing public network access for a storage account overrides the public access settings for individual containers in that storage account for Azure Resource Manager Deployment Model storage accounts. Azure Storage accounts that use the classic deployment model will be retired on August 31, 2024.

The default network configuration for a storage account permits a user with appropriate permissions to configure public network access to containers and blobs in a storage account. Keep in mind that public access to a container is always turned off by default and must be explicitly configured to permit anonymous requests. It grants read-only access to these resources without sharing the account key, and without requiring a shared access signature.
It is recommended not to provide public network access to storage accounts until, and unless, it is strongly desired. A shared access signature token or Azure AD RBAC should be used for providing controlled and timed access to blob containers.

## Remediation

### From Azure Portal

First, follow Microsoft documentation and create shared access signature tokens for your blob containers. Then,

1. Go to `Storage Accounts`.
2. For each storage account, under the `Security + networking` section, click `Networking`.
3. Set `Public network access` to `Disabled`.
4. Click `Save`.

### From Azure CLI

Set 'Public Network Access' to `Disabled` on the storage account

```bash
az storage account update --name <storage-account> --resource-group <resource-group> --public-network-access Disabled
```

### From PowerShell

For each Storage Account, run the following to set the `PublicNetworkAccess` setting to `Disabled`

```bash
Set-AzStorageAccount -ResourceGroupName <resource group name> -Name <storage account name> -PublicNetworkAccess Disabled
```

### Default Value

By default, `Public Network Access` is set to `Enabled from all networks` for the Storage Account.
