## Description

Azure Files offers soft delete for file shares, allowing you to easily recover your data when it is mistakenly deleted by an application or another storage account user.

Important data could be accidentally deleted or removed by a malicious actor. With soft delete enabled, the data is retained for the defined retention period before permanent deletion, allowing for recovery of the data.

## Remediation

### From Azure Portal

1. Go to `Storage Accounts`.
2. For each storage account with file shares, under `Data storage`, click `File shares`.
3. Under `File share settings`, click the value next to `Soft delete`.
4. Under `Soft delete for all file shares`, click the toggle to set it to `Enabled`.
5. Under `Retention policies`, set an appropriate number of days to retain soft deleted data between 1 and 365, inclusive.
6. Click `Save`.

### From Azure CLI

For each storage account requiring remediation, run the following command to enable soft delete for file shares and set an appropriate number of days for deleted data to be retained, between 1 and 365, inclusive:

```bash
az storage account file-service-properties update --account-name <storage-account> --enable-delete-retention true --delete-retention-days <retention-days>
```

### From PowerShell

For each storage account requiring remediation, run the following command to enable soft delete for file shares and set an appropriate number of days for deleted data to be retained, between 1 and 365, inclusive:

```bash
Update-AzStorageFileServiceProperty -ResourceGroupName <resource-group> -AccountName <storage-account> -EnableShareDeleteRetentionPolicy $true -ShareRetentionDays <retention-days>
```

### Default Value

Soft delete is enabled by default at the storage account file share setting level.
