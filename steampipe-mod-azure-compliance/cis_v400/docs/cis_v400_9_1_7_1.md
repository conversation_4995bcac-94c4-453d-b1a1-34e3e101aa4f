## Description

Microsoft Defender for Azure Cosmos DB scans all incoming network requests for threats to your Azure Cosmos DB resources.

In scanning Azure Cosmos DB requests within a subscription, requests are compared to a heuristic list of potential security threats. These threats could be a result of a security breach within your services, thus scanning for them could prevent a potential security threat from being introduced.

## Remediation

### From Azure Portal

1. Go to `Microsoft Defender for Cloud`.
2. Under `Management`, select `Environment Settings`.
3. Click on the subscription name.
4. Select the `Defender plans` blade.
5. On the `Database` row click on `Select types >`.
6. Set the toggle switch next to `Azure Cosmos DB` to `On`.
7. Click `Continue`.
8. Click `Save`.

### From Azure CLI

Run the following command:

```bash
az security pricing create -n 'CosmosDbs' --tier 'standard'
```

### From PowerShell

Use the below command to enable Standard pricing tier for Azure Cosmos DB

```bash
Set-AzSecurityPricing -Name 'CosmosDbs' -PricingTier 'Standard
```

### Default Value

By default, Microsoft Defender for Azure Cosmos DB is not enabled.
