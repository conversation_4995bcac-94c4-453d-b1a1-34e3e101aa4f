## Description

The principle of least privilege should be followed and only necessary privileges should be assigned instead of allowing full administrative access.

Custom roles in Azure with administrative access can obfuscate the permissions granted and introduce complexity and blind spots to the management of privileged identities. For less mature security programs without regular identity audits, the creation of Custom roles should be avoided entirely. For more mature security programs with regular identity audits, Custom Roles should be audited for use and assignment, used minimally, and the principle of least privilege should be observed when granting permissions

## Remediation

### From Azure Portal

1. From Azure Home select the Portal Menu.
2. Select `Subscriptions`.
3. Select a subscription.
4. Select `Access control (IAM)`.
5. Select `Roles`.
6. Click `Type` and select `Custom role` from the drop-down menu.
7. Check the box next to each role which grants subscription administrator privileges.
8. Select `Delete`.
9. Select `Yes`.

### From Azure CLI

List custom roles:

```bash
az role definition list --custom-role-only True
```

Check for entries with `assignableScope` of the `subscription`, and an action of `*`.

To remove a violating role:

```bash
az role definition delete --name <role name>
```

Note that any role assignments must be removed before a custom role can be deleted.
Ensure impact is assessed before deleting a custom role granting subscription administrator privileges.

### Default Value

By default, no custom owner roles are created.
