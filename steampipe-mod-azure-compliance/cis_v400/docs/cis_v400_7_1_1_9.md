## Description

Ensure that a Microsoft Entra diagnostic setting is configured to send Microsoft Entra activity logs to a suitable destination, such as a Log Analytics workspace, storage account, or event hub. This enables centralized monitoring and analysis of Microsoft Entra activity logs.

Microsoft Entra activity logs enables you to assess many aspects of your Microsoft Entra tenant. Configuring diagnostic settings in Microsoft Entra ensures these logs are collected and sent to an appropriate destination for monitoring, analysis, and retention.

## Remediation

### From Azure Portal

1. Go to `Microsoft Entra ID`.
2. Under `Monitoring`, click `Diagnostic settings`.
3. Click `+ Add diagnostic setting`.
4. Provide a `Diagnostic setting name`.
5. Under `Logs > Categories`, check the box next to each of the following logs:
 - `AuditLogs`
 - `SignInLogs`
 - `NonInteractiveUserSignInLogs`
 - `ServicePrincipalSignInLogs`
 - `ManagedIdentitySignInLogs`
 - `ProvisioningLogs`
 - `ADFSSignInLogs`
 - `RiskyUsers`
 - `UserRiskEvents`
 - `NetworkAccessTrafficLogs`
 - `RiskyServicePrincipals`
 - `ServicePrincipalRiskEvents`
 - `EnrichedOffice365AuditLogs`
 - `MicrosoftGraphActivityLogs`
 - `RemoteNetworkHealthLogs`
 - `NetworkAccessAlerts`
6. Configure an appropriate destination for the logs.
7. Click `Save`.

### Default Value

By default, Microsoft Entra diagnostic settings do not exist.
