## Description

Key Vaults contain object keys, secrets, and certificates. Deletion of a Key Vault can cause immediate data loss or loss of security functions (authentication, validation, verification, non-repudiation, etc.) supported by the Key Vault objects.

It is recommended the Key Vault be made recoverable by enabling the "Do Not Purge" and "Soft Delete" functions. This is in order to prevent loss of encrypted data, including storage accounts, SQL databases, and/or dependent services provided by Key Vault objects (Keys, Secrets, Certificates) etc. This may happen in the case of accidental deletion by a user or from disruptive activity by a malicious user.

**NOTE:** In February 2025, Microsoft will enable soft-delete protection on all key vaults, and users will no longer be able to opt out of or turn off soft-delete.

**WARNING:** A current limitation is that role assignments disappearing when Key Vault is deleted. All role assignments will need to be recreated after recovery.

Users may accidentally run delete/purge commands on a Key Vault, or an attacker or malicious user may do so deliberately in order to cause disruption. Deleting or purging a Key Vault leads to immediate data loss, as keys encrypting data and secrets/certificates allowing access/services will become non-accessible.

Setting `enablePurgeProtection` to "true" for a Key Vault ensures that even if Key Vault is deleted, Key Vault itself or its objects remain recoverable for the next 90 days. Key Vault/objects can either be recovered or purged (permanent deletion) during those 90 days. If no action is taken, the key vault and its objects will subsequently be purged.

Enabling the enablePurgeProtection parameter on Key Vaults ensures that Key Vaults and their objects cannot be deleted/purged permanently.

## Remediation

To enable "Do Not Purge" and "Soft Delete" for a Key Vault:

### From Azure Portal

1. Go to `Key Vaults`.
2. Click the name of a Key Vault.
3. Under `Settings`, click `Properties`.
4. Select the radio button next to `Enable purge protection (enforce a mandatory retention period for deleted vaults and vault objects).
`.
**Note:** Once enabled, this option cannot be disabled.
5. Click `Save`.
6. Repeat steps 1-5 for each Key Vault requiring remediation.

### From Azure CLI

```bash
az resource update --id /subscriptions/xxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx/resourceGroups/<resourceGroupName>/providers/Microsoft.KeyVault
/vaults/<keyVaultName> --set properties.enablePurgeProtection=true
```

### From PowerShell

```bash
Update-AzKeyVault -VaultName <vaultName -ResourceGroupName <resourceGroupName -EnablePurgeProtection
```

### Default Value

When a new Key Vault is created,
- `enableSoftDelete` is enabled by default, and
- `enablePurgeProtection` is disabled by default.

**NOTE:** In February 2025, Microsoft will enable soft-delete protection on all key vaults, and users will no longer be able to opt out of or turn off soft-delete.
