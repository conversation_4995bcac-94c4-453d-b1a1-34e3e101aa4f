## Overview

This section covers security recommendations to follow for logging and monitoring policies on an Azure Subscription.

**Scoping: A necessary exercise for effective and efficient use of Logging and Monitoring**

For recommendations contained in this section, it is crucial that your organization consider and settle on the scope of application for each recommendation individually. The scope of application cannot be realistically written in a generic prescriptive way within these recommendations, so a scoping exercise is strongly recommended. A scoping exercise will help you determine which resources are "in scope" and will receive partial or complete logging and monitoring treatment, and which resources are "out of scope" and will not receive any logging and monitoring treatment.

Your objectives with the scoping exercise should be to:
- Produce a clear classification of resources
- Understand the control requirements of any relevant security or compliance frameworks
- Ensure the appropriate personnel can detect and react to threats
- Ensure relevant resources have a historical register for accountability and investigation
- Minimize alert fatigue and cost

Release Environments provide a helpful context for understanding scope from a DevOps perspective. For example:

1. Production Environment.
2. Staging Environment.
3. Testing Environment.
4. Development Environment.

While resources considered in the scope of a Production Environment might have a full set of recommendations applied for logging and monitoring, other release environments might have a limited set of recommendations applied for the sake of accountability. The names of these environments and which resources are in the scope of each environment will vary from one organization to another.
