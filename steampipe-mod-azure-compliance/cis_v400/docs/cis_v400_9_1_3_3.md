## Description

The Endpoint protection component enables Microsoft Defender for Endpoint (formerly 'Advanced Threat Protection' or 'ATP' or 'WDATP' - see additional info) to communicate with Microsoft Defender for Cloud.

**IMPORTANT:** When enabling integration between DfE & DfC it needs to be taken into account that this will have some side effects that may be undesirable.

1. For server 2019 & above if defender is installed (default for these server SKUs) this will trigger a deployment of the new unified agent and link to any of the extended configuration in the Defender portal.
2. If the new unified agent is required for server SKUs of Win 2016 or Linux and lower there is additional integration that needs to be switched on and agents need to be aligned.

Microsoft Defender for Endpoint integration brings comprehensive Endpoint Detection and Response (EDR) capabilities within Microsoft Defender for Cloud. This integration helps to spot abnormalities, as well as detect and respond to advanced attacks on endpoints monitored by Microsoft Defender for Cloud.

MDE works only with Standard Tier subscriptions.

## Remediation

### From Azure Portal

1. From Azure Home select the Portal Menu.
2. Go to `Microsoft Defender for Cloud`.
3. Under `Management`, select `Environment Settings`.
4. Click on the subscription name.
5. Click `Settings & monitoring`.
6. Set the `Status` for `Endpoint protection` to `On`.
7. Click `Continue`.

### From Azure CLI

Use the below command to enable Standard pricing tier for Storage Accounts

```bash
az account get-access-token --query "{subscription:subscription,accessToken:accessToken}" --out tsv | xargs -L1 bash -c 'curl -X PUT -H "Authorization: Bearer $1" -H "Content-Type: application/json" [https://management.azure.com/subscriptions/<subscriptionID>/providers/Microsoft.Security/settings/WDATP?api-version=2021-06-01](https://management.azure.com/subscriptions/<subscriptionID>/providers/Microsoft.Security/settings/WDATP?api-version=2021-06-01) -d@"input.json"'

```

Where input.json contains the Request body json data as mentioned below.

```json
{
  "id": "/subscriptions/<Your_Subscription_Id>/providers/Microsoft.Security/settings/WDATP",
  "kind": "DataExportSettings",
  "type": "Microsoft.Security/settings",
  "properties": {
    "enabled": true
  }
}
```

### Default Value

By default, Endpoint protection is `off`.
