## Description

Restrict invitations to users with specific administrative roles only.

Restricting invitations to users with specific administrator roles ensures that only authorized accounts have access to cloud resources. This helps to maintain "Need to Know" permissions and prevents inadvertent access to data.

By default the setting `Guest invite restrictions` is set to `Anyone in the organization can invite guest users including guests and non-admins`. This would allow anyone within the organization to invite guests and non-admins to the tenant, posing a security risk.

## Remediation

### From Azure Portal

1. From Azure Home select the Portal Menu.
2. Select `Microsoft Entra ID`.
3. Under `Manage`, select `External Identities`.
4. Select `External collaboration settings`.
5. Under `Guest invite settings`, set `Guest invite restrictions`, to `Only users assigned to specific admin roles can invite guest users`.
6. Click `Save`.

### From Powershell

Enter the following:

```bash
Connect-MgGraph
Update-MgPolicyAuthorizationPolicy -AllowInvitesFrom "adminsAndGuestInviters"
```

### Default Value

By default, `Guest invite restrictions` is set to `Anyone in the organization can invite guest users including guests and non-admins`.
