## Description

Azure Resource Manager _CannotDelete (Delete)_ locks can prevent users from accidentally or maliciously deleting a storage account. This feature ensures that while the Storage account can still be modified or used, deletion of the Storage account resource requires removal of the lock by a user with appropriate permissions.

This feature is a protective control for the availability of data. By ensuring that a storage account or its parent resource group cannot be deleted without first removing the lock, the risk of data loss is reduced.

Applying a _Delete_ lock on storage accounts protects the availability of data by preventing the accidental or unauthorized deletion of the entire storage account. It is a fundamental protective control that can prevent data loss

## Remediation

### From Azure Portal

1. Navigate to the storage account in the Azure portal.
2. Under the `Settings` section, select `Locks`.
3. Select `Add`.
4. Provide a Name, and choose `Delete` for the type of lock.
5. Add a note about the lock if desired.

### From Azure CLI

Replace the information within <> with appropriate values:

```bash
az lock create --name <lock> \
--resource-group <resource-group> \
--resource <storage-account> \
--lock-type CanNotDelete \
--resource-type Microsoft.Storage/storageAccounts
```

### From PowerShell

Replace the information within <> with appropriate values:

```bash
New-AzResourceLock -LockLevel CanNotDelete `
-LockName <lock> `
-ResourceName <storage-account> `
-ResourceType Microsoft.Storage/storageAccounts `
-ResourceGroupName <resource-group>
```

### Default Value

By default, no locks are applied to Azure resources, including storage accounts. Locks must be manually configured after resource creation.
