## Description

Ensure that Intune logs are captured and fed into a central log analytics workspace.

Intune includes built-in logs that provide information about your environments. Sending logs to a Log Analytics workspace enables centralized analysis, correlation, and alerting for faster threat detection and response.

## Remediation

### From Azure Portal

1. Go to `Intune`.
2. <PERSON>lick `Reports`.
3. Under `Azure monitor`, click `Diagnostic settings`.
4. Click `+ Add diagnostic setting`.
5. Provide a `Diagnostic setting name`.
6. Under `Logs > Categories`, check the box next to each of the following logs:
 - `AuditLogs`
 - `OperationalLogs`
 - `DeviceComplianceOrg`
 - `Devices`
 - `Windows365AuditLogs`
7. Under `Destination details`, check the box next to `Send to Log Analytics workspace`.
8. Select a `Subscription`.
9. Select a `Log Analytics workspace`.
10. Click `Save`.

### Default Value

By default, Intune diagnostic settings do not exist.
