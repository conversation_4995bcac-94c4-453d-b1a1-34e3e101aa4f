## Description

Adding an Azure Resource Manager `ReadOnly` lock can prevent users from accidentally or maliciously deleting a storage account, modifying its properties and containers, or creating access assignments. The lock must be removed before the storage account can be deleted or updated. It provides more protection than a `CannotDelete`-type of resource manager lock.

This feature prevents `POST` operations on a storage account and containers to the Azure Resource Manager control plane, _management.azure.com_. Blocked operations include `listKeys` which prevents clients from obtaining the account shared access keys.

Microsoft does not recommend `ReadOnly` locks for storage accounts with Azure Files and Table service containers.

This Azure Resource Manager REST API documentation (spec) provides information about the control plane `POST` operations for _Microsoft.Storage_ resources.

Applying a `ReadOnly` lock on storage accounts protects the confidentiality and availability of data by preventing the accidental or unauthorized deletion of the entire storage account and modification of the account, container properties, or access permissions. It can offer enhanced protection for blob and queue workloads with tradeoffs in usability and compatibility for clients using account shared access keys.

## Remediation

### From Azure Portal

1. Navigate to the storage account in the Azure portal.
2. Under the `Settings` section, select `Locks`.
3. Select `Add`.
4. Provide a Name, and choose `ReadOnly` for the type of lock.
5. Add a note about the lock if desired.

### From Azure CLI

Replace the information within <> with appropriate values:

```bash
az lock create --name <lock> \
--resource-group <resource-group> \
--resource <storage-account> \
--lock-type ReadOnly \
--resource-type Microsoft.Storage/storageAccounts
```

### From PowerShell

Replace the information within <> with appropriate values:

```bash
New-AzResourceLock -LockLevel ReadOnly `
-LockName <lock> `
-ResourceName <storage-account> `
-ResourceType Microsoft.Storage/storageAccounts `
-ResourceGroupName <resource-group>
```

### Default Value

By default, no locks are applied to Azure resources, including storage accounts. Locks must be manually configured after resource creation.
