## Description

Use private endpoints for your Azure Storage accounts to allow clients and services to securely access data located over a network via an encrypted Private Link. To do this, the private endpoint uses an IP address from the VNet for each service. Network traffic between disparate services securely traverses encrypted over the VNet. This VNet can also link addressing space, extending your network and accessing resources on it. Similarly, it can be a tunnel through public networks to connect remote infrastructures together. This creates further security through segmenting network traffic and preventing outside sources from accessing it.

Securing traffic between services through encryption protects the data from easy interception and reading.

## Remediation

### From Azure Portal

1. Open the `Storage Accounts` blade.
2. For each listed Storage Account, perform the following:
3. Under the `Security + networking` heading, click on `Networking`.
4. Click on the `Private endpoint connections` tab at the top of the networking window.
5. Click the `+ Private endpoint` button.
6. In the `1 - Basics` tab/step:
 - `Enter a name` that will be easily recognizable as associated with the Storage Account (*Note*: The "Network Interface Name" will be automatically completed, but you can customize it if needed.)
 - Ensure that the `Region` matches the region of the Storage Account
 - Click `Next`
7. In the `2 - Resource` tab/step:
 - Select the `target sub-resource` based on what type of storage resource is being made available
 - Click `Next`
8. In the `3 - Virtual Network` tab/step:
 - Select the `Virtual network` that your Storage Account will be connecting to
 - Select the `Subnet` that your Storage Account will be connecting to
 - (Optional) Select other network settings as appropriate for your environment
 - Click `Next`
9. In the `4 - DNS` tab/step:
 - (Optional) Select other DNS settings as appropriate for your environment
 - Click `Next`
10. In the `5 - Tags` tab/step:
 - (Optional) Set any tags that are relevant to your organization
 - Click `Next`
11. In the `6 - Review + create` tab/step:
 - A validation attempt will be made and after a few moments it should indicate `Validation Passed` - if it does not pass, double-check your settings before beginning more in depth troubleshooting.
 - If validation has passed, click `Create` then wait for a few minutes for the scripted deployment to complete.

Repeat the above procedure for each Private Endpoint required within every Storage Account.

### From PowerShell

```bash
$storageAccount = Get-AzStorageAccount -ResourceGroupName '<ResourceGroupName>' -Name '<storageaccountname>'

$privateEndpointConnection = @{
Name = 'connectionName'
PrivateLinkServiceId = $storageAccount.Id
GroupID = "blob|blob_secondary|file|file_secondary|table|table_secondary|queue|queue_secondary|web|web_secondary|dfs|dfs_secondary"
}

$privateLinkServiceConnection = New-AzPrivateLinkServiceConnection @privateEndpointConnection

$virtualNetDetails = Get-AzVirtualNetwork -ResourceGroupName '<ResourceGroupName>' -Name '<name>'

$privateEndpoint = @{
ResourceGroupName = '<ResourceGroupName>'
Name = '<PrivateEndpointName>'
Location = '<location>'
Subnet = $virtualNetDetails.Subnets[0]
PrivateLinkServiceConnection = $privateLinkServiceConnection
}

New-AzPrivateEndpoint @privateEndpoint
```

### From Azure CLI

```bash
az network private-endpoint create --resource-group <ResourceGroupName --location <location> --name <private endpoint name> --vnet-name <VNET Name> --subnet <subnet name> --private-connection-resource-id <storage account ID> --connection-name <private link service connection name> --group-id <blob|blob_secondary|file|file_secondary|table|table_secondary|queue|queue_secondary|web|web_secondary|dfs|dfs_secondary>
```

### Default Value

By default, Private Endpoints are not created for Storage Accounts.
