## Description

Require administrators or appropriately delegated users to create new tenants.

It is recommended to only allow an administrator to create new tenants. This prevent users from creating new Microsoft Entra ID or Azure AD B2C tenants and ensures that only authorized users are able to do so.

## Remediation

### From Azure Portal

1. From Azure Home select the Portal Menu.
2. Select `Microsoft Entra ID`.
3. Under `Manage`, select `Users`.
4. Under `Manage`, select `User settings`.
5. Set `Restrict non-admin users from creating tenants ` to `Yes`.
6. Click `Save`.

### From PowerShell

```bash
Import-Module Microsoft.Graph.Identity.SignIns

Connect-MgGraph -Scopes 'Policy.ReadWrite.Authorization'

Select-MgProfile -Name beta

$params = @{
DefaultUserRolePermissions = @{
AllowedToCreateTenants = $false
}
}

Update-MgPolicyAuthorizationPolicy -AuthorizationPolicyId -BodyParameter $params
```
