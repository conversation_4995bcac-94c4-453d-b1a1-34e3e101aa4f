## Description

Ensures that two alternate forms of identification are provided before allowing a password reset.

A Self-service Password Reset (SSPR) through Azure Multi-factor Authentication (MFA) ensures the user's identity is confirmed using two separate methods of identification. With multiple methods set, an attacker would have to compromise both methods before they could maliciously reset a user's password.

## Remediation

### From Azure Portal

1. From Azure Home select the Portal Menu.
2. Select `Microsoft Entra ID`.
3. Under `Manage`, select `Users`.
4. Under `Manage`, select `Password reset`.
5. Select `Authentication methods`.
6. Set the `Number of methods required to reset` to `2`.
7. Click `Save`.

### Default Value

By default, the `Number of methods required to reset` is set to "2".
