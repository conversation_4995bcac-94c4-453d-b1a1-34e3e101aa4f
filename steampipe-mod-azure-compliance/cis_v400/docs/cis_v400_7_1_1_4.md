## Description

Enable AuditEvent logging for key vault instances to ensure interactions with key vaults are logged and available.

Monitoring how and when key vaults are accessed, and by whom, enables an audit trail of interactions with confidential information, keys, and certificates managed by Azure Key Vault. Enabling logging for Key Vault saves information in a user provided destination of either an Azure storage account or Log Analytics workspace. The same destination can be used for collecting logs for multiple Key Vaults.

## Remediation

### From Azure Portal

1. Go to `Key vaults`.
2. Select a Key vault.
3. Under `Monitoring`, select `Diagnostic settings`.
4. Click `Edit setting` to update an existing diagnostic setting, or `Add diagnostic setting` to create a new one.
5. If creating a new diagnostic setting, provide a name.
6. Configure an appropriate destination.
7. Under `Category groups`, check `audit` and `allLogs`.
8. Click `Save`.

### From Azure CLI

To update an existing `Diagnostic Settings`

```bash
az monitor diagnostic-settings update --name "<diagnostic_setting_name>" --resource <key_vault_id>
```

To create a new `Diagnostic Settings`

```bash
az monitor diagnostic-settings create --name "<diagnostic_setting_name>" --resource <key_vault_id> --logs "[{category:audit,enabled:true},{category:allLogs,enabled:true}]" --metrics "[{category:AllMetrics,enabled:true}]" <[--event-hub <event_hub_ID> --event-hub-rule <event_hub_auth_rule_ID> | --storage-account <storage_account_ID> |--workspace <log_analytics_workspace_ID> | --marketplace-partner-id <solution_resource_ID>]>
```

### From PowerShell

Create the `Log` settings object

```bash
$logSettings = @()
$logSettings += New-AzDiagnosticSettingLogSettingsObject -Enabled $true -Category audit
$logSettings += New-AzDiagnosticSettingLogSettingsObject -Enabled $true -Category allLogs
```

Create the `Metric` settings object

```bash
$metricSettings = @()
$metricSettings += New-AzDiagnosticSettingMetricSettingsObject -Enabled $true -Category AllMetrics
```

Create the `Diagnostic Settings` for each `Key Vault`

```bash
New-AzDiagnosticSetting -Name "<diagnostic_setting_name>" -ResourceId <key_vault_id> -Log $logSettings -Metric $metricSettings [-StorageAccountId <storage_account_ID> | -EventHubName <event_hub_name> -EventHubAuthorizationRuleId <event_hub_auth_rule_ID> | -WorkSpaceId <log analytics workspace ID> | -MarketPlacePartnerId <full resource ID for third-party solution>]
```

### Default Value

By default, Diagnostic AuditEvent logging is not enabled for Key Vault instances.
