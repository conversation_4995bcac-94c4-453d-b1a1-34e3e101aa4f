## Description

Create an activity log alert for Service Health.

Monitoring for Service Health events provides insight into service issues, planned maintenance, security advisories, and other changes that may affect the Azure services and regions in use.

## Remediation

### From Azure Portal

1. Go to `Monitor`.
2. Click `Alerts`.
3. Click `+ Create`.
4. Select `Alert rule` from the drop-down menu.
5. Choose a subscription.
6. <PERSON>lick `Apply`.
7. Select the `Condition` tab.
8. <PERSON>lick `See all signals`.
9. Select `Service health`.
10. <PERSON>lick `Apply`.
11. Open the drop-down menu next to `Event types`.
12. Check the box next to `Select all`.
13. Select the `Actions` tab.
14. Click `Select action groups` to select an existing action group, or `Create action group` to create a new action group.
15. Follow the prompts to choose or create an action group.
16. Select the `Details` tab.
17. Select a `Resource group`, provide an `Alert rule name` and an optional `Alert rule description`.
18. Click `Review + create`.
19. Click `Create`.
20. Repeat steps 1-19 for each subscription requiring remediation.

### From Azure CLI

For each subscription requiring remediation, run the following command to create a `ServiceHealth` alert rule for a subscription:

```bash
az monitor activity-log alert create --subscription <subscription-id> --resource-group <resource-group> --name <alert-rule> --condition category=ServiceHealth and properties.incidentType=Incident --scope /subscriptions/<subscription-id> --action-group <action-group>
```

### From PowerShell

Create the `Conditions` object:

```bash
$conditions = @()
$conditions += New-AzActivityLogAlertAlertRuleAnyOfOrLeafConditionObject -Field category -Equal ServiceHealth
$conditions += New-AzActivityLogAlertAlertRuleAnyOfOrLeafConditionObject -Field properties.incidentType -Equal Incident
```

Retrieve the `Action Group` information and store in a variable:

```bash
$actionGroup = Get-AzActionGroup -ResourceGroupName <resource-group> -Name <action-group>
```

Create the `Actions` object:

```bash
$actionObject = New-AzActivityLogAlertActionGroupObject -Id $actionGroup.Id
```

Create the `Scope` object:

```bash
$scope = "/subscriptions/<subscription-id>"
```

Create the activity log alert rule:

```bash
New-AzActivityLogAlert -Name <alert-rule> -ResourceGroupName <resource-group> -Condition $conditions -Scope $scope -Location global -Action $actionObject -Subscription <subscription-id> -Enabled $true
```

Repeat for each subscription requiring remediation.

### Default Value

By default, no monitoring alerts are created.
