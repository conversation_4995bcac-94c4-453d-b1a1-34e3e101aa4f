## Overview

This section covers security best practice recommendations for Storage Accounts in Azure.

The recommendations in this section apply to the Storage Account, but not to the Storage Services which may be running on that account. Use the Storage Account recommendations as a starting place for securing the account, then proceed to apply the recommendations from the storage services section(s) that are relevant to the storage services running on your account.

Storage Accounts are a family of account types that support different Storage Services. The Storage Account types and their supported services follow:

- **Standard general-purpose v2** supported services: Blob Storage (including Data Lake Storage), Queue Storage, Table Storage, and Azure Files.
- **Premium block blobs** supported services: Blob Storage (including Data Lake Storage)
- **Premium file shares** supported services: Azure Files
- **Premium page blobs** supported services: Page blobs only

**Help us improve this Benchmark!**
If you notice a needed correction, want to provide feedback, or wish to contribute security best practice guidance please join our community and create a ticket, propose a change, or start a discussion so we can improve this guidance!

The CIS Microsoft Azure Community is here: [https://workbench.cisecurity.org/communities/72](https://workbench.cisecurity.org/communities/72)

-----

### Resources for Storage Accounts

**Azure Product page**:
- [https://azure.microsoft.com/en-us/products/category/storage/](https://azure.microsoft.com/en-us/products/category/storage/)

**Azure Storage Account overview**:
- [https://learn.microsoft.com/en-us/azure/storage/common/storage-account-overview](https://learn.microsoft.com/en-us/azure/storage/common/storage-account-overview)

**Microsoft Cloud Security Baseline for Storage**:
- [https://learn.microsoft.com/en-us/security/benchmark/azure/baselines/storage-security-baseline](https://learn.microsoft.com/en-us/security/benchmark/azure/baselines/storage-security-baseline)
