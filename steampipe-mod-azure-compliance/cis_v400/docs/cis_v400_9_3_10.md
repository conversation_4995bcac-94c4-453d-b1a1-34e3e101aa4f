## Description

Azure Key Vault Managed HSM is a fully managed, highly available, single-tenant cloud service that safeguards cryptographic keys using FIPS 140-2 Level 3 validated HSMs.

**Note:** This recommendation to use Managed HSM applies only to scenarios where specific regulatory and compliance requirements mandate the use of a dedicated hardware security module.

Managed HSM is a fully managed, highly available, single-tenant service that ensures FIPS 140-2 Level 3 compliance. It provides centralized key management, isolated access control, and private endpoints for secure access. Integrated with Azure services, it supports migration from Key Vault, ensures data residency, and offers monitoring and auditing for enhanced security.

## Remediation

### From Azure CLI

Run the following command to set `oid` to be the `OID` of the signed-in user:

```bash
$oid = az ad signed-in-user show --query id -o tsv
```

Alternatively, prepare a space-separated list of OIDs to be provided as the `administrators` of the HSM.

Run the following command to create a Managed HSM:

```bash
az keyvault create --resource-group <resource-group> --hsm-name <hsm-name> --retention-days <retention-days> --administrators $oid
```

The command can take several minutes to complete.

After the HSM has been created, it must be activated before it can be used. Activation requires providing a minimum of three and a maximum of ten RSA key pairs, as well as the minimum number of keys required to decrypt the security domain (called a quorum).

OpenSSL can be used to generate the self-signed certificates, for example:

```bash
openssl req -newkey rsa:2048 -nodes -keyout cert_1.key -x509 -days 365 -out cert_1.cer
```

Run the following command to download the security domain and activate the Managed HSM:

```bash
az keyvault security-domain download --hsm-name <managed-hsm> --sd-wrapping-keys <key-1> <key-2> <key-3> --sd-quorum <quorum> --security-domain-file <managed-hsm-security-domain>.json
```

Store the security domain file and the RSA key pairs securely. They will be required for disaster recovery or for creating another Managed HSM that shares the same security domain so that the two can share keys.

The Managed HSM will now be in an active state and ready for use.
