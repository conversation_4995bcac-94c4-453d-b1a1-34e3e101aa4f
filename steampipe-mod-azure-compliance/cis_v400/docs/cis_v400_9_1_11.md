## Description

The Microsoft Cloud Security Benchmark (or "MCSB") is an Azure Policy Initiative containing many security policies to evaluate resource configuration against best practice recommendations. If a policy in the MCSB is set with effect type `Disabled`, it is not evaluated and may prevent administrators from being informed of valuable security recommendations.

A security policy defines the desired configuration of resources in your environment and helps ensure compliance with company or regulatory security requirements. The MCSB Policy Initiative a set of security recommendations based on best practices and is associated with every subscription by default. When a policy "Effect" is set to `Audit`, policies in the MCSB ensure that Defender for Cloud evaluates relevant resources for supported recommendations. To ensure that policies within the MCSB are not being missed when the Policy Initiative is evaluated, none of the policies should have an Effect of `Disabled`.

## Remediation

### From Azure Portal

1. From Azure Home select the Portal Menu.
2. Select `Microsoft Defender for Cloud`.
3. Under `Management`, select `Environment settings`.
4. <PERSON>lick on the appropriate Management Group or Subscription.
5. <PERSON>lick on `Security policies` in the left column.
6. <PERSON>lick on `Microsoft cloud security benchmark`.
7. <PERSON>lick `Add Filter` and select `Effect`.
8. Check the `Disabled` box to search for all disabled policies.
9. <PERSON>lick `Apply`.
10. Click the blue ellipsis `...` to the right of a policy name.
11. <PERSON>lick `Manage effect and parameters`.
12. Under `Policy effect`, select the radio button next to `Audit`.
13. <PERSON><PERSON> `Save`.
14. <PERSON>lick `Refresh`.
15. Repeat steps 10-14 until all disabled policies are updated.
16. Repeat steps 1-15 for each Management Group or Subscription requiring remediation.

### Default Value

By default, the MCSB policy initiative is assigned on all subscriptions, and **most** policies will have an effect of `Audit`. Some policies will have a default effect of `Disabled`.
