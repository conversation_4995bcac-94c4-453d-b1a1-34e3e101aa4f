#!/bin/bash
# Aggressive testing to find SQL errors

echo "Starting aggressive benchmark testing..."

# Test all AWS benchmarks
for benchmark in cis_v300 cis_v200 cis_v150 foundational_security nist_800_53_rev_5; do
    echo "Testing AWS $benchmark..."
    curl -s -X POST http://localhost:8082/api/aws/benchmark \
        -H "Content-Type: application/json" \
        -d "{\"secret_reference\": \"aws-local-secops\", \"account_id\": \"all-accounts\", \"benchmark\": \"$benchmark\"}" &
done

# Test all Azure benchmarks
for benchmark in cis_v210 cis_v200 cis_v130 hipaa_hitrust_v92 nist_sp_800_53_rev_5; do
    echo "Testing Azure $benchmark..."
    curl -s -X POST http://localhost:8083/api/azure/benchmark \
        -H "Content-Type: application/json" \
        -d "{\"secret_reference\": \"azure-local-secops\", \"subscription_id\": \"all-subscriptions\", \"benchmark\": \"$benchmark\"}" &
done

# Test all GCP benchmarks  
for benchmark in cis_v300 cis_v200 cis_v120 nist_800_53_rev_5 nist_csf; do
    echo "Testing GCP $benchmark..."
    curl -s -X POST http://localhost:8080/api/gcp/benchmark \
        -H "Content-Type: application/json" \
        -d "{\"secret_reference\": \"gcp-local-secops\", \"project_id\": \"all-projects\", \"benchmark\": \"$benchmark\"}" &
done

wait
echo "All benchmarks submitted"

# Wait and check for errors
sleep 60

echo -e "\nChecking for errors in AWS jobs..."
curl -s http://localhost:8082/api/aws/jobs | jq -r '.jobs[] | select(.error != null) | "\(.benchmark): \(.error)"' || true

echo -e "\nChecking for errors in Azure jobs..."
curl -s http://localhost:8083/api/azure/jobs | jq -r '.jobs[] | select(.error != null) | "\(.benchmark): \(.error)"' || true

echo -e "\nChecking for errors in GCP jobs..."
curl -s http://localhost:8080/api/gcp/jobs | jq -r '.jobs[] | select(.error != null) | "\(.benchmark): \(.error)"' || true