#!/bin/bash

# Deploy all services to Cloud Run
# Get the latest commit SHA for tagging
COMMIT_SHA=$(git rev-parse --short HEAD)

# Deploy Admin API
gcloud run deploy admin-api \
  --image gcr.io/vratant-test-prj/admin-api:${COMMIT_SHA} \
  --region us-central1 \
  --platform managed \
  --allow-unauthenticated \
  --memory 2Gi \
  --cpu 2 \
  --port 8085 \
  --set-env-vars="PROJECT_ID=vratant-test-prj,DOMAIN=steampipe-compliance.com" \
  --min-instances 1 \
  --max-instances 10 \
  --timeout 300s

# Deploy GCP compliance service
gcloud run deploy steampipe-gcp-compliance \
  --image gcr.io/vratant-test-prj/steampipe-gcp-compliance:${COMMIT_SHA} \
  --region us-central1 \
  --platform managed \
  --no-allow-unauthenticated \
  --memory 8Gi \
  --cpu 4 \
  --port 8080 \
  --set-env-vars="AUTH_TYPE=adc,PROJECT_ID=vratant-test-prj,STEAMPIPE_UPDATE_CHECK=false,POWERPIPE_UPDATE_CHECK=false,STEAMPIPE_DATABASE_PASSWORD=steampipe,STEAMPIPE_INIT_TIMEOUT=300,STEAMPIPE_TIMEOUT=600s,SERVICE_TYPE=gcp" \
  --min-instances 1 \
  --max-instances 5 \
  --timeout 3600s \
  --concurrency 5

# Deploy AWS compliance service  
gcloud run deploy steampipe-aws-compliance \
  --image gcr.io/vratant-test-prj/steampipe-aws-compliance:${COMMIT_SHA} \
  --region us-central1 \
  --platform managed \
  --no-allow-unauthenticated \
  --memory 8Gi \
  --cpu 4 \
  --port 8082 \
  --set-env-vars="AUTH_TYPE=secret_manager,PROJECT_ID=vratant-test-prj,STEAMPIPE_UPDATE_CHECK=false,POWERPIPE_UPDATE_CHECK=false,STEAMPIPE_DATABASE_PASSWORD=steampipe,STEAMPIPE_INIT_TIMEOUT=300,STEAMPIPE_TIMEOUT=600s,SERVICE_TYPE=aws,AWS_REGION=us-east-1" \
  --min-instances 1 \
  --max-instances 5 \
  --timeout 3600s \
  --concurrency 5

# Deploy Azure compliance service
gcloud run deploy steampipe-azure-compliance \
  --image gcr.io/vratant-test-prj/steampipe-azure-compliance:${COMMIT_SHA} \
  --region us-central1 \
  --platform managed \
  --no-allow-unauthenticated \
  --memory 8Gi \
  --cpu 4 \
  --port 8083 \
  --set-env-vars="AUTH_TYPE=secret_manager,PROJECT_ID=vratant-test-prj,STEAMPIPE_UPDATE_CHECK=false,POWERPIPE_UPDATE_CHECK=false,STEAMPIPE_DATABASE_PASSWORD=steampipe,STEAMPIPE_INIT_TIMEOUT=300,STEAMPIPE_TIMEOUT=600s,SERVICE_TYPE=azure" \
  --min-instances 1 \
  --max-instances 5 \
  --timeout 3600s \
  --concurrency 5

# Get backend service URLs
echo "Getting backend service URLs..."
GCP_URL=$(gcloud run services describe steampipe-gcp-compliance --region us-central1 --format='value(status.url)')
AWS_URL=$(gcloud run services describe steampipe-aws-compliance --region us-central1 --format='value(status.url)')
AZURE_URL=$(gcloud run services describe steampipe-azure-compliance --region us-central1 --format='value(status.url)')

# Deploy API Gateway with backend URLs
gcloud run deploy api-gateway \
  --image gcr.io/vratant-test-prj/api-gateway:${COMMIT_SHA} \
  --region us-central1 \
  --platform managed \
  --allow-unauthenticated \
  --memory 2Gi \
  --cpu 2 \
  --port 8081 \
  --set-env-vars="GCP_BACKEND_URL=${GCP_URL},AWS_BACKEND_URL=${AWS_URL},AZURE_BACKEND_URL=${AZURE_URL}" \
  --min-instances 1 \
  --max-instances 10 \
  --timeout 300s

# Deploy Dashboard UI
gcloud run deploy dashboard-ui \
  --image gcr.io/vratant-test-prj/dashboard-ui:${COMMIT_SHA} \
  --region us-central1 \
  --platform managed \
  --allow-unauthenticated \
  --memory 1Gi \
  --cpu 1 \
  --port 8081 \
  --set-env-vars="BACKEND_GCP_URL=${GCP_URL},BACKEND_AWS_URL=${AWS_URL},BACKEND_AZURE_URL=${AZURE_URL}" \
  --min-instances 1 \
  --max-instances 10

echo "Deployment complete!"
echo "Dashboard URL: $(gcloud run services describe dashboard-ui --region us-central1 --format='value(status.url)')"