
# 🛡️ Cloud Compliance Benchmark Execution – API Integration Guide

This document outlines how clients can securely onboard and execute cloud compliance benchmark tests via our API using credentials stored in Google Cloud Secret Manager.

---

## 🔐 Step 1: Customer Credential Storage

1. We provide you a **GCP Service Account Key** with **access to our Secret Manager**.
2. You use this key to **store credentials** for your customer in the following format inside Secret Manager:
   ```
   <customer1-gcp-key>
   <customer1-aws-key>
   <customer1-azure-key>
   ```

---

## 🚀 Step 2: Triggering a Benchmark

To start a benchmark, call the following **asynchronous API endpoint** depending on the cloud provider:

- `/api/gcp/run-benchmark-async`
- `/api/aws/run-benchmark-async`
- `/api/azure/run-benchmark-async`

### Example `curl` Request (for GCP)

```bash
curl -X POST https://steampipe-gcp-compliance-************.us-central1.run.app/api/gcp/run-benchmark-async \
-H "Content-Type: application/json" \
-d '{
  "benchmark": "all_controls",
  "project_id": "vratant-test-prj",
  "secret_manager_name": "customer1-gcp-key"
}'
```

#### Payload Fields:
- `benchmark`: Name of the benchmark (e.g., `all_controls`)
- `project_id`: GCP Project ID
- `secret_manager_name`: Name of the secret in Secret Manager that contains customer credentials

---

## 📬 Step 3: API Response (Benchmark Trigger)

On a successful trigger, you will receive a response like:

```json
{
  "check_status_url": "/api/gcp/job/a1bf4b52-5071-46a5-9640-bd5327e1727e/status",
  "get_result_url": "/api/gcp/job/a1bf4b52-5071-46a5-9640-bd5327e1727e/result",
  "job_id": "a1bf4b52-5071-46a5-9640-bd5327e1727e",
  "message": "Benchmark job started",
  "status": "success"
}
```

---

## 📊 Step 4: Monitor Benchmark Status

Use the `job_id` to check the status of your benchmark.

### Status API (for GCP)

```bash
GET https://steampipe-gcp-compliance-************.us-central1.run.app/api/gcp/job/<job_id>/status
```

### Example Response

```json
{
  "benchmark": "all_controls",
  "cloud_provider": "gcp",
  "completed_at": "2025-07-25T13:51:46.460174",
  "created_at": "2025-07-25T13:48:54.687484",
  "error": "Steampipe service error: Failed to start Steampipe service after all attempts including reset",
  "id": "a1bf4b52-5071-46a5-9640-bd5327e1727e",
  "progress": 0,
  "project_id": "vratant-test-prj",
  "result_file": null,
  "started_at": null,
  "status": "failed"
}
```

### Possible `status` values:
- `pending`
- `running`
- `success`
- `failed`

---

## 📥 Step 5: Fetch Benchmark Result

Once the benchmark job is complete, retrieve the final result using:

### Result API (for GCP)

```bash
GET https://steampipe-gcp-compliance-************.us-central1.run.app/api/gcp/job/<job_id>/result
```

🔁 Similar `/status` and `/result` endpoints exist for:
- `/api/aws/job/<job_id>/status` and `/result`
- `/api/azure/job/<job_id>/status` and `/result`

---

## 📦 Summary: Customer Onboarding & API Workflow

| Step | Action |
|------|--------|
| 1️⃣ | We provide **Service Account Key** with access to Secret Manager |
| 2️⃣ | You store customer cloud credentials using the key: <br> `customer1-aws-key`, `customer1-gcp-key`, etc. |
| 3️⃣ | Use the appropriate API (`/aws`, `/gcp`, `/azure`) and pass the secret key name |
| 4️⃣ | Benchmark job starts, and you receive a `job_id` |
| 5️⃣ | Use `job_id` to check job status and fetch results |
