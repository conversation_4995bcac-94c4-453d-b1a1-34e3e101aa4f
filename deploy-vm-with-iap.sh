#!/bin/bash

# VM Deployment Script with IAP and Load Balancer
# This script deploys steampipe compliance dashboard on a VM behind IAP

set -e

# Configuration
PROJECT_ID="${PROJECT_ID:-vratant-test-prj}"
REGION="us-central1"
ZONE="us-central1-a"
VM_NAME="steampipe-compliance-vm"
INSTANCE_GROUP_NAME="steampipe-compliance-ig"
BACKEND_SERVICE_NAME="steampipe-compliance-backend"
URL_MAP_NAME="steampipe-compliance-lb"
HTTPS_PROXY_NAME="steampipe-compliance-https-proxy"
FORWARDING_RULE_NAME="steampipe-compliance-forwarding-rule"
SSL_CERT_NAME="steampipe-compliance-cert"
HEALTH_CHECK_NAME="steampipe-compliance-health-check"
FIREWALL_RULE_NAME="allow-health-check"
FIREWALL_IAP_RULE="allow-iap-ssh"
NETWORK_TAG="steampipe-compliance"
SERVICE_ACCOUNT="steampipe-sa@${PROJECT_ID}.iam.gserviceaccount.com"

# IAP Configuration
IAP_CLIENT_ID="${IAP_CLIENT_ID:-}"
IAP_CLIENT_SECRET="${IAP_CLIENT_SECRET:-}"

echo "=== Steampipe VM Deployment with IAP and Load Balancer ==="
echo "Project: ${PROJECT_ID}"
echo "Region: ${REGION}"
echo "Zone: ${ZONE}"

# Check if required tools are installed
if ! command -v gcloud &> /dev/null; then
    echo "Error: gcloud CLI is not installed"
    exit 1
fi

# Set project
echo "Setting project..."
gcloud config set project ${PROJECT_ID}

# Create service account if it doesn't exist
echo "Creating service account..."
gcloud iam service-accounts create steampipe-sa \
    --display-name="Steampipe Service Account" \
    --description="Service account for Steampipe compliance VM" \
    2>/dev/null || echo "Service account already exists"

# Grant necessary permissions
echo "Granting permissions to service account..."
for role in \
    "roles/storage.objectViewer" \
    "roles/secretmanager.secretAccessor" \
    "roles/logging.logWriter" \
    "roles/monitoring.metricWriter"
do
    gcloud projects add-iam-policy-binding ${PROJECT_ID} \
        --member="serviceAccount:${SERVICE_ACCOUNT}" \
        --role="${role}" \
        --quiet
done

# Create firewall rules for health checks
echo "Creating firewall rules..."
gcloud compute firewall-rules create ${FIREWALL_RULE_NAME} \
    --network=default \
    --action=allow \
    --direction=ingress \
    --source-ranges=***********/22,**********/16 \
    --target-tags=${NETWORK_TAG} \
    --rules=tcp:8080,tcp:8081,tcp:8082,tcp:8083,tcp:8085 \
    2>/dev/null || echo "Firewall rule already exists"

# Create firewall rule for IAP SSH access
gcloud compute firewall-rules create ${FIREWALL_IAP_RULE} \
    --network=default \
    --action=allow \
    --direction=ingress \
    --source-ranges=************/20 \
    --target-tags=${NETWORK_TAG} \
    --rules=tcp:22 \
    2>/dev/null || echo "IAP SSH firewall rule already exists"

# Create VM instance
echo "Creating VM instance..."
gcloud compute instances create ${VM_NAME} \
    --zone=${ZONE} \
    --machine-type=e2-standard-4 \
    --network-interface=network-tier=PREMIUM,subnet=default,no-address \
    --metadata=startup-script='#!/bin/bash
# Update system
apt-get update
apt-get install -y docker.io docker-compose

# Configure Docker
systemctl enable docker
systemctl start docker

# Add user to docker group
usermod -aG docker $(whoami)

# Create directory for docker-compose
mkdir -p /opt/steampipe-compliance



# Create docker-compose.yml with all services including Azure
cat > /opt/steampipe-compliance/docker-compose.yml <<EOF
version: "3.8"

networks:
  steampipe-network:
    driver: bridge

volumes:
  azure-steampipe-config:
  azure-steampipe-internal:
  azure-steampipe-logs:
  azure-steampipe-db:
  azure-steampipe-plugins:
  aws-steampipe-config:
  aws-steampipe-internal:
  aws-steampipe-logs:
  aws-steampipe-db:
  aws-steampipe-plugins:
  gcp-steampipe-config:
  gcp-steampipe-internal:
  gcp-steampipe-logs:
  gcp-steampipe-db:
  gcp-steampipe-plugins:

services:
  aws-compliance:
    image: gcr.io/${PROJECT_ID}/steampipe-aws-compliance:latest
    ports:
      - "8082:8082"
    environment:
      - AUTH_TYPE=secret_manager
      - PROJECT_ID=${PROJECT_ID}
      - STEAMPIPE_UPDATE_CHECK=false
      - POWERPIPE_UPDATE_CHECK=false
      - STEAMPIPE_DATABASE_PASSWORD=steampipe
      - STEAMPIPE_INIT_TIMEOUT=300
      - STEAMPIPE_TIMEOUT=600s
      - SERVICE_TYPE=aws
      - AWS_REGION=us-east-1
      - SKIP_IAP_CHECK=true
    volumes:
      - aws-steampipe-config:/home/<USER>/.steampipe/config
      - aws-steampipe-internal:/home/<USER>/.steampipe/internal
      - aws-steampipe-logs:/home/<USER>/.steampipe/logs
      - aws-steampipe-db:/home/<USER>/.steampipe/db
      - aws-steampipe-plugins:/home/<USER>/.steampipe/plugins
    networks:
      - steampipe-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8082/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  azure-compliance:
    image: gcr.io/${PROJECT_ID}/steampipe-azure-compliance:latest
    ports:
      - "8083:8083"
    environment:
      - AUTH_TYPE=adc
      - PROJECT_ID=${PROJECT_ID}
      - STEAMPIPE_UPDATE_CHECK=false
      - POWERPIPE_UPDATE_CHECK=false
      - STEAMPIPE_DATABASE_PASSWORD=steampipe
      - STEAMPIPE_INIT_TIMEOUT=300
      - STEAMPIPE_TIMEOUT=600s
      - SERVICE_TYPE=azure
      - SKIP_IAP_CHECK=true
    volumes:
      - azure-steampipe-config:/home/<USER>/.steampipe/config
      - azure-steampipe-internal:/home/<USER>/.steampipe/internal
      - azure-steampipe-logs:/home/<USER>/.steampipe/logs
      - azure-steampipe-db:/home/<USER>/.steampipe/db
      - azure-steampipe-plugins:/home/<USER>/.steampipe/plugins

    networks:
      - steampipe-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8083/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  gcp-compliance:
    image: gcr.io/${PROJECT_ID}/steampipe-gcp-compliance:latest
    ports:
      - "8080:8080"
    environment:
      - AUTH_TYPE=adc
      - PROJECT_ID=${PROJECT_ID}
      - STEAMPIPE_UPDATE_CHECK=false
      - POWERPIPE_UPDATE_CHECK=false
      - STEAMPIPE_DATABASE_PASSWORD=steampipe
      - STEAMPIPE_INIT_TIMEOUT=300
      - STEAMPIPE_TIMEOUT=600s
      - SERVICE_TYPE=gcp
      - SKIP_IAP_CHECK=true
    volumes:
      - gcp-steampipe-config:/home/<USER>/.steampipe/config
      - gcp-steampipe-internal:/home/<USER>/.steampipe/internal
      - gcp-steampipe-logs:/home/<USER>/.steampipe/logs
      - gcp-steampipe-db:/home/<USER>/.steampipe/db
      - gcp-steampipe-plugins:/home/<USER>/.steampipe/plugins
    networks:
      - steampipe-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  api-gateway:
    image: gcr.io/${PROJECT_ID}/api-gateway:latest
    ports:
      - "8081:8081"
    environment:
      - GCP_BACKEND_URL=http://gcp-compliance:8080
      - AWS_BACKEND_URL=http://aws-compliance:8082
      - AZURE_BACKEND_URL=http://azure-compliance:8083
    depends_on:
      - aws-compliance
      - azure-compliance
      - gcp-compliance
    networks:
      - steampipe-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  dashboard-ui:
    image: gcr.io/${PROJECT_ID}/dashboard-ui:latest
    ports:
      - "80:8081"
    environment:
      - BACKEND_GCP_URL=http://gcp-compliance:8080
      - BACKEND_AWS_URL=http://aws-compliance:8082
      - BACKEND_AZURE_URL=http://azure-compliance:8083

    depends_on:
      - api-gateway
    networks:
      - steampipe-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/health"]
      interval: 30s
      timeout: 10s
      retries: 3
EOF

# Main directory already created above

# Configure Docker to use GCR
gcloud auth configure-docker gcr.io --quiet

# Start services
cd /opt/steampipe-compliance
docker-compose pull
docker-compose up -d

# Create systemd service
cat > /etc/systemd/system/steampipe-compliance.service <<EOF
[Unit]
Description=Steampipe Compliance Dashboard
Requires=docker.service
After=docker.service

[Service]
Type=simple
WorkingDirectory=/opt/steampipe-compliance
ExecStart=/usr/bin/docker-compose up
ExecStop=/usr/bin/docker-compose down
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

systemctl enable steampipe-compliance.service
systemctl start steampipe-compliance.service
' \
    --maintenance-policy=MIGRATE \
    --provisioning-model=STANDARD \
    --service-account=${SERVICE_ACCOUNT} \
    --scopes=https://www.googleapis.com/auth/cloud-platform \
    --tags=${NETWORK_TAG} \
    --create-disk=auto-delete=yes,boot=yes,device-name=${VM_NAME},image=projects/ubuntu-os-cloud/global/images/ubuntu-2204-jammy-v20240319,mode=rw,size=100,type=pd-standard \
    --no-shielded-secure-boot \
    --shielded-vtpm \
    --shielded-integrity-monitoring \
    --labels=purpose=steampipe-compliance \
    --reservation-affinity=any

echo "Waiting for VM to be ready..."
sleep 60

# Create instance group
echo "Creating instance group..."
gcloud compute instance-groups unmanaged create ${INSTANCE_GROUP_NAME} \
    --zone=${ZONE} \
    2>/dev/null || echo "Instance group already exists"

# Add VM to instance group
gcloud compute instance-groups unmanaged add-instances ${INSTANCE_GROUP_NAME} \
    --zone=${ZONE} \
    --instances=${VM_NAME}

# Create health check
echo "Creating health check..."
gcloud compute health-checks create http ${HEALTH_CHECK_NAME} \
    --port=80 \
    --request-path=/health \
    --check-interval=30s \
    --timeout=10s \
    --unhealthy-threshold=3 \
    --healthy-threshold=2 \
    2>/dev/null || echo "Health check already exists"

# Create backend service
echo "Creating backend service..."
gcloud compute backend-services create ${BACKEND_SERVICE_NAME} \
    --protocol=HTTP \
    --port-name=http \
    --health-checks=${HEALTH_CHECK_NAME} \
    --global \
    --iap=enabled \
    2>/dev/null || echo "Backend service already exists"

# Add backend to backend service
gcloud compute backend-services add-backend ${BACKEND_SERVICE_NAME} \
    --instance-group=${INSTANCE_GROUP_NAME} \
    --instance-group-zone=${ZONE} \
    --global

# Set named port on instance group
gcloud compute instance-groups unmanaged set-named-ports ${INSTANCE_GROUP_NAME} \
    --zone=${ZONE} \
    --named-ports=http:80

# Create URL map
echo "Creating URL map..."
gcloud compute url-maps create ${URL_MAP_NAME} \
    --default-service=${BACKEND_SERVICE_NAME} \
    2>/dev/null || echo "URL map already exists"

# Create SSL certificate (self-signed for testing)
echo "Creating SSL certificate..."
gcloud compute ssl-certificates create ${SSL_CERT_NAME} \
    --domains=steampipe-compliance.${PROJECT_ID}.appspot.com \
    --global \
    2>/dev/null || echo "SSL certificate already exists"

# Create HTTPS proxy
echo "Creating HTTPS proxy..."
gcloud compute target-https-proxies create ${HTTPS_PROXY_NAME} \
    --url-map=${URL_MAP_NAME} \
    --ssl-certificates=${SSL_CERT_NAME} \
    --global \
    2>/dev/null || echo "HTTPS proxy already exists"

# Create forwarding rule
echo "Creating forwarding rule..."
gcloud compute forwarding-rules create ${FORWARDING_RULE_NAME} \
    --address=ephemeral \
    --target-https-proxy=${HTTPS_PROXY_NAME} \
    --ports=443 \
    --global

# Configure IAP
if [ -n "${IAP_CLIENT_ID}" ] && [ -n "${IAP_CLIENT_SECRET}" ]; then
    echo "Configuring IAP..."
    gcloud iap web enable \
        --resource-type=backend-services \
        --oauth2-client-id=${IAP_CLIENT_ID} \
        --oauth2-client-secret=${IAP_CLIENT_SECRET} \
        --service=${BACKEND_SERVICE_NAME}
else
    echo "IAP client ID and secret not provided. Skipping IAP configuration."
    echo "To enable IAP, set IAP_CLIENT_ID and IAP_CLIENT_SECRET environment variables."
fi

# Get the external IP
echo "Getting load balancer IP..."
LB_IP=$(gcloud compute forwarding-rules describe ${FORWARDING_RULE_NAME} --global --format="value(IPAddress)")

echo ""
echo "=== Deployment Complete ==="
echo "Load Balancer IP: ${LB_IP}"
echo "Dashboard URL: https://${LB_IP}/"
echo ""
echo "To access the VM via SSH through IAP:"
echo "gcloud compute ssh ${VM_NAME} --zone=${ZONE} --tunnel-through-iap"
echo ""
echo "To check service status on VM:"
echo "gcloud compute ssh ${VM_NAME} --zone=${ZONE} --tunnel-through-iap --command='sudo docker ps'"
echo ""
echo "To view logs:"
echo "gcloud compute ssh ${VM_NAME} --zone=${ZONE} --tunnel-through-iap --command='sudo docker-compose -f /opt/steampipe-compliance/docker-compose.yml logs -f'"
echo ""
echo "Note: It may take 5-10 minutes for the load balancer to be fully operational."
echo "Note: The SSL certificate warning is expected for self-signed certificates."